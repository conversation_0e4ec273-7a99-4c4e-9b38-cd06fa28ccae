/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.message.pojo;

import com.chinamobile.cmss.ops.monitor.cockpit.common.constant.MessageConstant;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * ShortMessageResponse 短信发送响应
 */
@Data
@Slf4j
public class ShortMessageResponse {
    
    /**
     * 结果编码
     */
    private String code;
    
    /**
     * 结果描述
     */
    private String msg;
    
    /**
     * validateShortMessageResponse
     */
    public void validateShortMessageResponse() {
        
        if (!MessageConstant.Message.SUCCESS_CODE.equals(this.getCode())) {
            log.error("调用短信接口失败，message:{}", this.getMsg());
        } else {
            log.info("调用短信接口成功，message:{}", this.getMsg());
        }
    }
}
