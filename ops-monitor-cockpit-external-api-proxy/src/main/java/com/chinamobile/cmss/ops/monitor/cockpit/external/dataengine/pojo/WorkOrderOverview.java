/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class WorkOrderOverview {
    
    /**
     * 工单号
     */
    @JsonProperty("cell1")
    private String workOrderNo;
    
    /**
     * 工单标题
     */
    @JsonProperty("cell14")
    private String workOrderTitle;
    
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("create_time")
    private Date createTime;
    
    /**
     * 处理时限
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("dealtime")
    private Date dealTime;
    
    /**
     * 故障等级/事件等级
     */
    @JsonProperty("fault_level")
    private String faultLevel;
    
    /**
     * 二线处理人姓名
     */
    @JsonProperty("processor")
    private String processor;
    
    /**
     * 二线处理人手机号
     */
    @JsonProperty("processor_phone")
    private String processorPhone;
    
    /**
     * 资源池
     */
    @JsonProperty("resource_pool")
    private String resourcePool;
    
    /**
     * 状态
     */
    @JsonProperty("state")
    private String state;
    
}
