/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FuzzySearchView implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 历史、活动告警0/1
     */
    private int status;
    
    /**
     * 未确认、已确认/0/1
     */
    private Integer ackStatus;
    
    /**
     * 是否是工程告警 0/1
     */
    private int isProject;
    
    /**
     * 输入框内容
     */
    private String searchKey;
    
    // private PageDto pageDto;
    
    /**
     * 时间转为索引，首次发生时间
     */
    private String[] firstOccurrenceTime;
    
    /**
     * 最后发生时间
     */
    private String[] lastOccurrenceTime;
    
    /**
     * 高级过滤器属性间过滤方式，此字段已废弃
     */
    private String relationOp;
    
    /**
     * 高级过滤器
     */
    private List<PropertyFilter> propertyFilterList;
    
    /**
     * 查询其它告警标识，1为其他告警
     */
    private int otherStatus;
    
    /**
     * 屏蔽告警
     */
    private int shieldStatus;
    
    /**
     * 是否是简单规则,1复杂，传空为简单
     */
    private int isSimple;
    
    /**
     * 简单规则方式
     */
    private String simpleRelationOp;
    
    /**
     * 复杂规则方式
     */
    private String complexRelationOp;
    
    /**
     * 是否统一接口:统一/非统一，1/0
     */
    private int isUnique;
    
    /**
     * 是否标注好:标准化/未标准化，1/0
     */
    private int isStandard;
    
    /**
     * 告警清除时间
     */
    private String[] clearTime;
    
    /**
     * 告警ID列表
     */
    private List<String> alarmIdList;
    
    private List<String> selectedColumns;
    
    public String getSearchKey() {
        return searchKey == null ? null : searchKey.replace("'", "''");
    }
    
    @Data
    public static class PropertyFilter implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 属性英文名称
         */
        private String propertyName;
        
        /**
         * 属性中文名称
         */
        private String propertyLabel;
        
        /**
         * 字典值
         */
        private String renderValue;
        
        /**
         * 符号
         */
        private String op;
        
        /**
         * 对应字典名称
         */
        private String dictName;
        
        /**
         * 类型字符串或枚举
         */
        private String renderType;
        
        public String getRenderValue() {
            return renderValue == null ? null : renderValue.replace("'", "''");
        }
    }
}
