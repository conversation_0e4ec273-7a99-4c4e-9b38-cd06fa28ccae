/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

/**
 * Cmdb Base Args
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class CmdbBaseArgs {
    
    /**
     * 当前用户登录态，bk_token与bk_username必须一个有效，bk_token可以通过Cookie获取
     */
    @JsonProperty("bk_token")
    private String bkToken;
    
    /**
     * 当前用户用户名，应用免登录态验证白名单中的应用，用此字段指定当前用户
     */
    @JsonProperty("bk_username")
    private String bkUsername;
    
    /**
     * 查询条件
     */
    @JsonProperty("page")
    private Page page;
    
    /**
     * 应用ID
     */
    @JsonProperty("bk_app_code")
    private String bkAppCode;
    
    /**
     * 安全密钥(应用 TOKEN)，可以通过 蓝鲸智云开发者中心 -> 点击应用ID -> 基本信息 获取
     */
    @JsonProperty("bk_app_secret")
    private String bkAppSecret;
    
    /**
     * bkSupplierAccount
     */
    @JsonProperty("bk_supplier_account")
    private String bkSupplierAccount = "0";
    
    /**
     * buildCmdbProperties
     * @param bkAppCode bkAppCode
     * @param bkAppSecret bkAppSecret
     * @param bkUsername bkUsername
     */
    public void buildCmdbProperties(String bkAppCode, String bkAppSecret, String bkUsername) {
        this.setBkAppCode(bkAppCode);
        
        this.setBkAppSecret(bkAppSecret);
        
        if (StringUtils.isNotBlank(bkUsername)) {
            this.setBkUsername(bkUsername);
            this.setBkToken(null);
        }
    }
    
    @Data
    @Builder
    @AllArgsConstructor
    public static class Page {
        
        /**
         * 记录开始位置
         */
        private Integer start;
        
        /**
         * 每页限制条数,最大500
         */
        private Integer limit;
        
        /**
         * 排序字段
         */
        private String sort;
        
    }
    
}
