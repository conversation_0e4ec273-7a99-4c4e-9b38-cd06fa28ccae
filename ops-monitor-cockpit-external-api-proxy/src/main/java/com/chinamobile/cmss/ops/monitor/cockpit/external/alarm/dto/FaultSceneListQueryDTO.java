/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FaultSceneListQueryDTO {
    
    /**
     * 页号
     */
    private Integer page;
    
    /**
     * 每页数目
     */
    private Integer size;
    
    /**
     * 故障场景名称（作战室名称）
     */
    private String faultSceneName;
    
    /**
     * 故障级别
     */
    private Integer faultSceneGrade;
    
    /**
     * 作战室编号
     */
    private String faultSceneNo;
    
    /**
     * 故障状态
     */
    private Integer faultSceneStatus;
    
    /**
     * 故障场景创建开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createStartTime;
    
    /**
     * 故障场景创建结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createEndTime;
    
    /**
     * 故障场景创建人
     */
    private String creator;
    
    /**
     * foc故障id
     */
    private String focFaultId;
    
    /**
     * 关联工单号
     */
    private String relatedOrderNo;
    
    /**
     * 是否包含删除，默认false，不包含
     */
    private Boolean includeDeletion;
}
