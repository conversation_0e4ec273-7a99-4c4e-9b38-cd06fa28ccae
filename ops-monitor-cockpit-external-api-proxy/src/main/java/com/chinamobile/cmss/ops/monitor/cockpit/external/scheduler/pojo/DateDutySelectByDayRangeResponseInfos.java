/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DateDutySelectByDayRangeResponseInfos {
    
    /**
     * 班次类型: 1
     */
    private Integer shiftType;
    
    /**
     * 班次时间
     */
    private List<DutyConfigShiftTimeResponseVO> shiftTime;
    
    /**
     * @field 日期排班信息
     */
    private List<DateDutyBodyResponseVO> tbody;
    
    @Data
    public static class DutyConfigShiftTimeResponseVO {
        
        /**
         * 班次类型： 7c5123a-ddc3-42bb-b54c-3406cc41c24b
         */
        private String code;
        
        /**
         * 班次开始时间 00:00:0
         */
        private String startTime;
        
        /**
         * 班次结束时间 24:00:00
         */
        private String endTime;
    }
    
    @Data
    public static class DateDutyBodyResponseVO {
        
        /**
         * @field 值班日期 2020-09-10
         */
        private String date;
        
        /**
         * 人员角色Map
         */
        private Map<String, List<DateDutyUserResponseVO>> dutyMap;
        
        @Data
        public static class DateDutyUserResponseVO {
            
            /**
             * 监控人名 张三
             */
            private String fullName;
            
            /**
             * @field 手机号 18893839988
             */
            private String phone;
            
            /**
             * 值班角色类型 1
             */
            private Integer roleType;
            
            /**
             * @field 班次id(值班角色代码) f0727004-ae49-4f82-87ac-92600481d10c
             */
            private String configId;
        }
    }
    
}
