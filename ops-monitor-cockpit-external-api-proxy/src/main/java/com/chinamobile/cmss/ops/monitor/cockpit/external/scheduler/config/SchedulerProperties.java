/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: lingmeng
 * @Date: 2025/04/10 11:17
 * @Description:
 * @Version: 1.0
 */
@ConfigurationProperties(prefix = "ops.monitor.cockpit.scheduler")
@Data
public class SchedulerProperties {
    
    /**
     * 故障调度 配置
     */
    private String baseUrl;
    
    /**
     * 故障调度活动作战室列表 url
     */
    private String faultSceneListRemoteUrl =
            "/api/scheduler/extsys/v1/ext/monitor/cockpit/querySceneListForCockpit";
    
    /**
     * 故障调度消息审批任务数目 url
     */
    private String msgAuditTaskNumRemoteUrl =
            "/api/scheduler/configuration/v1/ext/message/audit/queryAuditTaskNum";
    
    /**
     * 依据日期范围查询日期排班数据url
     */
    private String selectByDayRangeUrl = "/api/scheduler/configuration/v1/pc/date/duty/selectByDayRange";
    
    /**
     * 查询排版列表
     */
    private String invokeByDutySettingUrl = "/api/scheduler/configuration/v1/pc/duty/template/queryList/invokeByDutySetting";
    
    private String queryAESGCMBase64KeyUrl = "/api/scheduler/configuration/v1/pc/net/interact/query/auth";
}
