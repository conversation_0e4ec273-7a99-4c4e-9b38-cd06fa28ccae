/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 告警系统配置属性
 */
@ConfigurationProperties(prefix = "ops.monitor.cockpit.alarm")
@Data
public class AlarmProperties {
    
    /**
     * 告警系统基础URL
     */
    private String baseUrl;
    
    private String majorAlarmCount = "/api/fms/search/cockpit/type/filter/statistic";
    
    private String alarmLevelFilterStatistic = "/api/fms/search/cockpit/level/filter/statistic";
    
    private String queryFaultSceneListUrl = "/api/scheduler/procedure/v1/pc/fault/scene/queryFaultSceneList/invokeByActiveFault";
    
    private String getAlarmNumUrl = "/api/fms/search/active/level/statistic";
}
