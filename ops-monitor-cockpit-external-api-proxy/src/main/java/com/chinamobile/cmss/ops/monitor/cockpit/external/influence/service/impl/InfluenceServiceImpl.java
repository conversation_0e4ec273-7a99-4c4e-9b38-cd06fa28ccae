/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.influence.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.UserUtils;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.config.InfluenceProperties;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.pojo.FaultAggregateBoardInfo;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.pojo.InfluenceResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.service.InfluenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

/**
 * @author: lingmeng
 * @Date: 2025/04/10 13:49
 * @Description: 影响面第三方调用实现
 * @Version: 1.0
 **/
@Service
@Slf4j
public class InfluenceServiceImpl implements InfluenceService {
    
    public final RestClient restClient;
    
    private final InfluenceProperties influenceProperties;
    
    public InfluenceServiceImpl(@Qualifier("influenceRestClient") RestClient restClient, InfluenceProperties influenceProperties) {
        this.restClient = restClient;
        this.influenceProperties = influenceProperties;
    }
    
    /**
     * 获取工单的故障等级统计信息
     * @return FaultAggregateBoardInfo
     **/
    @Override
    public InfluenceResponse<FaultAggregateBoardInfo> queryFaultAggregateBoardInfo() {
        
        log.info("请求影响面中的工单故障等级统计信息");
        String url = influenceProperties.getBaseUrl() + influenceProperties.getFaultLevelStatisticsRemoteUrl();
        InfluenceResponse<FaultAggregateBoardInfo> influenceResponse = restClient.get()
                .uri(url)
                .header("Cookie", "bk_token=" + UserUtils.getToken())
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        influenceResponse.validateInfluenceResponse();
        return influenceResponse;
        
    }
}
