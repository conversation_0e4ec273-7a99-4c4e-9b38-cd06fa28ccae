/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo;

import com.chinamobile.cmss.ops.monitor.cockpit.external.exception.SchedulerApiException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * SchedulerResponse
 * @param <T> body
 */
@Data
@Slf4j
public class SchedulerResponse<T> {
    
    /**
     * 元数据
     */
    private Meta meta;
    
    private T data;
    
    /**
     * validateSchedulerResponse
     * @throws SchedulerApiException SchedulerApiException
     */
    public void validateSchedulerResponse() {
        
        if (!"20000".equals(this.getMeta().getCode())) {
            log.error("调用故障调度接口失败,code:{},message:{}", this.getMeta().getCode(), this.getMeta().getMessage());
            throw new SchedulerApiException(this.getMeta().getMessage(), this.getMeta().getCode());
        } else {
            log.info("调用故障调度接口成功");
        }
    }
    
    @Data
    public static class Meta {
        
        /**
         * 编码
         */
        private String code;
        
        /**
         * 消息
         */
        private String message;
    }
}
