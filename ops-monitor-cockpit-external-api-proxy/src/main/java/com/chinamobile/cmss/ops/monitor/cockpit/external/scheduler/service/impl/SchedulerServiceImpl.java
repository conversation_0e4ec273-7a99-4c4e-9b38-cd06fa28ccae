/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.UserUtils;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.config.SchedulerProperties;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.dto.DateDutySelectByDayRangeDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.AESGCMBase64KeyInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.CockpitFaultSceneDetail;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.DateDutySelectByDayRangeResponseInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.DutyTemplateInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.MsgAuditTaskNum;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.SchedulerResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.service.SchedulerService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.List;
import java.util.Optional;

/**
 * @author: lingmeng
 * @Date: 2025/04/10 13:49
 * @Description:
 * @Version: 1.0
 **/

@Service
@Slf4j
public class SchedulerServiceImpl implements SchedulerService {
    
    public final RestClient restClient;
    
    private final SchedulerProperties schedulerProperties;
    
    public SchedulerServiceImpl(@Qualifier("schedulerRestClient") RestClient restClient, SchedulerProperties schedulerProperties) {
        this.restClient = restClient;
        this.schedulerProperties = schedulerProperties;
    }
    
    /**
     * 获取故障作战室列表
     * @return CockpitFaultSceneDetail
     **/
    @Override
    public SchedulerResponse<List<CockpitFaultSceneDetail>> querySceneListForCockpit() {
        
        log.info("请求故障调度中的活动作战室列表");
        String url = schedulerProperties.getBaseUrl() + schedulerProperties.getFaultSceneListRemoteUrl();
        SchedulerResponse<List<CockpitFaultSceneDetail>> schedulerResponse = restClient.post()
                .uri(url)
                .header("Cookie", "bk_token=" + UserUtils.getToken())
                .body(Maps.newHashMap())
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        schedulerResponse.validateSchedulerResponse();
        return schedulerResponse;
    }
    
    /**
     * 获取消息审批任务数目
     * @return MsgAuditTaskNum
     **/
    @Override
    public SchedulerResponse<MsgAuditTaskNum> queryAuditTaskNum() {
        log.info("请求故障调度中的消息审批任务数目");
        String url = schedulerProperties.getBaseUrl() + schedulerProperties.getMsgAuditTaskNumRemoteUrl();
        SchedulerResponse<MsgAuditTaskNum> schedulerResponse = restClient.get()
                .uri(url)
                .header("Cookie", "bk_token=" + UserUtils.getToken())
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        schedulerResponse.validateSchedulerResponse();
        return schedulerResponse;
    }
    
    /**
     * 依据日期范围查询日期排班数据
     * @param dateDutySelectByDayRangeDTO dateDutySelectByDayRangeDTO
     * @return DateDutySelectByDayRangeResponseInfos
     */
    @Override
    public SchedulerResponse<DateDutySelectByDayRangeResponseInfos> selectByDayRange(DateDutySelectByDayRangeDTO dateDutySelectByDayRangeDTO) {
        log.info("请求故障调度中日期排班数据入参：{}", dateDutySelectByDayRangeDTO.toString());
        String url = schedulerProperties.getBaseUrl() + schedulerProperties.getSelectByDayRangeUrl();
        SchedulerResponse<DateDutySelectByDayRangeResponseInfos> schedulerResponse = restClient.post()
                .uri(url)
                .header("Cookie", "bk_token=" + UserUtils.getToken())
                .body(dateDutySelectByDayRangeDTO)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        schedulerResponse.validateSchedulerResponse();
        return schedulerResponse;
    }
    
    /**
     * 查询值班模板列表
     * @param disable      是否启用
     * @param templateName 模板名称
     * @param fullName     全程
     * @return list
     */
    @Override
    public SchedulerResponse<List<DutyTemplateInfos>> querySchedulingTemplateList(Integer disable, String templateName, String fullName) {
        
        String url = schedulerProperties.getBaseUrl() + schedulerProperties.getInvokeByDutySettingUrl();
        
        URI finalUri = UriComponentsBuilder
                .fromHttpUrl(url)
                .queryParamIfPresent("disable", Optional.ofNullable(disable))
                .queryParamIfPresent("templateName", Optional.ofNullable(templateName))
                .queryParamIfPresent("fullName", Optional.ofNullable(fullName))
                .build(true)
                .toUri();
        log.info("请求故障调度中排班模板入参：disable={},templateName={},fullName={}，接口url：{}", disable, templateName, fullName, finalUri);
        SchedulerResponse<List<DutyTemplateInfos>> response = restClient.get()
                .uri(finalUri)
                .header("Cookie", "bk_token=" + UserUtils.getToken())
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        if (response != null) {
            response.validateSchedulerResponse();
        }
        
        return response;
    }
    
    /**
     * 密钥
     * @return 秘钥
     */
    @Override
    public SchedulerResponse<AESGCMBase64KeyInfos> queryAESGCMBase64Key() {
        String url = schedulerProperties.getBaseUrl() + schedulerProperties.getQueryAESGCMBase64KeyUrl();
        SchedulerResponse<AESGCMBase64KeyInfos> schedulerResponse = restClient.get()
                .uri(url)
                .header("Cookie", "bk_token=" + UserUtils.getToken())
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        schedulerResponse.validateSchedulerResponse();
        return schedulerResponse;
    }
}
