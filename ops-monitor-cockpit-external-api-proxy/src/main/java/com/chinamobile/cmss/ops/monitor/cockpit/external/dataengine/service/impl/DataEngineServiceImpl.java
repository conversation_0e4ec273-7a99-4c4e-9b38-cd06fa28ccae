/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.config.DataEngineProperties;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.dto.DataEngineQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.DataEngineResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.WorkOrderNumStatistic;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.WorkOrderOverview;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.service.DataEngineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

/**
 * @author: lingmeng
 * @Date: 2025/5/11 11:07
 * @Description: DataEngine Service
 */
@Service
@Slf4j
public class DataEngineServiceImpl implements DataEngineService {
    
    public final RestClient restClient;
    
    private final DataEngineProperties dataEngineProperties;
    
    public DataEngineServiceImpl(@Qualifier("dataEngineRestClient") RestClient restClient, DataEngineProperties dataEngineProperties) {
        this.restClient = restClient;
        this.dataEngineProperties = dataEngineProperties;
    }
    
    @Override
    public DataEngineResponse<WorkOrderOverview> getSuperviseWorkOrderList(DataEngineQueryDTO dataEngineQueryDTO) {
        
        log.info("请求数据治理中督办故障工单/事件工单接口的入参：{}", dataEngineQueryDTO.toString());
        // 组装数据治理除了sql和存储介质外的其他参数
        dataEngineQueryDTO.setBkAppCode(dataEngineProperties.getAppCode());
        dataEngineQueryDTO.setBkAppSecret(dataEngineProperties.getAppSecret());
        dataEngineQueryDTO.setBkdataAuthenticationMethod(dataEngineProperties.getBkdataAuthenticationMethod());
        dataEngineQueryDTO.setBkdataDataToken(dataEngineProperties.getDataToken());
        
        String url = dataEngineProperties.getBaseUrl();
        
        DataEngineResponse<WorkOrderOverview> dataEngineResponse = restClient.post()
                .uri(url)
                .body(dataEngineQueryDTO)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        dataEngineResponse.validateDataEngineResponse();
        
        return dataEngineResponse;
    }
    
    @Override
    public DataEngineResponse<WorkOrderNumStatistic> getWorkOrderNum(DataEngineQueryDTO dataEngineQueryDTO) {
        
        log.info("请求数据治理中某类工单数目接口的入参：{}", dataEngineQueryDTO.toString());
        // 组装数据治理除了sql和存储介质外的其他参数
        dataEngineQueryDTO.setBkAppCode(dataEngineProperties.getAppCode());
        dataEngineQueryDTO.setBkAppSecret(dataEngineProperties.getAppSecret());
        dataEngineQueryDTO.setBkdataAuthenticationMethod(dataEngineProperties.getBkdataAuthenticationMethod());
        dataEngineQueryDTO.setBkdataDataToken(dataEngineProperties.getDataToken());
        
        String url = dataEngineProperties.getBaseUrl();
        
        DataEngineResponse<WorkOrderNumStatistic> dataEngineResponse = restClient.post()
                .uri(url)
                .body(dataEngineQueryDTO)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        dataEngineResponse.validateDataEngineResponse();
        
        return dataEngineResponse;
    }
    
    /**
     * 查询数据治理
     * @param dataEngineQueryDTO data
     * @param <T>                泛型
     * @return 泛型
     */
    @Override
    public <T> DataEngineResponse<T> requestDataEngine(DataEngineQueryDTO dataEngineQueryDTO) {
        
        log.info("请求数据治理中/v3/dataquery/query/接口的入参：{}", dataEngineQueryDTO.toString());
        // 组装数据治理除了sql和存储介质外的其他参数
        dataEngineQueryDTO.setBkAppCode(dataEngineProperties.getAppCode());
        dataEngineQueryDTO.setBkAppSecret(dataEngineProperties.getAppSecret());
        dataEngineQueryDTO.setBkdataAuthenticationMethod(dataEngineProperties.getBkdataAuthenticationMethod());
        dataEngineQueryDTO.setBkdataDataToken(dataEngineProperties.getDataToken());
        
        String url = dataEngineProperties.getBaseUrl();
        
        DataEngineResponse<T> dataEngineResponse = restClient.post()
                .uri(url)
                .body(dataEngineQueryDTO)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        dataEngineResponse.validateDataEngineResponse();
        
        return dataEngineResponse;
    }
}
