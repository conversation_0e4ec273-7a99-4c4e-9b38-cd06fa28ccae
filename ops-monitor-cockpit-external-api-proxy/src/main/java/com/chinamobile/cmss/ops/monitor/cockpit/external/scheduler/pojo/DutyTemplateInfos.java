/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DutyTemplateInfos {
    
    /**
     * @field 模板id:762342dd-4273-4667-a785-d71c61363028
     */
    private String id;
    
    /**
     * @field 模板编号:JCPB
     */
    private String code;
    
    /**
     * @field 模板名称:日期排版模板
     */
    private String name;
    
    /**
     * @field 模板类型 1日期排班 2固定排班 3定制化排班
     */
    private Integer templateType;
    
    /**
     * @field 启禁用：0启用 1禁用
     */
    private Integer disable;
    
    /**
     * @field 版本号 1,2,3…递增
     */
    private Integer versionCode;
    
    /**
     * @field 更新标记 0：不需更新 1：需更新
     */
    private Integer updateFlag;
}