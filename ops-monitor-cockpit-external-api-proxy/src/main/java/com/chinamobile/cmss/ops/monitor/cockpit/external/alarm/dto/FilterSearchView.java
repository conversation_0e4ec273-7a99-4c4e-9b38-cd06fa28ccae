/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FilterSearchView extends FuzzySearchView implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private List<PropertyFilter> conditionList;
    
    private String[] checkedColumns;
    
    private String exportDatabase;
    
    private Integer exportSize;
    
    /**
     * 数据库查询标识(doris/es)
     * 仅在同一查询模块区分doris查询和es查询
     * 区别doris sql和es sql不兼容的语法
     */
    private String dataBaseFlag;
    
}
