/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.service;

import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto.SearchInstDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto.SearchInstanceByObjectDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto.SearchObjectAttributeDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.CmdbResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.ResourcePoolModelInstanceInfo;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.SearchInstInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.SearchObjectAttributeInfos;

import java.util.List;

/**
 * Cmdb Service
 */
public interface CmdbService {
    
    /**
     * 获取资源池列表
     * @param searchResourcePoolDTO searchResourcePoolDTO
     * @return ResourcePoolModelInstanceInfo
     */
    CmdbResponse<ResourcePoolModelInstanceInfo> getResourcePoolInfoList(SearchInstanceByObjectDTO searchResourcePoolDTO);
    
    /**
     * 根据关联关系实例查询模型实例
     * @param searchInstDTO searchInstDTO
     * @return CmdbResponse
     */
    CmdbResponse<SearchInstInfos> searchInst(SearchInstDTO searchInstDTO);
    
    /**
     * 查询对象模型属性
     * @param searchObjectAttributeDTO searchObjectAttributeDTO
     * @return CmdbResponse
     */
    CmdbResponse<List<SearchObjectAttributeInfos>> searchObjectAttribute(SearchObjectAttributeDTO searchObjectAttributeDTO);
}
