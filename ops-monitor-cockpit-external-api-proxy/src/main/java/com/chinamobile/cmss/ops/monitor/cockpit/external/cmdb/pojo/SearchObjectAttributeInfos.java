/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Describe
 */
@Data
public class SearchObjectAttributeInfos implements Serializable {
    
    @JsonProperty("creator")
    // 数据的创建者
    private String creator;
    
    @JsonProperty("description")
    // 数据的描述信息
    private String description;
    
    @JsonProperty("editable")
    // 表明数据是否可编辑
    private boolean editable;
    
    @JsonProperty("isonly")
    // 表明唯一性
    private boolean isonly;
    
    @JsonProperty("ispre")
    // true: 预置字段, false: 非内置字段
    private boolean ispre;
    
    @JsonProperty("isreadonly")
    // true: 只读, false: 非只读
    private boolean isreadonly;
    
    @JsonProperty("isrequired")
    // true: 必填, false: 可选
    private boolean isrequired;
    
    @JsonProperty("option")
    // 用户自定义内容，存储的内容及格式由调用方决定
    private List<TypeEnums> option;
    
    @JsonProperty("unit")
    // 单位
    private String unit;
    
    @JsonProperty("placeholder")
    // 占位符
    private String placeholder;
    
    @JsonProperty("bk_property_group")
    // 字段分栏的名字
    private String bkPropertyGroup;
    
    @JsonProperty("bk_obj_id")
    // 模型ID
    private String bkObjId;
    
    @JsonProperty("bk_supplier_account")
    // 开发商账号
    private String bkSupplierAccount;
    
    @JsonProperty("bk_property_id")
    // 模型的属性ID
    private String bkPropertyId;
    
    @JsonProperty("bk_property_name")
    // 模型属性名，用于展示
    private String bkPropertyName;
    
    @JsonProperty("bk_property_type")
    // 定义的属性字段用于存储数据的数据类型（singlechar, longchar, int, enum, date, time, objuser, singleasst, multiasst, timezone, bool）
    private String bkPropertyType;
    
    @JsonProperty("bk_asst_obj_id")
    // 如果有关联其它的模型，那么就必须设置此字段，否则不需要设置
    private String bkAsstObjId;
    
    @JsonProperty("bk_biz_id")
    // 业务自定义字段的业务ID
    private int bkBizId;
    
    @JsonProperty("create_time")
    // 创建时间
    private String createTime;
    
    @JsonProperty("last_time")
    // 更新时间
    private String lastTime;
    
    @JsonProperty("id")
    // 查询对象的ID值
    private int id;
    
    @JsonProperty("bk_property_index")
    private int bkPropertyIndex;
    
    @JsonProperty("bk_issystem")
    private boolean bkIssystem;
    
    @JsonProperty("bk_isapi")
    private boolean bkIsapi;
    
    @Data
    public static class TypeEnums {
        
        private String id;
        
        private String name;
        
        private String type;
        
        @JsonProperty("is_default")
        private Boolean isDefault;
    }
}
