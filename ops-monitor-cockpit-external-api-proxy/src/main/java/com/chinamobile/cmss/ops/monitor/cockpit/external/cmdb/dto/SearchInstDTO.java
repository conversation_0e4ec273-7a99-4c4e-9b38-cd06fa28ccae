/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Describe
 */
@Data
@SuperBuilder
public class SearchInstDTO extends CmdbBaseArgs {
    
    /**
     * 模型ID
     */
    
    @JsonProperty("bk_obj_id")
    private String bkObjId;
    /**
     * 具有关联关系的模型实例查询条件
     */
    
    @JsonProperty("condition")
    private Map<String, List<BaseParam>> condition;
    /**
     * 具有关联关系的模型实例查询条件
     */
    
    @JsonProperty("time_condition")
    private TimeCondition timeCondition;
    /**
     * 指定查询模型实例返回的字段,key为模型ID，value为该查询模型要返回的模型属性字段
     */
    
    @JsonProperty("fields")
    private Map<String, List<String>> fields;
    
    @Data
    @Builder
    public static class BaseParam {
        
        @JsonProperty("field")
        private String field;
        
        @JsonProperty("operator")
        private String operator;
        
        @JsonProperty("value")
        private Object value;
        
    }
    
    @Data
    @Builder
    public static class TimeCondition {
        
        @JsonProperty("oper")
        private String oper;
        
        @JsonProperty("rules")
        private List<Rule> rules;
        
        @JsonProperty("value")
        private String value;
        
        @Data
        @Builder
        public static class Rule {
            
            @JsonProperty("field")
            private String field;
            
            @JsonProperty("start")
            private String start;
            
            @JsonProperty("end")
            private String end;
        }
    }
    /*
     * @Data
     * 
     * @Builder public static class Field{
     * 
     * @JsonProperty("bk_switch") private List<String> bkSwitch;
     * 
     * }
     */
}
