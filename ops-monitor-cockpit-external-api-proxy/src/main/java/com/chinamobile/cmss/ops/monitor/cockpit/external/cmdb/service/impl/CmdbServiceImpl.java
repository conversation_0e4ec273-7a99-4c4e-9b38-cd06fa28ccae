/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.service.impl;

import com.alibaba.fastjson2.JSON;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.config.CmdbProperties;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto.SearchInstDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto.SearchInstanceByObjectDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto.SearchObjectAttributeDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.CmdbResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.ResourcePoolModelInstanceInfo;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.SearchInstInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.SearchObjectAttributeInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.service.CmdbService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

import java.util.List;

/**
 * @author: nijiahui
 * @Date: 2023/11/27 11:07
 * @Description: Cmdb Service
 */
@Service
@Slf4j
public class CmdbServiceImpl implements CmdbService {
    
    public final RestClient restClient;
    
    private final CmdbProperties cmdbProperties;
    
    public CmdbServiceImpl(@Qualifier("cmdbRestClient") RestClient restClient, CmdbProperties cmdbProperties) {
        this.restClient = restClient;
        this.cmdbProperties = cmdbProperties;
    }
    
    /**
     * 取资源池实例信息
     * @param searchResourcePoolDTO searchResourcePoolDTO
     * @return ResourcePoolModelInstanceInfo
     */
    @Override
    public CmdbResponse<ResourcePoolModelInstanceInfo> getResourcePoolInfoList(SearchInstanceByObjectDTO searchResourcePoolDTO) {
        log.info("请求cmdb中ResourcePoolInfos接口的入参：{}", searchResourcePoolDTO.toString());
        searchResourcePoolDTO.buildCmdbProperties(cmdbProperties.getBkAppCode(), cmdbProperties.getBkAppSecret(),
                cmdbProperties.getBkUsername());
        String url = cmdbProperties.getSearchInstanceByObjectUrl();
        
        CmdbResponse<ResourcePoolModelInstanceInfo> cmdbResponse = restClient.post()
                .uri(url)
                .body(searchResourcePoolDTO)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        cmdbResponse.validateCmdbResponse();
        
        return cmdbResponse;
    }
    
    /**
     * 根据关联关系实例查询模型实例
     * @param searchInstDTO searchInstDTO
     * @return CmdbResponse
     */
    @Override
    public CmdbResponse<SearchInstInfos> searchInst(SearchInstDTO searchInstDTO) {
        log.info("请求cmdb中searchInst接口的入参：{}", searchInstDTO.toString());
        searchInstDTO.buildCmdbProperties(cmdbProperties.getBkAppCode(), cmdbProperties.getBkAppSecret(),
                cmdbProperties.getBkUsername());
        String url = cmdbProperties.getSearchInstUrl();
        
        CmdbResponse<SearchInstInfos> cmdbResponse = restClient.post()
                .uri(url)
                .body(searchInstDTO)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        cmdbResponse.validateCmdbResponse();
        return cmdbResponse;
    }
    
    /**
     * 查询对象模型属性
     * @param searchObjectAttributeDTO searchObjectAttributeDTO
     * @return CmdbResponse
     */
    @Override
    public CmdbResponse<List<SearchObjectAttributeInfos>> searchObjectAttribute(SearchObjectAttributeDTO searchObjectAttributeDTO) {
        log.info("请求cmdb中searchObjectAttribute接口的入参：{}", JSON.toJSONString(searchObjectAttributeDTO));
        searchObjectAttributeDTO.buildCmdbProperties(cmdbProperties.getBkAppCode(), cmdbProperties.getBkAppSecret(),
                cmdbProperties.getBkUsername());
        
        String url = cmdbProperties.getSearchObjectAttributeUrl();
        
        CmdbResponse<List<SearchObjectAttributeInfos>> cmdbResponse = restClient.post()
                .uri(url)
                .body(searchObjectAttributeDTO)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        cmdbResponse.validateCmdbResponse();
        return cmdbResponse;
    }
}
