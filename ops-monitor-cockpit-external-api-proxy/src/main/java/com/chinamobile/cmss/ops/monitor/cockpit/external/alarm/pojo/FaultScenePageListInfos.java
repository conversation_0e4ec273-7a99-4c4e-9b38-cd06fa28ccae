/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FaultScenePageListInfos {
    
    private List<FaultScenePlainVO> list;
    
    private long total;
    
    @Data
    public static class FaultScenePlainVO {
        
        /**
         * 主键 uuid
         */
        private String id;
        
        /**
         * 名称，作战室主题
         */
        private String name;
        
        /**
         * 故障等级：0-一般故障，1-重要故障，2-重大故障
         */
        private Integer grade;
        
        /**
         * 环节状态：10-60，10-调度开始，20-故障处理，30-故障修复，40-恢复确认，50-客户通知，60-调度结束
         */
        private Integer phaseStatus;
        
        /**
         * 状态：0-关闭，1-开启
         */
        private Integer status;
        
        /**
         * 创建账号
         */
        private String username;
        
        /**
         * 创建中文名
         */
        private String fullName;
        
        /**
         * 开始时间
         */
        private Date startTime;
        
        /**
         * 结束时间
         */
        private Date endTime;
        
        /**
         * 创建时间
         */
        private Date createTime;
        
        /**
         * 更新时间
         */
        private Date updateTime;
        
        /**
         * 是否影响客户业务，0-否，1-是
         */
        private Integer isImpactBiz;
        
        /**
         * 故障类型code
         */
        private String sceneTypeCode;
        
        /**
         * 资源池
         */
        private String region;
        
        /**
         * 故障产品
         */
        private String faultProduct;
        
        /**
         * 主要影响
         */
        private String mainImpact;
        
        /**
         * 投诉信息
         */
        private String complaintInfo;
        
        /**
         * 发生时间
         */
        private Date occurTime;
        
        /**
         * 发现时间
         */
        private Date findTime;
        
        /**
         * 是否恢复
         */
        private Integer isRecover;
        
        /**
         * 恢复时间
         */
        private Date recoverTime;
        
        /**
         * 故障原因
         */
        private String faultReason;
        
        /**
         * 故障信息
         */
        private String faultInfo;
        
        /**
         * 故障解决措施
         */
        private String solution;
        
        /**
         * 短信的最近一次发送时间
         */
        private Date lastMessageSendTime;
        
        /**
         * 内部短信的最近一次发送时间
         */
        private Date innerLastMessageSendTime;
        
        /**
         * 普通作战室短信的最近一次发送时间common_last_message_send_time
         */
        private Date commonLastMessageSendTime;
        
        /**
         * 升级时间
         */
        private Date upgradeTime;
        
        /**
         * 故障概述
         */
        private String faultSummary;
        
        /**
         * 故障资源池ID(分号分隔)
         */
        private String faultResource;
        
        /**
         * 预计恢复时间
         */
        private Date estimateResumeTime;
        
        /**
         * 客户自行恢复手段
         */
        private String customerResumeInfo;
        
        /**
         * 处理进展
         */
        private String treamentProgress;
        
        /**
         * 信息栏主要影响是否刷新标志位，0-不刷新 1-刷新
         */
        private Integer isImpactNeedRefresh;
        
        /**
         * 作战室创建之初是否创建视讯，0-否 1-是
         */
        private Integer isCreateRoom;
        
        /**
         * 作战室目前是否存在视讯，0-否 1-是
         */
        private Integer isExistRoom;
        
        /**
         * 调度人姓名
         */
        private String schedulerManFullName;
        
        /**
         * 调度人手机号
         */
        private String schedulerManPhone;
        
        /**
         * 是否全部资源池，null代表当前没有在信息栏里面选择任何东西，0-未选择全局资源池选项，1-已选择全局资源池选项
         */
        private Integer isAllFaultResource;
        
        /**
         * 作战室编号
         */
        private String sceneNo;
        
        /**
         * 当前作战室当前使用的fot影响面的uuid
         */
        private String usingFotImpactUuid;
        
        /**
         * 当前作战室当前使用的fot影响面的下载文件链接
         */
        private String usingFotImpactFileUrl;
        
        /**
         * 当前作战室接收的fot推送的最新影响面（不一定使用）的uuid
         */
        private String lastReceiveFotImpactUuid;
        
        /**
         * 故障历时
         */
        private String faultDuration;
        
        /**
         * 关联工单号
         */
        private String relatedOrderNo;
        
        /**
         * 删除标识
         * 0-未删除，1-已删除
         */
        private Integer delFlag;
        
        /**
         * foc故障id
         */
        private String focFaultId;
        
        /**
         * 作战室是否foc创建
         * 0-否，1-是
         */
        private Integer isFocCreate;
        
        /**
         * 云审计
         */
        private String productName;
        
        /**
         * IaaS产品部
         */
        private String belongDept;
        
        /**
         * 本作战室是否发送过短信
         * 0-否，1-是
         */
        private Integer isSendMessage;
        
        /**
         * 作战室正在使用的影响面数据的客户影响清单
         */
        private String usingFotImpactCustomerList;
        
        /**
         * 作战室正在使用的影响面数据的客户标签
         */
        private String usingFotCustomerLabel;
        
        /**
         * 报障数量
         */
        private Integer reportFaultNum;
        
        /**
         * 作战室正在使用的影响面数据的影响省份
         */
        private String usingFotInfluenceProvince;
        
        /**
         * 值班长
         */
        private String shiftForeman;
        
        /**
         * 主故障处理人员
         */
        private String mainFaultHandler;
        
        /**
         * 调度会议信息
         */
        private String meetingInfo;
        
        /**
         * 告警是否核实，0-否，1-是
         */
        private Integer isAlarmVerify;
        
        /**
         * 变更看板核实情况，0-否，1-是
         */
        private Integer isChangeDashboardVerify;
        
        /**
         * 是否执行应急快恢手段，0-否，1-是
         */
        private Integer isExecuteEmergencyQuickRecovery;
        
        /**
         * 是否查看操作日志，0-否，1-是
         */
        private Integer isCheckOperateLog;
        
        /**
         * 是否使用诊断树，0-否，1-是
         */
        private Integer isUseDiagnosisTree;
        
        /**
         * 是否发送过云空间，0-否，1-是
         */
        private Integer isSendCloudSpace;
        
    }
}
