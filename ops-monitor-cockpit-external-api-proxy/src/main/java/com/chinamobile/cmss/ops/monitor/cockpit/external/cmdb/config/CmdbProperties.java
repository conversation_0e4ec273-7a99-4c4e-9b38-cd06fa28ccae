/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Cmdb Properties
 */
@ConfigurationProperties(prefix = "ops.monitor.cockpit.cmdb")
@Data
public class CmdbProperties {
    
    /**
     * cmdb url
     */
    private String baseUrl;
    
    /**
     * cmdb 应用ID
     */
    private String bkAppCode;
    
    /**
     * 用户
     */
    
    private String bkUsername;
    
    /**
     * cmdb 安全密钥
     */
    
    private String bkAppSecret;
    
    /**
     * search_inst_by_object
     */
    private String searchInstanceByObjectUrl = "/api/c/compapi/v2/cc/search_inst_by_object/";
    
    /**
     * search_inst
     */
    private String searchInstUrl = "/api/c/compapi/v2/cc/search_inst/";
    
    /**
     * search_inst
     */
    private String searchObjectAttributeUrl = "/api/c/compapi/v2/cc/search_object_attribute/";
    
}
