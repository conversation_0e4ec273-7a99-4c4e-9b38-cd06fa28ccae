/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.message.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chinamobile.cmss.ops.monitor.cockpit.common.constant.MessageConstant;
import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.SHA256Utils;
import com.chinamobile.cmss.ops.monitor.cockpit.external.exception.MessageApiException;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.config.MessageProperties;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.dto.CloudSpaceMessageSendDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.dto.ShortMessageSendDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.pojo.CloudSpaceMessageResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.pojo.ShortMessageResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

import java.util.HashMap;
import java.util.Map;

/**
 *  @author: lingmeng
 *  @Date: 2025/05/18 13:49
 *  @Description:
 *  @Version: 1.0
 **/

@Service
@Slf4j
public class MessageServiceImpl implements MessageService {
    
    public final RestClient restClient;
    
    private final MessageProperties messageProperties;
    
    public MessageServiceImpl(@Qualifier("messageRestClient") RestClient restClient, MessageProperties messageProperties) {
        this.restClient = restClient;
        this.messageProperties = messageProperties;
    }
    
    @Override
    public ShortMessageResponse sendShortMessage(ShortMessageSendDTO shortMessageSendDTO) {
        
        // 设置源系统
        shortMessageSendDTO.setSourceSystem(messageProperties.getSourceSystem());
        log.info("请求短信发送接口，入参：{}", JSONUtil.toJsonStr(shortMessageSendDTO));
        String url = messageProperties.getBaseUrl() + messageProperties.getShortMessageRemoteUrl();
        Map<String, String> headerMap = getHeader();
        ShortMessageResponse shortMessageResponse = restClient.post()
                .uri(url)
                .header("key", headerMap.get("key"))
                .header("signature", headerMap.get("signature"))
                .header("nonce", headerMap.get("nonce"))
                .header("timestamp", headerMap.get("timestamp"))
                .body(shortMessageSendDTO)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        log.info("请求短信发送接口，出参：{}", JSONUtil.toJsonStr(shortMessageResponse));
        shortMessageResponse.validateShortMessageResponse();
        return shortMessageResponse;
    }
    
    @Override
    public CloudSpaceMessageResponse sendCloudSpaceMessage(CloudSpaceMessageSendDTO cloudSpaceMessageSendDTO) {
        
        // 指定action,msgType,rstSend;其余content,message,title,userMobiles,categoryName,sender,sendUserType均由业务侧指定
        cloudSpaceMessageSendDTO.setAction(StrUtil.isNotBlank(cloudSpaceMessageSendDTO.getAction()) ? cloudSpaceMessageSendDTO.getAction() : StrUtil.EMPTY);
        cloudSpaceMessageSendDTO.setMsgType(MessageConstant.CloudSpace.MsgType.TEXT);
        cloudSpaceMessageSendDTO.setRstSend(MessageConstant.CloudSpace.RstSend.NO);
        log.info("请求云空间发送接口，入参：{}", JSONUtil.toJsonStr(cloudSpaceMessageSendDTO));
        String url = messageProperties.getBaseUrl() + messageProperties.getCloudSpaceMessageRemoteUrl();
        Map<String, String> headerMap = getHeader();
        CloudSpaceMessageResponse cloudSpaceMessageResponse = restClient.post()
                .uri(url)
                .header("key", headerMap.get("key"))
                .header("signature", headerMap.get("signature"))
                .header("nonce", headerMap.get("nonce"))
                .header("timestamp", headerMap.get("timestamp"))
                .body(cloudSpaceMessageSendDTO)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        log.info("请求云空间发送接口，出参：{}", JSONUtil.toJsonStr(cloudSpaceMessageResponse));
        cloudSpaceMessageResponse.validateCloudSpaceMessageResponse();
        return cloudSpaceMessageResponse;
    }
    
    /**
     * 短信邮件鉴权
     * <AUTHOR>
     * @return java.util.Map
     * @throws MessageApiException 消息api异常
     */
    private Map<String, String> getHeader() {
        
        Map<String, String> header = new HashMap<>();
        String signature = null;
        
        // 获取当前13位时间戳
        final long currentTimeStamp = System.currentTimeMillis();
        // 因为随机数需要六位以上，所以生成一个10000至999999之间的随机数
        final int currentRandomNumber = RandomUtils.nextInt(MessageConstant.EmailAndMessageSignature.NONCE_START,
                MessageConstant.EmailAndMessageSignature.NONCE_END);
        
        try {
            signature = SHA256Utils.encode(messageProperties.getToken() + currentTimeStamp + currentRandomNumber);
        } catch (final Exception e) {
            log.error("调用消息接口时，签名加密失败", e);
            throw new MessageApiException("签名获取失败", "500");
        }
        
        header.put("key", messageProperties.getKey());
        header.put("signature", signature);
        header.put("nonce", String.valueOf(currentRandomNumber));
        header.put("timestamp", String.valueOf(currentTimeStamp));
        
        return header;
    }
}
