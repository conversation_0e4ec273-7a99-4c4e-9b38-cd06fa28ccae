/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.influence.service;

import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.pojo.FaultAggregateBoardInfo;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.pojo.InfluenceResponse;

/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2023
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */
/*
 * description 故障影响面对接Service
 * @Author: lingmeng
 * @Date:  2025年04月10日 16:53:08
 */
public interface InfluenceService {
    
    /**
     * 获取驾驶舱的工单故障等级信息
     * @return FaultAggregateBoardInfo
     */
    InfluenceResponse<FaultAggregateBoardInfo> queryFaultAggregateBoardInfo();
    
}
