/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class DataEngineQueryDTO {
    
    /**
     * 蓝鲸app编码
     */
    @JsonProperty("bk_app_code")
    private String bkAppCode;
    
    /**
     * 蓝鲸app秘钥
     */
    @JsonProperty("bk_app_secret")
    private String bkAppSecret;
    
    /**
     * 数据认证方法
     */
    @JsonProperty("bkdata_authentication_method")
    private String bkdataAuthenticationMethod;
    
    /**
     * 蓝鲸数据认证方法
     */
    @JsonProperty("bkdata_data_token")
    private String bkdataDataToken;
    
    /**
     * 优先存储
     */
    @JsonProperty("prefer_storage")
    private String preferStorage;
    
    /**
     * sql语句
     */
    @JsonProperty("sql")
    private String sql;
}
