/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo;

import com.chinamobile.cmss.ops.monitor.cockpit.external.exception.DataEngineApiException;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * DataEngineResponse
 * @param <T> body
 */
@Data
@Slf4j
public class DataEngineResponse<T> {
    
    private Errors errors;
    
    private String code;
    
    private boolean result;
    
    private String message;
    
    private ResponseData<T> data;
    
    /**
     * validateDataEngineResponse
     * @throws DataEngineApiException DataEngineApiException
     */
    public void validateDataEngineResponse() {
        
        if (!this.isResult()) {
            log.error("调用数据治理接口失败,code:{},message:{}", this.code, this.getMessage());
            throw new DataEngineApiException(this.getMessage(), String.valueOf(this.code));
        }
    }
    
    @Data
    public static class ResponseData<T> {
        
        /**
         * 集群
         */
        private String cluster;
        
        /**
         * 总共条目
         */
        private Long totalRecords;
        
        /**
         * 来源
         */
        private String source;
        
        /**
         * 总共条目
         */
        private List<T> list;
        
        /**
         * 查询字段排序
         */
        @JsonProperty("select_fields_order")
        private List<String> selectFieldsOrder;
        
        /**
         * 查询sql
         */
        private String sql;
        
        /**
         * 总数据量
         */
        @JsonProperty("total_record_size")
        private Long totalRecordSize;
        
        /**
         * 时间开销
         */
        private Double timetaken;
        
        /**
         * 设备
         */
        private String device;
        
        /**
         * 结果表ids
         */
        @JsonProperty("result_table_ids")
        private List<String> resultTableIds;
        
    }
    
    @Data
    public static class Errors {
        
        private String error;
        
        @JsonProperty("query_id")
        private String queryId;
    }
}
