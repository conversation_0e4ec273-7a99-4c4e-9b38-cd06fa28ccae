/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.influence.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FaultAggregateBoardInfo implements Serializable {
    
    /**
     * 自关联
     */
    @JsonProperty("self_related")
    private Integer selfRelated;
    
    /**
     * 处理效率
     */
    @JsonProperty("handling_efficiency")
    private HandlingEfficiencyInfo handlingEfficiencyInfo;
    
    /**
     * 故障等级
     */
    @JsonProperty("fault_level")
    private FaultLevelInfo faultLevelInfo;
    
    @Data
    public static class HandlingEfficiencyInfo {
        
        /**
         * 即将超时
         */
        @JsonProperty("to_time_out")
        private Integer toTimeOut;
        
        /**
         * 超时
         */
        @JsonProperty("timed_out")
        private Integer timedOut;
        
        /**
         * 不及时响应
         */
        @JsonProperty("untimely_response")
        private Integer untimelyResponse;
        
    }
    
    @Data
    public static class FaultLevelInfo {
        
        /**
         * 一般事件
         */
        @JsonProperty("general_event")
        private Integer generalEvent;
        
        /**
         * 一般故障
         */
        @JsonProperty("normal_fault")
        private Integer normalFault;
        
        /**
         * 较大故障
         */
        @JsonProperty("serious_fault")
        private Integer seriousFault;
        
        /**
         * 重大故障
         */
        @JsonProperty("major_fault")
        private Integer majorFault;
        
        /**
         * 严重故障
         */
        @JsonProperty("critical_fault")
        private Integer criticalFault;
        
        /**
         * 故障
         */
        @JsonProperty("fault")
        private Integer fault;
        
        /**
         * 总计
         */
        @JsonProperty("all")
        private Integer all;
        
    }
    
}
