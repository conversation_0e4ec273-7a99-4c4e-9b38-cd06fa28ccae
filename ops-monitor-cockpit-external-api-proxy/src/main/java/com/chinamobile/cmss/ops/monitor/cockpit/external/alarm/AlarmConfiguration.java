/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.alarm;

import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.config.AlarmClientHttpRequestInterceptor;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.config.AlarmProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.web.client.RestClient;

/**
 * 告警系统配置
 */
@Configuration
@EnableConfigurationProperties(AlarmProperties.class)
@ComponentScan
public class AlarmConfiguration {
    
    private final AlarmProperties alarmProperties;
    
    public AlarmConfiguration(AlarmProperties alarmProperties) {
        this.alarmProperties = alarmProperties;
    }
    
    /**
     * 告警系统 RestClient
     *
     * @return RestClient
     */
    @Bean("alarmRestClient")
    public RestClient restClient() {
        return RestClient.builder()
                .baseUrl(alarmProperties.getBaseUrl())
                .requestInterceptor(clientHttpRequestInterceptor())
                .build();
    }
    
    /**
     * 告警系统 HTTP 请求拦截器
     *
     * @return ClientHttpRequestInterceptor
     */
    @Bean("alarmClientHttpRequestInterceptor")
    public ClientHttpRequestInterceptor clientHttpRequestInterceptor() {
        return new AlarmClientHttpRequestInterceptor();
    }
}
