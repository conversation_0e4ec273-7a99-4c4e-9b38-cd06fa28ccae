/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
public class SearchInstanceByObjectDTO extends CmdbBaseArgs {
    
    /**
     * 模型ID
     */
    @JsonProperty("bk_obj_id")
    private String bkObjId;
    
    /**
     * 查询条件
     */
    @JsonProperty("condition")
    private Condition condition;
    
    /**
     * 指定查询的字段
     */
    @JsonProperty("fields")
    private List<String> fields;
    
    @Data
    @Builder
    @AllArgsConstructor
    public static class Condition {
        
        @JsonProperty("bk_inst_id")
        private Integer bkInstId;
        
        @JsonProperty("bk_inst_name")
        private String bkInstName;
        
        @JsonProperty("code")
        private String code;
        
    }
}
