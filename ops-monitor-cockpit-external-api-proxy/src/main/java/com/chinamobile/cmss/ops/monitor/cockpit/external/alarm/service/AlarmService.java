/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.service;

import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto.AlarmTypeFilterStatisticDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto.FaultSceneListQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto.FilterSearchView;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmLevelFilterStatisticInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.FaultScenePageListInfos;

import java.util.Map;

/**
 * 告警服务接口
 */
public interface AlarmService {
    
    /**
     * 根据过滤器id查询重大告警数量
     * @param alarmStatisticForCockpitDto filter
     * @return map
     */
    AlarmResponse<Map<String, Integer>> getMajorAlarmCountByFilter(AlarmTypeFilterStatisticDTO alarmStatisticForCockpitDto);
    
    /**
     * 根据过滤器id查询告警级别统计信息
     * @param startTime 发生时间
     * @param endTime   发生时间
     * @param filterId  过滤器id
     * @return AlarmResponse
     */
    AlarmResponse<AlarmLevelFilterStatisticInfos> getLevelFilterStatistic(String startTime, String endTime, String filterId);
    
    /**
     * 查询作战室列表
     * @param faultSceneListQueryDTO fault
     * @return 故障列表
     */
    AlarmResponse<FaultScenePageListInfos> queryFaultSceneList(FaultSceneListQueryDTO faultSceneListQueryDTO);
    
    /**
     * 活动告警统计查询
     * @param filterSearchView filter
     * @return map
     */
    AlarmResponse<Map<String, Integer[]>> getAlarmNum(FilterSearchView filterSearchView);
}
