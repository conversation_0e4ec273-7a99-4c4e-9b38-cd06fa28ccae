/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.UserUtils;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.config.AlarmProperties;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto.AlarmTypeFilterStatisticDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto.FaultSceneListQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto.FilterSearchView;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmLevelFilterStatisticInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.FaultScenePageListInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.service.AlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

import java.util.Map;

/**
 * 告警服务实现类
 */
@Service
@Slf4j
public class AlarmServiceImpl implements AlarmService {
    
    private final RestClient restClient;
    
    private final AlarmProperties alarmProperties;
    
    public AlarmServiceImpl(@Qualifier("alarmRestClient") RestClient restClient, AlarmProperties alarmProperties) {
        this.restClient = restClient;
        this.alarmProperties = alarmProperties;
    }
    
    /**
     * 根据过滤器id查询重大告警数量
     * @param alarmStatisticForCockpitDto filter
     * @return map
     */
    @Override
    public AlarmResponse<Map<String, Integer>> getMajorAlarmCountByFilter(AlarmTypeFilterStatisticDTO alarmStatisticForCockpitDto) {
        log.info("请求告警getMajorAlarmCountByFilter接口的入参：{}", alarmStatisticForCockpitDto.toString());
        String url = alarmProperties.getMajorAlarmCount();
        
        AlarmResponse<Map<String, Integer>> response = restClient.post()
                .uri(url)
                .header("Cookie", "bk_token=" + UserUtils.getToken())
                .body(alarmStatisticForCockpitDto)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<AlarmResponse<Map<String, Integer>>>() {
                });
        
        response.validateAlarmResponse();
        
        return response;
    }
    
    /**
     * 根据过滤器id查询告警级别统计信息
     * @param startTime 发生时间
     * @param endTime   发生时间
     * @param filterId  过滤器id
     * @return AlarmResponse
     */
    @Override
    public AlarmResponse<AlarmLevelFilterStatisticInfos> getLevelFilterStatistic(String startTime, String endTime, String filterId) {
        log.info("请求告警getLevelFilterStatistic接口的入参的过滤器id：{}", filterId);
        String url = alarmProperties.getAlarmLevelFilterStatistic();
        
        AlarmResponse<AlarmLevelFilterStatisticInfos> response = restClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path(url)
                        .queryParam("startTime", startTime)
                        .queryParam("endTime", endTime)
                        .queryParam("filterId", filterId)
                        .build())
                .header("Cookie", "bk_token=" + UserUtils.getToken())
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<AlarmResponse<AlarmLevelFilterStatisticInfos>>() {
                });
        
        if (response != null) {
            response.validateAlarmResponse();
        }
        
        return response;
    }
    
    /**
     * 查询作战室列表
     * @param faultSceneListQueryDTO fault
     * @return 故障列表
     */
    @Override
    public AlarmResponse<FaultScenePageListInfos> queryFaultSceneList(FaultSceneListQueryDTO faultSceneListQueryDTO) {
        log.info("请求调度queryFaultSceneList接口的入参：{}", faultSceneListQueryDTO.toString());
        String url = alarmProperties.getQueryFaultSceneListUrl();
        AlarmResponse<FaultScenePageListInfos> response = restClient.get()
                
                .uri(uriBuilder -> uriBuilder
                        .path(url)
                        .queryParam("faultSceneNo", faultSceneListQueryDTO.getFaultSceneNo())
                        .queryParam("faultSceneName", faultSceneListQueryDTO.getFaultSceneName())
                        .queryParam("createStartTime", faultSceneListQueryDTO.getCreateStartTime())
                        .queryParam("createEndTime", faultSceneListQueryDTO.getCreateEndTime())
                        .queryParam("faultSceneStatus", faultSceneListQueryDTO.getFaultSceneStatus())
                        .queryParam("page", faultSceneListQueryDTO.getPage())
                        .queryParam("size", faultSceneListQueryDTO.getSize())
                        .build())
                .header("Cookie", "bk_token=" + UserUtils.getToken())
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        if (response != null) {
            response.validateAlarmResponse();
        }
        
        return response;
    }
    
    /**
     * 活动告警统计查询
     * @param filterSearchView filter
     * @return map
     */
    @Override
    public AlarmResponse<Map<String, Integer[]>> getAlarmNum(FilterSearchView filterSearchView) {
        log.info("请求活动告警/active/level/statistic接口的入参：{}", filterSearchView.toString());
        String url = alarmProperties.getGetAlarmNumUrl();
        
        AlarmResponse<Map<String, Integer[]>> response = restClient.put()
                .uri(url)
                .header("Cookie", "bk_token=" + UserUtils.getToken())
                .body(filterSearchView)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        
        response.validateAlarmResponse();
        
        return response;
    }
    
}
