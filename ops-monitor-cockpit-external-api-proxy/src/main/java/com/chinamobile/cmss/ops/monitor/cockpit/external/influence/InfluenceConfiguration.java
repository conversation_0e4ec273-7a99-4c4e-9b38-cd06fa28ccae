/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.influence;

import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.config.InfluenceProperties;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.config.SchedulerClientHttpRequestInterceptor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.web.client.RestClient;

@Configuration
@EnableConfigurationProperties(InfluenceProperties.class)
@ComponentScan
public class InfluenceConfiguration {
    
    private final InfluenceProperties influenceProperties;
    
    public InfluenceConfiguration(InfluenceProperties influenceProperties) {
        this.influenceProperties = influenceProperties;
    }
    
    /**
     * scheduler Rest Client
     *
     * @return RestClient
     */
    @Bean("influenceRestClient")
    public RestClient restClient() {
        return RestClient.builder()
                .baseUrl(influenceProperties.getBaseUrl())
                .requestInterceptor(clientHttpRequestInterceptor())
                .build();
    }
    
    /**
     * ClientHttpRequestInterceptor
     *
     * @return ClientHttpRequestInterceptor
     */
    @Bean
    public ClientHttpRequestInterceptor clientHttpRequestInterceptor() {
        return new SchedulerClientHttpRequestInterceptor();
    }
}
