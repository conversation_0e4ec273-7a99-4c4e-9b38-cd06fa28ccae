/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.service;

import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.dto.DateDutySelectByDayRangeDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.AESGCMBase64KeyInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.CockpitFaultSceneDetail;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.DateDutySelectByDayRangeResponseInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.DutyTemplateInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.SchedulerResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.MsgAuditTaskNum;

import java.util.List;

/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2023
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */
/*
 * description 故障调度对接Service
 * @Author: lingmeng
 * @Date:  2025年04月10日 16:53:08
 */
public interface SchedulerService {
    
    /**
     * 获取驾驶舱的活动作战室列表
     * @return CockpitFaultSceneDetail
     */
    SchedulerResponse<List<CockpitFaultSceneDetail>> querySceneListForCockpit();
    
    /**
     * 获取驾驶舱的消息待办审批数目
     * @return CockpitMsgAuditTaskNum
     */
    SchedulerResponse<MsgAuditTaskNum> queryAuditTaskNum();
    
    /**
     * 依据日期范围查询日期排班数据
     * @param dateDutySelectByDayRangeDTO dto
     * @return paiban
     */
    SchedulerResponse<DateDutySelectByDayRangeResponseInfos> selectByDayRange(DateDutySelectByDayRangeDTO dateDutySelectByDayRangeDTO);
    
    /**
     * 查询值班模板列表
     * @param disable      是否启用
     * @param templateName 模板名称
     * @param fullName     全程
     * @return list
     */
    SchedulerResponse<List<DutyTemplateInfos>> querySchedulingTemplateList(Integer disable, String templateName, String fullName);
    
    /**
     * 密钥
     * @return 秘钥
     */
    SchedulerResponse<AESGCMBase64KeyInfos> queryAESGCMBase64Key();
}
