/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.influence.pojo;

import com.chinamobile.cmss.ops.monitor.cockpit.external.exception.InfluenceApiException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * InfluenceResponse
 * @param <T> body
 */
@Data
@Slf4j
public class InfluenceResponse<T> {
    
    /**
     * 请求成功与否。true:请求成功；false请求失败
     */
    private boolean result;
    
    /**
     * 请求返回的数据
     */
    private T data;
    
    /**
     * validateInfluenceResponse
     * @throws InfluenceApiException InfluenceApiException
     */
    public void validateInfluenceResponse() {
        
        if (!this.isResult()) {
            log.error("调用influence接口失败");
            throw new InfluenceApiException("调用influence接口失败", String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        } else {
            log.info("调用influence接口成功，result:{}", this.getData());
        }
    }
}
