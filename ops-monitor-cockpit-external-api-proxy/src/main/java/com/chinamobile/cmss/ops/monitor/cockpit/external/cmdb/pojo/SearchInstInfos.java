/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */

@Data
public class SearchInstInfos implements Serializable {
    
    /**
     * 记录条数
     */
    private Long count;
    
    /**
     * 模型实例实际数据
     */
    private List<SearchInstInfo> info;
    
    @Data
    public static class SearchInstInfo {
        
        private String status;
        
        private String city;
        
        private String code;
        
        private String description;
        
        @JsonProperty("resource_id")
        private String resourceId;
        
        private String address;
        
        private Float longitude;
        
        @JsonProperty("bk_obj_id")
        private String bkObjId;
        
        @JsonProperty("operation_type")
        private String operationType;
        
        @JsonProperty("create_time")
        private String createTime;
        
        @JsonProperty("pool_type")
        private String poolType;
        
        @JsonProperty("bk_supplier_account")
        private String bkSupplierAccount;
        
        private Float latitude;
        
        @JsonProperty("bk_inst_id")
        private Integer bkInstId;
        
        @JsonProperty("is_main_az")
        private Boolean isMainAz;
        
        @JsonProperty("last_time")
        private String lastTime;
        
        @JsonProperty("bk_inst_name")
        private String bkInstName;
        
        private String uuid;
        
        private String province;
        
    }
    
}
