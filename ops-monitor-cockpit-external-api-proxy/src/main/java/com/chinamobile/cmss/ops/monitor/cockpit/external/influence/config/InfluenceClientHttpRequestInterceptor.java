/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.influence.config;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;

@Slf4j
public class InfluenceClientHttpRequestInterceptor implements ClientHttpRequestInterceptor {
    
    private static final String WATCH_ID = "influence-call-interface";
    
    private static final String WATCH_TASK_NAME_PREFIX = "influence-";
    
    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        StopWatch watch = new StopWatch(WATCH_ID);
        watch.start(WATCH_TASK_NAME_PREFIX + RandomUtil.randomLong(8));
        log.info("traceId:{},start call influence interface：{}", watch.getId(), request.getURI());
        ClientHttpResponse execute = execution.execute(request, body);
        watch.stop();
        log.info("traceId:{},end call influence interface：{},timeout:{}", watch.getId(), request.getURI(),
                watch.getTotalTimeMillis());
        return execute;
    }
}
