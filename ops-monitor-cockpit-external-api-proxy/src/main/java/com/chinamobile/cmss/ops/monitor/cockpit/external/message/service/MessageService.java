/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.message.service;

import com.chinamobile.cmss.ops.monitor.cockpit.external.message.dto.CloudSpaceMessageSendDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.dto.ShortMessageSendDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.pojo.CloudSpaceMessageResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.pojo.ShortMessageResponse;

/*
 * description 消息中台对接Service
 * @Author: lingmeng
 * @Date:  2025年05月18日 16:53:08
 */
public interface MessageService {
    
    /**
     * 短信发送
     * @param shortMessageSendDTO 短信入参
     * @return ShortMessageResponse
     */
    ShortMessageResponse sendShortMessage(ShortMessageSendDTO shortMessageSendDTO);
    
    /**
     * 云空间发送
     * @param cloudSpaceMessageSendDTO 云空间入参
     * @return CloudSpaceMessageResponse
     */
    CloudSpaceMessageResponse sendCloudSpaceMessage(CloudSpaceMessageSendDTO cloudSpaceMessageSendDTO);
}
