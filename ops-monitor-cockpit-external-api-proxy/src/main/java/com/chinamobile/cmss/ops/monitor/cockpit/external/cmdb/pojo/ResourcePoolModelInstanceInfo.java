/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ResourcePoolModelInstanceInfo implements Serializable {
    
    /**
     * 记录条数
     */
    private Long count;
    
    /**
     * 资源池实际数据
     */
    private List<ResourcePoolInfo> info;
    
    @Data
    public static class ResourcePoolInfo {
        
        /**
         * 编码
         */
        @JsonProperty("code")
        private String code;
        
        /**
         * 资源池实例id
         */
        @JsonProperty("bk_inst_id")
        private Integer bkInstId;
        
        /**
         * 资源池实例名
         */
        @JsonProperty("bk_inst_name")
        private String bkInstName;
        
        /**
         * 资源池id
         */
        @JsonProperty("resource_id")
        private String resourceId;
        
        /**
         * 资源池省份字段
         */
        @JsonProperty("province")
        private String province;
        
        /**
         * 可用区列表
         */
        @JsonProperty("available_zone")
        private List<AvailableZone> azList;
        
        @AllArgsConstructor
        @Builder
        @Data
        public static class AvailableZone {
            
            /**
             * 可用区实例id
             */
            @JsonProperty("bk_inst_id")
            private Integer bkInstId;
            
            /**
             * 可用区实例名
             */
            @JsonProperty("bk_inst_name")
            private String bkInstName;
            
            /**
             * 所属资源池id
             */
            @JsonProperty("resource_id")
            private String resourceId;
            
            /**
             * 编码
             */
            @JsonProperty("code")
            private String code;
        }
    }
}
