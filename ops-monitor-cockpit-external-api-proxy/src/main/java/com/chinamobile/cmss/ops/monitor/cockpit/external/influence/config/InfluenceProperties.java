/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.influence.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: lingmeng @Date: 2025/04/10 11:17 @Description: @Version： 1.0
 */
@ConfigurationProperties(prefix = "ops.monitor.cockpit.influence")
@Data
public class InfluenceProperties {
    
    /**
     * 故障影响面 配置
     */
    private String baseUrl;
    
    /**
     * 故障数目统计（较大及以上故障，一般故障） url
     */
    private String faultLevelStatisticsRemoteUrl =
            "/ops-foc/fault-analyse/listing/active/aggregate_board/";
}
