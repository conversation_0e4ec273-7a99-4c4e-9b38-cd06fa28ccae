/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo;

import com.chinamobile.cmss.ops.monitor.cockpit.external.exception.AlarmApiException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警系统响应
 * @param <T> 响应数据类型
 */
@Data
@Slf4j
public class AlarmResponse<T> {
    
    private Meta meta;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 验证响应是否成功
     * @throws AlarmApiException 告警API异常
     */
    public void validateAlarmResponse() {
        if (!"20000".equals(this.getMeta().getCode())) {
            log.error("调用告警接口失败,code:{},message:{}", this.meta.code, this.meta.getMessage());
            throw new AlarmApiException(this.meta.message, String.valueOf(this.meta.code));
        } else {
            log.info("调用告警接口成功，result:{}", this.getData());
        }
    }
    
    @Data
    public static class Meta {
        
        /**
         * 编码
         */
        private String code;
        
        /**
         * 消息
         */
        private String message;
    }
}
