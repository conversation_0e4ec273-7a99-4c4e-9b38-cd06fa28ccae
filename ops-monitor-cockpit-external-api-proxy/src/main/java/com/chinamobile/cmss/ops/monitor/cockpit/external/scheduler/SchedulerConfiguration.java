/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler;

import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.config.SchedulerClientHttpRequestInterceptor;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.config.SchedulerProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.web.client.RestClient;

@Configuration
@EnableConfigurationProperties(SchedulerProperties.class)
@ComponentScan
public class SchedulerConfiguration {
    
    private final SchedulerProperties schedulerProperties;
    
    public SchedulerConfiguration(SchedulerProperties schedulerProperties) {
        this.schedulerProperties = schedulerProperties;
    }
    
    /**
     * scheduler Rest Client
     *
     * @return RestClient
     */
    @Bean("schedulerRestClient")
    public RestClient restClient() {
        return RestClient.builder()
                .baseUrl(schedulerProperties.getBaseUrl())
                .requestInterceptor(clientHttpRequestInterceptor())
                .build();
    }
    
    /**
     * ClientHttpRequestInterceptor
     *
     * @return ClientHttpRequestInterceptor
     */
    @Bean("schedulerClientHttpRequestInterceptor")
    public ClientHttpRequestInterceptor clientHttpRequestInterceptor() {
        return new SchedulerClientHttpRequestInterceptor();
    }
    
}
