/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.message.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 *  @author: lingmeng
 *  @Date: 2025/05/18 11:17
 *  @Description:
 *  @Version: 1.0
 * */
@ConfigurationProperties(prefix = "ops.monitor.cockpit.message")
@Data
public class MessageProperties {
    
    /** 泰岳消息中台 配置 */
    private String baseUrl;
    
    /** 泰岳消息中台 key配置 */
    private String key;
    
    /** 泰岳消息中台 token配置 */
    private String token;
    
    /** 泰岳消息中台 源系统配置 */
    private String sourceSystem;
    
    /** 泰岳短信发送 url */
    private String shortMessageRemoteUrl = "/msn/v1.1/rest/tenant/sms/send";
    
    /** 泰岳云空间发送 url */
    private String cloudSpaceMessageRemoteUrl = "/msn/v1.1/rest/tenant/app/send/message";
}
