/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.message.pojo;

import com.chinamobile.cmss.ops.monitor.cockpit.common.constant.MessageConstant;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * CloudSpaceMessageResponse 云空间发送响应
 */
@Data
@Slf4j
public class CloudSpaceMessageResponse {
    
    /**
     * 状态码 0表示成功, 其他表示失败
     */
    private String status;
    
    /**
     * true表示成功, false表示失败
     */
    private String success;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 部分发送失败手机号英文，分隔
     */
    @JsonProperty("segment_fail_mobiles")
    private String segmentFailMobiles;
    
    /**
     * validateCloudSpaceMessageResponse
     */
    public void validateCloudSpaceMessageResponse() {
        
        if (!MessageConstant.CloudSpace.SendResult.SUCCESS_CODE.equals(this.getStatus())) {
            log.error("调用云空间接口失败，message:{}", this.getMessage());
        } else {
            log.info("调用云空间接口成功，message:{}", this.getMessage());
        }
    }
}
