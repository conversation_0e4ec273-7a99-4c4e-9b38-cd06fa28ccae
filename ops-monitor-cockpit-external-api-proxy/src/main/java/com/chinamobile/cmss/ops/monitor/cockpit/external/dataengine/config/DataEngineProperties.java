/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 *  @author: lingmeng
 *  @Date: 2025/05/10 11:17
 *  @Description: 数据治理对接
 *  @Version: 1.0
 * */
@ConfigurationProperties(prefix = "data.queryengine")
@Data
public class DataEngineProperties {
    
    /** 数据治理配置 */
    private String baseUrl;
    
    /** app编码 */
    private String appCode;
    
    /** app秘钥 */
    private String appSecret;
    
    /** 数据认证方法 */
    private String bkdataAuthenticationMethod;
    
    /** 数据令牌 */
    private String dataToken;
}
