/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.service;

import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.dto.DataEngineQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.DataEngineResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.WorkOrderNumStatistic;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.WorkOrderOverview;

/**
 * DataEngine Service
 */
public interface DataEngineService {
    
    /**
     * 工单督办-获取故障/事件工单列表
     * @param dataEngineQueryDTO dataEngineQueryDTO
     * @return WorkOrderOverview
     */
    DataEngineResponse<WorkOrderOverview> getSuperviseWorkOrderList(DataEngineQueryDTO dataEngineQueryDTO);
    
    /**
     * 获取某种类型的匹配某些状态的工单数目
     * @param dataEngineQueryDTO dataEngineQueryDTO
     * @return WorkOrderNumStatistic
     */
    DataEngineResponse<WorkOrderNumStatistic> getWorkOrderNum(DataEngineQueryDTO dataEngineQueryDTO);
    
    /**
     * 查询数据治理
     * @param dataEngineQueryDTO data
     * @param <T>                泛型
     * @return 泛型
     */
    <T> DataEngineResponse<T> requestDataEngine(DataEngineQueryDTO dataEngineQueryDTO);
}
