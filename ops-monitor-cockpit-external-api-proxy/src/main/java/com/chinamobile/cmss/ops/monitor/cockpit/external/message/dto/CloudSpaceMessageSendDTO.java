/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.message.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CloudSpaceMessageSendDTO {
    
    /**
     * 消息点击跳转地址
     */
    private String action;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 会话列表透出的标题
     */
    private String message;
    
    /**
     * 消息标题
     */
    private String title;
    
    /**
     * 待发送手机号,多个用英文,隔开
     */
    private String userMobiles;
    
    /**
     * 消息分类名称
     */
    private String categoryName;
    
    /**
     * 消息发送者名称
     */
    private String sender;
    
    /**
     * 消息类型枚举值：0、1     0:文本消息;1:应用消息 空或不传：应用消息
     */
    private String msgType;
    
    /**
     * 消息发送者名称 枚举值：外协、自有 当msgType为应用消息时， 入参senduserType无效，该参数的获取接口内部实现（取userMobiles对应ops账号的用户类型）
     */
    private String sendUserType;
    
    /**
     * 失败后是否需要重推短信 枚举值：是、否
     */
    private String rstSend;
}
