/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb;

import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.config.CmdbClientHttpRequestInterceptor;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.config.CmdbProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.web.client.RestClient;

/**
 * Cmdb Configuration
 */
@Configuration
@EnableConfigurationProperties(CmdbProperties.class)
@ComponentScan
public class CmdbConfiguration {
    
    private final CmdbProperties cmdbProperties;
    
    public CmdbConfiguration(CmdbProperties cmdbProperties) {
        this.cmdbProperties = cmdbProperties;
    }
    
    /**
     * Cmdb Rest Client
     *
     * @return RestClient
     */
    @Bean("cmdbRestClient")
    public RestClient restClient() {
        return RestClient.builder()
                .baseUrl(cmdbProperties.getBaseUrl())
                .requestInterceptor(clientHttpRequestInterceptor())
                .build();
    }
    
    /**
     * ClientHttpRequestInterceptor
     *
     * @return ClientHttpRequestInterceptor
     */
    @Bean("cmdbClientHttpRequestInterceptor")
    public ClientHttpRequestInterceptor clientHttpRequestInterceptor() {
        return new CmdbClientHttpRequestInterceptor();
    }
}
