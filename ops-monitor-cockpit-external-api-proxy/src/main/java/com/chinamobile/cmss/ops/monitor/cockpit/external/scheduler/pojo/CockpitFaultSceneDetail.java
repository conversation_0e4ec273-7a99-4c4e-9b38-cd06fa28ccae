/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CockpitFaultSceneDetail implements Serializable {
    
    /**
     * 作战室UUID
     */
    private String sceneId;
    
    /**
     * 作战室ID
     */
    private String sceneNo;
    
    /**
     * 作战室名称
     */
    private String sceneName;
    
    /**
     * 故障等级名称，0-普通事件，1-一般故障，2-较大故障，3-严重故障，4-重大故障
     */
    private String faultGradeName;
    
    /**
     * 关联工单号，分号分隔
     */
    private String relatedOrderNo;
    
    /**
     * 发现时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date findTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 内部消息最新发送时间创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date innerLastMessageSendTime;
    
    /**
     * 外部消息最新发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date externalLastMessageSendTime;
    
    /**
     * 信息栏
     */
    @JsonProperty("cockpitInfoBarItemVOList")
    private List<CockpitInfoBarItem> cockpitInfoBarItemList;
    
}
