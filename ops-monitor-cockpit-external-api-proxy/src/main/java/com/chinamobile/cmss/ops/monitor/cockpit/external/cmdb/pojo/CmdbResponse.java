/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import com.chinamobile.cmss.ops.monitor.cockpit.external.exception.CmdbApiException;

/**
 * CmdbResponse
 * @param <T> body
 */
@Data
@Slf4j
public class CmdbResponse<T> {
    
    /**
     * 请求成功与否。true:请求成功；false请求失败
     */
    private boolean result;
    
    /**
     * 错误编码。 0表示success，>0表示失败错误
     */
    private int code;
    
    /**
     * 请求失败返回的错误信息
     */
    private String message;
    
    /**
     * 权限信息
     */
    private Object permission;
    
    /**
     * 请求链id
     */
    @JsonProperty("request_id")
    private String requestId;
    
    /**
     * 请求返回的数据
     */
    private T data;
    
    /**
     * validateCmdbResponse
     * @throws CmdbApiException CmdbApiException
     */
    public void validateCmdbResponse() {
        
        if (!this.isResult()) {
            log.error("调用cmdb接口失败,code:{},message:{}", this.code, this.getMessage());
            throw new CmdbApiException(this.getMessage(), String.valueOf(this.code));
        }
    }
}
