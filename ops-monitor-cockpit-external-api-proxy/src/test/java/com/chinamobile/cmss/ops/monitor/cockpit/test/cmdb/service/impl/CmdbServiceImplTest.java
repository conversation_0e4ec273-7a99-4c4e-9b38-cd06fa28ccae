/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.test.cmdb.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.config.CmdbProperties;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto.SearchInstanceByObjectDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.CmdbResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.ResourcePoolModelInstanceInfo;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.service.impl.CmdbServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestClient;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * CmdbServiceImpl 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class CmdbServiceImplTest {
    
    @Mock
    private RestClient restClient;
    
    @Mock
    private CmdbProperties cmdbProperties;
    
    @Mock
    private RestClient.RequestBodyUriSpec requestBodyUriSpec;
    
    @Mock
    private RestClient.RequestBodySpec requestBodySpec;
    
    @Mock
    private RestClient.ResponseSpec responseSpec;
    
    @InjectMocks
    private CmdbServiceImpl cmdbService;
    
    private SearchInstanceByObjectDTO mockSearchDto;
    
    private CmdbResponse<ResourcePoolModelInstanceInfo> mockCmdbResponse;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockSearchDto = SearchInstanceByObjectDTO.builder()
                .bkObjId("resource_pool")
                .fields(Arrays.asList("bk_inst_id", "bk_inst_name"))
                .build();
        
        // 初始化 CMDB 响应
        mockCmdbResponse = new CmdbResponse<>();
        mockCmdbResponse.setResult(true);
        mockCmdbResponse.setCode(0);
        mockCmdbResponse.setMessage("success");
        mockCmdbResponse.setRequestId("test-request-id");
        
        ResourcePoolModelInstanceInfo resourcePoolInfo = new ResourcePoolModelInstanceInfo();
        mockCmdbResponse.setData(resourcePoolInfo);
    }
    
    /**
     * 测试获取资源池列表
     */
    @Test
    void testGetResourcePoolInfoList() {
        // 模拟配置属性
        when(cmdbProperties.getBkAppCode()).thenReturn("test-app-code");
        when(cmdbProperties.getBkAppSecret()).thenReturn("test-app-secret");
        when(cmdbProperties.getBkUsername()).thenReturn("test-username");
        when(cmdbProperties.getSearchInstanceByObjectUrl()).thenReturn("/api/v3/search/instance/object");
        
        // 模拟 RestClient 调用链
        when(restClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
        when(requestBodySpec.body(any(SearchInstanceByObjectDTO.class))).thenReturn(requestBodySpec);
        when(requestBodySpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
        when(requestBodySpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(mockCmdbResponse);
        
        // 执行测试
        CmdbResponse<ResourcePoolModelInstanceInfo> result = cmdbService.getResourcePoolInfoList(mockSearchDto);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isResult());
        assertEquals(0, result.getCode());
        assertEquals("success", result.getMessage());
        assertEquals("test-request-id", result.getRequestId());
        assertNotNull(result.getData());
        
        // 验证 DTO 的属性设置
        assertEquals("test-app-code", mockSearchDto.getBkAppCode());
        assertEquals("test-app-secret", mockSearchDto.getBkAppSecret());
        assertEquals("test-username", mockSearchDto.getBkUsername());
        
        // 验证方法调用
        verify(restClient, times(1)).post();
        verify(requestBodyUriSpec, times(1)).uri("/api/v3/search/instance/object");
        verify(requestBodySpec, times(1)).body(mockSearchDto);
        verify(requestBodySpec, times(1)).accept(MediaType.APPLICATION_JSON);
    }
    
    /**
     * 测试获取资源池列表 - 使用空用户名
     */
    @Test
    void testGetResourcePoolInfoListWithEmptyUsername() {
        // 模拟配置属性
        when(cmdbProperties.getBkAppCode()).thenReturn("test-app-code");
        when(cmdbProperties.getBkAppSecret()).thenReturn("test-app-secret");
        when(cmdbProperties.getBkUsername()).thenReturn("");
        when(cmdbProperties.getSearchInstanceByObjectUrl()).thenReturn("/api/v3/search/instance/object");
        
        // 模拟 RestClient 调用链
        when(restClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
        when(requestBodySpec.body(any(SearchInstanceByObjectDTO.class))).thenReturn(requestBodySpec);
        when(requestBodySpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
        when(requestBodySpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(mockCmdbResponse);
        
        // 执行测试
        CmdbResponse<ResourcePoolModelInstanceInfo> result = cmdbService.getResourcePoolInfoList(mockSearchDto);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isResult());
        
        // 验证 DTO 的属性设置 - 空用户名时不应设置 bkUsername（因为StringUtils.isNotBlank("")为false）
        assertEquals("test-app-code", mockSearchDto.getBkAppCode());
        assertEquals("test-app-secret", mockSearchDto.getBkAppSecret());
        assertEquals(null, mockSearchDto.getBkUsername());
        // 验证方法调用
        verify(restClient, times(1)).post();
    }
    
    /**
     * 测试获取资源池列表 - 使用null用户名
     */
    @Test
    void testGetResourcePoolInfoListWithNullUsername() {
        // 模拟配置属性
        when(cmdbProperties.getBkAppCode()).thenReturn("test-app-code");
        when(cmdbProperties.getBkAppSecret()).thenReturn("test-app-secret");
        when(cmdbProperties.getBkUsername()).thenReturn(null);
        when(cmdbProperties.getSearchInstanceByObjectUrl()).thenReturn("/api/v3/search/instance/object");
        
        // 模拟 RestClient 调用链
        when(restClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
        when(requestBodySpec.body(any(SearchInstanceByObjectDTO.class))).thenReturn(requestBodySpec);
        when(requestBodySpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
        when(requestBodySpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(mockCmdbResponse);
        
        // 执行测试
        CmdbResponse<ResourcePoolModelInstanceInfo> result = cmdbService.getResourcePoolInfoList(mockSearchDto);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isResult());
        
        // 验证 DTO 的属性设置
        assertEquals("test-app-code", mockSearchDto.getBkAppCode());
        assertEquals("test-app-secret", mockSearchDto.getBkAppSecret());
        assertEquals(null, mockSearchDto.getBkUsername());
        
        // 验证方法调用
        verify(restClient, times(1)).post();
    }
    
    /**
     * 测试获取资源池列表 - 验证日志记录
     */
    @Test
    void testGetResourcePoolInfoListLogging() {
        // 模拟配置属性
        when(cmdbProperties.getBkAppCode()).thenReturn("test-app-code");
        when(cmdbProperties.getBkAppSecret()).thenReturn("test-app-secret");
        when(cmdbProperties.getBkUsername()).thenReturn("test-username");
        when(cmdbProperties.getSearchInstanceByObjectUrl()).thenReturn("/api/v3/search/instance/object");
        
        // 模拟 RestClient 调用链
        when(restClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
        when(requestBodySpec.body(any(SearchInstanceByObjectDTO.class))).thenReturn(requestBodySpec);
        when(requestBodySpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
        when(requestBodySpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(mockCmdbResponse);
        
        // 执行测试
        CmdbResponse<ResourcePoolModelInstanceInfo> result = cmdbService.getResourcePoolInfoList(mockSearchDto);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isResult());
        
        // 验证方法调用次数
        verify(cmdbProperties, times(1)).getBkAppCode();
        verify(cmdbProperties, times(1)).getBkAppSecret();
        verify(cmdbProperties, times(1)).getBkUsername();
        verify(cmdbProperties, times(1)).getSearchInstanceByObjectUrl();
    }
}
