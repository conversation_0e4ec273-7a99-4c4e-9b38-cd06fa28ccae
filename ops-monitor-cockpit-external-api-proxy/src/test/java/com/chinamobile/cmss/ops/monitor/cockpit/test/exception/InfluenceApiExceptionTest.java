/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.test.exception;

import com.chinamobile.cmss.ops.monitor.cockpit.external.exception.InfluenceApiException;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * InfluenceApiException 的单元测试类
 */
public class InfluenceApiExceptionTest {
    
    /**
     * 测试InfluenceApiException构造函数
     */
    @Test
    void testInfluenceApiExceptionConstructor() {
        // 测试数据
        String message = "影响面API调用失败";
        String code = "70001";
        
        // 创建异常实例
        InfluenceApiException exception = new InfluenceApiException(message, code);
        
        // 验证异常属性
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
    
    /**
     * 测试InfluenceApiException继承关系
     */
    @Test
    void testInfluenceApiExceptionInheritance() {
        // 测试数据
        String message = "影响面API异常";
        String code = "70002";
        
        // 创建异常实例
        InfluenceApiException exception = new InfluenceApiException(message, code);
        
        // 验证继承关系
        assertNotNull(exception);
        assertEquals(RuntimeException.class, exception.getClass().getSuperclass().getSuperclass());
    }
    
    /**
     * 测试InfluenceApiException空消息
     */
    @Test
    void testInfluenceApiExceptionWithEmptyMessage() {
        // 测试数据
        String message = "";
        String code = "70003";
        
        // 创建异常实例
        InfluenceApiException exception = new InfluenceApiException(message, code);
        
        // 验证异常属性
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
    
    /**
     * 测试InfluenceApiException空代码
     */
    @Test
    void testInfluenceApiExceptionWithEmptyCode() {
        // 测试数据
        String message = "影响面API异常";
        String code = "";
        
        // 创建异常实例
        InfluenceApiException exception = new InfluenceApiException(message, code);
        
        // 验证异常属性
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
    
    /**
     * 测试InfluenceApiException null值
     */
    @Test
    void testInfluenceApiExceptionWithNullValues() {
        // 测试数据
        String message = null;
        String code = null;
        
        // 创建异常实例
        InfluenceApiException exception = new InfluenceApiException(message, code);
        
        // 验证异常属性
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
}
