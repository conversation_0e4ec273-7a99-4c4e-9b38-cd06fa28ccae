/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.test.alarm.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.UserUtils;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.config.AlarmProperties;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto.AlarmTypeFilterStatisticDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmLevelFilterStatisticInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.service.impl.AlarmServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestClient;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AlarmServiceImpl 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class AlarmServiceImplTest {
    
    @Mock
    private RestClient restClient;
    
    @Mock
    private AlarmProperties alarmProperties;
    
    @Mock
    private RestClient.RequestBodyUriSpec requestBodyUriSpec;
    
    @Mock
    private RestClient.RequestBodySpec requestBodySpec;
    
    @Mock
    private RestClient.RequestHeadersSpec requestHeadersSpec;
    
    @Mock
    private RestClient.ResponseSpec responseSpec;
    
    @Mock
    private RestClient.RequestHeadersUriSpec requestHeadersUriSpec;
    
    @InjectMocks
    private AlarmServiceImpl alarmService;
    
    private AlarmTypeFilterStatisticDTO mockDto;
    
    private AlarmResponse<Map<String, Integer>> mockMajorAlarmResponse;
    
    private AlarmResponse<AlarmLevelFilterStatisticInfos> mockLevelFilterResponse;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockDto = AlarmTypeFilterStatisticDTO.builder()
                .filterIds(Arrays.asList("filter1", "filter2"))
                .startTime("2023-01-01")
                .endTime("2023-01-31")
                .build();
        
        // 初始化重大告警响应
        mockMajorAlarmResponse = new AlarmResponse<>();
        AlarmResponse.Meta meta = new AlarmResponse.Meta();
        meta.setCode("20000");
        meta.setMessage("success");
        mockMajorAlarmResponse.setMeta(meta);
        
        Map<String, Integer> alarmCountMap = new HashMap<>();
        alarmCountMap.put("filter1", 10);
        alarmCountMap.put("filter2", 5);
        mockMajorAlarmResponse.setData(alarmCountMap);
        
        // 初始化告警级别过滤统计响应
        mockLevelFilterResponse = new AlarmResponse<>();
        mockLevelFilterResponse.setMeta(meta);
        
        AlarmLevelFilterStatisticInfos statisticInfos = new AlarmLevelFilterStatisticInfos();
        statisticInfos.setFilterId("filter1");
        statisticInfos.setMajorAlarmCount(10);
        statisticInfos.setImportantAlarmCount(5);
        statisticInfos.setSecondARYAlarmCount(3);
        mockLevelFilterResponse.setData(statisticInfos);
    }
    
    /**
     * 测试根据过滤器ID查询重大告警数量
     */
    @Test
    void testGetMajorAlarmCountByFilter() {
        // 模拟配置属性
        when(alarmProperties.getMajorAlarmCount()).thenReturn("/api/fms/search/cockpit/type/filter/statistic");
        
        // 模拟 UserUtils.getToken() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getToken).thenReturn("test-token");
            
            // 模拟 RestClient 调用链
            when(restClient.post()).thenReturn(requestBodyUriSpec);
            when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
            when(requestBodySpec.header(anyString(), anyString())).thenReturn(requestBodySpec);
            when(requestBodySpec.body(any(AlarmTypeFilterStatisticDTO.class))).thenReturn(requestBodySpec);
            when(requestBodySpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
            when(requestBodySpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(mockMajorAlarmResponse);
            
            // 执行测试
            AlarmResponse<Map<String, Integer>> result = alarmService.getMajorAlarmCountByFilter(mockDto);
            
            // 验证结果
            assertNotNull(result);
            assertEquals("20000", result.getMeta().getCode());
            assertEquals("success", result.getMeta().getMessage());
            assertNotNull(result.getData());
            assertEquals(2, result.getData().size());
            assertEquals(10, result.getData().get("filter1"));
            assertEquals(5, result.getData().get("filter2"));
            
            // 验证方法调用
            verify(restClient, times(1)).post();
            verify(requestBodyUriSpec, times(1)).uri(anyString());
            verify(requestBodySpec, times(1)).header(eq("Cookie"), eq("bk_token=test-token"));
            verify(requestBodySpec, times(1)).body(mockDto);
        }
    }
    
    /**
     * 测试根据过滤器ID查询告警级别统计信息
     */
    @Test
    void testGetLevelFilterStatistic() {
        // 模拟配置属性
        when(alarmProperties.getAlarmLevelFilterStatistic()).thenReturn("/api/fms/search/cockpit/level/filter/statistic");
        
        // 模拟 UserUtils.getToken() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getToken).thenReturn("test-token");
            
            // 模拟 RestClient 调用链
            when(restClient.get()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(any(java.util.function.Function.class))).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.header(anyString(), anyString())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(mockLevelFilterResponse);
            
            // 执行测试
            AlarmResponse<AlarmLevelFilterStatisticInfos> result = alarmService.getLevelFilterStatistic(
                    "2023-01-01", "2023-01-31", "filter1");
            
            // 验证结果
            assertNotNull(result);
            assertEquals("20000", result.getMeta().getCode());
            assertEquals("success", result.getMeta().getMessage());
            assertNotNull(result.getData());
            assertEquals("filter1", result.getData().getFilterId());
            assertEquals(10, result.getData().getMajorAlarmCount());
            assertEquals(5, result.getData().getImportantAlarmCount());
            assertEquals(3, result.getData().getSecondARYAlarmCount());
            
            // 验证方法调用
            verify(restClient, times(1)).get();
            verify(requestHeadersUriSpec, times(1)).uri(any(java.util.function.Function.class));
            verify(requestHeadersSpec, times(1)).header(eq("Cookie"), eq("bk_token=test-token"));
        }
    }
    
    /**
     * 测试查询告警级别统计信息 - 响应为null
     */
    @Test
    void testGetLevelFilterStatisticWithNullResponse() {
        // 模拟配置属性
        when(alarmProperties.getAlarmLevelFilterStatistic()).thenReturn("/api/fms/search/cockpit/level/filter/statistic");
        
        // 模拟 UserUtils.getToken() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getToken).thenReturn("test-token");
            
            // 模拟 RestClient 调用链返回 null
            when(restClient.get()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(any(java.util.function.Function.class))).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.header(anyString(), anyString())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(null);
            
            // 执行测试
            AlarmResponse<AlarmLevelFilterStatisticInfos> result = alarmService.getLevelFilterStatistic(
                    "2023-01-01", "2023-01-31", "filter1");
            
            // 验证结果
            assertEquals(null, result);
            
            // 验证方法调用
            verify(restClient, times(1)).get();
        }
    }
    
    /**
     * 测试getMajorAlarmCountByFilter方法 - 验证响应验证调用
     */
    @Test
    void testGetMajorAlarmCountByFilterValidation() {
        // 模拟配置属性
        when(alarmProperties.getMajorAlarmCount()).thenReturn("/api/fms/search/cockpit/type/filter/statistic");
        
        // 创建一个可以验证validateAlarmResponse调用的spy对象
        AlarmResponse<Map<String, Integer>> spyResponse = Mockito.spy(mockMajorAlarmResponse);
        
        // 模拟 UserUtils.getToken() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getToken).thenReturn("test-token");
            
            // 模拟 RestClient 调用链
            when(restClient.post()).thenReturn(requestBodyUriSpec);
            when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
            when(requestBodySpec.header(anyString(), anyString())).thenReturn(requestBodySpec);
            when(requestBodySpec.body(any(AlarmTypeFilterStatisticDTO.class))).thenReturn(requestBodySpec);
            when(requestBodySpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
            when(requestBodySpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(spyResponse);
            
            // 执行测试
            AlarmResponse<Map<String, Integer>> result = alarmService.getMajorAlarmCountByFilter(mockDto);
            
            // 验证结果
            assertNotNull(result);
            
            // 验证validateAlarmResponse方法被调用
            verify(spyResponse, times(1)).validateAlarmResponse();
        }
    }
}
