/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.test.alarm.config;

import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.config.AlarmProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * AlarmProperties 的单元测试类
 */
public class AlarmPropertiesTest {
    
    private AlarmProperties alarmProperties;
    
    @BeforeEach
    void setUp() {
        alarmProperties = new AlarmProperties();
    }
    
    /**
     * 测试默认值
     */
    @Test
    void testDefaultValues() {
        // 验证默认URL路径
        assertEquals("/api/fms/search/cockpit/type/filter/statistic", alarmProperties.getMajorAlarmCount());
        assertEquals("/api/fms/search/cockpit/level/filter/statistic", alarmProperties.getAlarmLevelFilterStatistic());
    }
    
    /**
     * 测试baseUrl的getter和setter
     */
    @Test
    void testBaseUrlGetterAndSetter() {
        // 测试设置和获取baseUrl
        String testBaseUrl = "http://test-alarm-api.example.com";
        alarmProperties.setBaseUrl(testBaseUrl);
        assertEquals(testBaseUrl, alarmProperties.getBaseUrl());
        
        // 测试设置空字符串
        alarmProperties.setBaseUrl("");
        assertEquals("", alarmProperties.getBaseUrl());
        
        // 测试设置null
        alarmProperties.setBaseUrl(null);
        assertEquals(null, alarmProperties.getBaseUrl());
    }
    
    /**
     * 测试majorAlarmCount的getter和setter
     */
    @Test
    void testMajorAlarmCountGetterAndSetter() {
        // 测试设置和获取majorAlarmCount
        String testPath = "/api/v2/alarm/major/count";
        alarmProperties.setMajorAlarmCount(testPath);
        assertEquals(testPath, alarmProperties.getMajorAlarmCount());
        
        // 测试设置空字符串
        alarmProperties.setMajorAlarmCount("");
        assertEquals("", alarmProperties.getMajorAlarmCount());
        
        // 测试设置null
        alarmProperties.setMajorAlarmCount(null);
        assertEquals(null, alarmProperties.getMajorAlarmCount());
    }
    
    /**
     * 测试alarmLevelFilterStatistic的getter和setter
     */
    @Test
    void testAlarmLevelFilterStatisticGetterAndSetter() {
        // 测试设置和获取alarmLevelFilterStatistic
        String testPath = "/api/v2/alarm/level/filter";
        alarmProperties.setAlarmLevelFilterStatistic(testPath);
        assertEquals(testPath, alarmProperties.getAlarmLevelFilterStatistic());
        
        // 测试设置空字符串
        alarmProperties.setAlarmLevelFilterStatistic("");
        assertEquals("", alarmProperties.getAlarmLevelFilterStatistic());
        
        // 测试设置null
        alarmProperties.setAlarmLevelFilterStatistic(null);
        assertEquals(null, alarmProperties.getAlarmLevelFilterStatistic());
    }
    
    /**
     * 测试所有属性的设置
     */
    @Test
    void testAllPropertiesSet() {
        // 设置所有属性
        String baseUrl = "https://alarm-api.example.com";
        String majorAlarmPath = "/api/v3/major/alarms";
        String levelFilterPath = "/api/v3/level/filter";
        
        alarmProperties.setBaseUrl(baseUrl);
        alarmProperties.setMajorAlarmCount(majorAlarmPath);
        alarmProperties.setAlarmLevelFilterStatistic(levelFilterPath);
        
        // 验证所有属性
        assertEquals(baseUrl, alarmProperties.getBaseUrl());
        assertEquals(majorAlarmPath, alarmProperties.getMajorAlarmCount());
        assertEquals(levelFilterPath, alarmProperties.getAlarmLevelFilterStatistic());
    }
    
    /**
     * 测试属性对象创建
     */
    @Test
    void testAlarmPropertiesCreation() {
        // 验证对象创建成功
        assertNotNull(alarmProperties);
        
        // 创建新实例
        AlarmProperties newProperties = new AlarmProperties();
        assertNotNull(newProperties);
        
        // 验证默认值
        assertEquals("/api/fms/search/cockpit/type/filter/statistic", newProperties.getMajorAlarmCount());
        assertEquals("/api/fms/search/cockpit/level/filter/statistic", newProperties.getAlarmLevelFilterStatistic());
    }
    
    /**
     * 测试不同类型的URL设置
     */
    @Test
    void testDifferentUrlTypes() {
        // 测试不同类型的URL
        String[] testUrls = {
                "http://localhost:8080",
                "https://secure-api.example.com:443",
                "http://*************:9090",
                "https://alarm-service.internal"
        };
        
        for (String url : testUrls) {
            alarmProperties.setBaseUrl(url);
            assertEquals(url, alarmProperties.getBaseUrl());
        }
    }
    
    /**
     * 测试路径参数的不同格式
     */
    @Test
    void testDifferentPathFormats() {
        // 测试不同格式的路径
        String[] testPaths = {
                "/api/v1/alarms",
                "/alarm/statistics",
                "api/alarms",
                "/api/v2/alarm/count/",
                ""
        };
        
        for (String path : testPaths) {
            alarmProperties.setMajorAlarmCount(path);
            assertEquals(path, alarmProperties.getMajorAlarmCount());
            
            alarmProperties.setAlarmLevelFilterStatistic(path);
            assertEquals(path, alarmProperties.getAlarmLevelFilterStatistic());
        }
    }
}
