/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.test.cmdb.pojo;

import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.CmdbResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.exception.CmdbApiException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * CmdbResponse 的单元测试类
 */
public class CmdbResponseTest {
    
    private CmdbResponse<String> cmdbResponse;
    
    @BeforeEach
    void setUp() {
        cmdbResponse = new CmdbResponse<>();
        cmdbResponse.setData("test data");
        cmdbResponse.setRequestId("req-123");
        cmdbResponse.setPermission("permission-info");
    }
    
    /**
     * 测试成功响应验证
     */
    @Test
    void testValidateCmdbResponseSuccess() {
        // 设置成功响应
        cmdbResponse.setResult(true);
        cmdbResponse.setCode(0);
        cmdbResponse.setMessage("success");
        
        // 验证不抛出异常
        assertDoesNotThrow(() -> cmdbResponse.validateCmdbResponse());
    }
    
    /**
     * 测试失败响应验证 - result为false
     */
    @Test
    void testValidateCmdbResponseFailureWithFalseResult() {
        // 设置失败响应
        cmdbResponse.setResult(false);
        cmdbResponse.setCode(1001);
        cmdbResponse.setMessage("CMDB API调用失败");
        
        // 验证抛出CmdbApiException
        CmdbApiException exception = assertThrows(CmdbApiException.class,
                () -> cmdbResponse.validateCmdbResponse());
        
        assertEquals("CMDB API调用失败", exception.getMessage());
        assertEquals("1001", exception.getCode());
    }
    
    /**
     * 测试失败响应验证 - 错误代码非零
     */
    @Test
    void testValidateCmdbResponseFailureWithNonZeroCode() {
        // 设置失败响应
        cmdbResponse.setResult(false);
        cmdbResponse.setCode(500);
        cmdbResponse.setMessage("内部服务器错误");
        
        // 验证抛出CmdbApiException
        CmdbApiException exception = assertThrows(CmdbApiException.class,
                () -> cmdbResponse.validateCmdbResponse());
        
        assertEquals("内部服务器错误", exception.getMessage());
        assertEquals("500", exception.getCode());
    }
    
    /**
     * 测试CmdbResponse的getter和setter
     */
    @Test
    void testCmdbResponseGettersAndSetters() {
        CmdbResponse<Integer> response = new CmdbResponse<>();
        
        // 测试result
        response.setResult(true);
        assertTrue(response.isResult());
        
        response.setResult(false);
        assertFalse(response.isResult());
        
        // 测试code
        response.setCode(200);
        assertEquals(200, response.getCode());
        
        // 测试message
        response.setMessage("测试消息");
        assertEquals("测试消息", response.getMessage());
        
        // 测试permission
        response.setPermission("test-permission");
        assertEquals("test-permission", response.getPermission());
        
        // 测试requestId
        response.setRequestId("test-request-id");
        assertEquals("test-request-id", response.getRequestId());
        
        // 测试data
        response.setData(42);
        assertEquals(Integer.valueOf(42), response.getData());
    }
    
    /**
     * 测试不同数据类型的CmdbResponse
     */
    @Test
    void testCmdbResponseWithDifferentDataTypes() {
        // 测试Boolean类型
        CmdbResponse<Boolean> boolResponse = new CmdbResponse<>();
        boolResponse.setData(true);
        assertEquals(Boolean.TRUE, boolResponse.getData());
        
        // 测试null数据
        CmdbResponse<String> nullResponse = new CmdbResponse<>();
        nullResponse.setData(null);
        assertEquals(null, nullResponse.getData());
    }
    
    /**
     * 测试空消息的失败响应
     */
    @Test
    void testValidateCmdbResponseFailureWithEmptyMessage() {
        // 设置失败响应
        cmdbResponse.setResult(false);
        cmdbResponse.setCode(404);
        cmdbResponse.setMessage("");
        
        // 验证抛出CmdbApiException
        CmdbApiException exception = assertThrows(CmdbApiException.class,
                () -> cmdbResponse.validateCmdbResponse());
        
        assertEquals("", exception.getMessage());
        assertEquals("404", exception.getCode());
    }
    
    /**
     * 测试null消息的失败响应
     */
    @Test
    void testValidateCmdbResponseFailureWithNullMessage() {
        // 设置失败响应
        cmdbResponse.setResult(false);
        cmdbResponse.setCode(503);
        cmdbResponse.setMessage(null);
        
        // 验证抛出CmdbApiException
        CmdbApiException exception = assertThrows(CmdbApiException.class,
                () -> cmdbResponse.validateCmdbResponse());
        
        assertEquals(null, exception.getMessage());
        assertEquals("503", exception.getCode());
    }
}
