/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.test.exception;

import com.chinamobile.cmss.ops.monitor.cockpit.external.exception.CmdbApiException;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * CmdbApiException 的单元测试类
 */
public class CmdbApiExceptionTest {
    
    /**
     * 测试CmdbApiException构造函数
     */
    @Test
    void testCmdbApiExceptionConstructor() {
        // 测试数据
        String message = "CMDB API调用失败";
        String code = "60001";
        
        // 创建异常实例
        CmdbApiException exception = new CmdbApiException(message, code);
        
        // 验证异常属性
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
    
    /**
     * 测试CmdbApiException继承关系
     */
    @Test
    void testCmdbApiExceptionInheritance() {
        // 测试数据
        String message = "CMDB API异常";
        String code = "60002";
        
        // 创建异常实例
        CmdbApiException exception = new CmdbApiException(message, code);
        
        // 验证继承关系
        assertNotNull(exception);
        assertEquals(RuntimeException.class, exception.getClass().getSuperclass().getSuperclass());
    }
    
    /**
     * 测试CmdbApiException空消息
     */
    @Test
    void testCmdbApiExceptionWithEmptyMessage() {
        // 测试数据
        String message = "";
        String code = "60003";
        
        // 创建异常实例
        CmdbApiException exception = new CmdbApiException(message, code);
        
        // 验证异常属性
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
    
    /**
     * 测试CmdbApiException空代码
     */
    @Test
    void testCmdbApiExceptionWithEmptyCode() {
        // 测试数据
        String message = "CMDB API异常";
        String code = "";
        
        // 创建异常实例
        CmdbApiException exception = new CmdbApiException(message, code);
        
        // 验证异常属性
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
    
    /**
     * 测试CmdbApiException null值
     */
    @Test
    void testCmdbApiExceptionWithNullValues() {
        // 测试数据
        String message = null;
        String code = null;
        
        // 创建异常实例
        CmdbApiException exception = new CmdbApiException(message, code);
        
        // 验证异常属性
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
}
