/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.test.influence.pojo;

import com.chinamobile.cmss.ops.monitor.cockpit.external.exception.InfluenceApiException;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.pojo.InfluenceResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * InfluenceResponse 的单元测试类
 */
public class InfluenceResponseTest {
    
    private InfluenceResponse<String> influenceResponse;
    
    @BeforeEach
    void setUp() {
        influenceResponse = new InfluenceResponse<>();
        influenceResponse.setData("test data");
    }
    
    /**
     * 测试成功响应验证
     */
    @Test
    void testValidateInfluenceResponseSuccess() {
        // 设置成功响应
        influenceResponse.setResult(true);
        
        // 验证不抛出异常
        assertDoesNotThrow(() -> influenceResponse.validateInfluenceResponse());
    }
    
    /**
     * 测试失败响应验证 - result为false
     */
    @Test
    void testValidateInfluenceResponseFailureWithFalseResult() {
        // 设置失败响应
        influenceResponse.setResult(false);
        
        // 验证抛出InfluenceApiException
        InfluenceApiException exception = assertThrows(InfluenceApiException.class,
                () -> influenceResponse.validateInfluenceResponse());
        
        assertEquals("调用influence接口失败", exception.getMessage());
        assertEquals(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()), exception.getCode());
    }
    
    /**
     * 测试InfluenceResponse的getter和setter
     */
    @Test
    void testInfluenceResponseGettersAndSetters() {
        InfluenceResponse<Integer> response = new InfluenceResponse<>();
        
        // 测试result
        response.setResult(true);
        assertTrue(response.isResult());
        
        response.setResult(false);
        assertFalse(response.isResult());
        
        // 测试data
        response.setData(42);
        assertEquals(Integer.valueOf(42), response.getData());
    }
    
    /**
     * 测试不同数据类型的InfluenceResponse
     */
    @Test
    void testInfluenceResponseWithDifferentDataTypes() {
        // 测试Boolean类型
        InfluenceResponse<Boolean> boolResponse = new InfluenceResponse<>();
        boolResponse.setData(true);
        assertEquals(Boolean.TRUE, boolResponse.getData());
        
        // 测试null数据
        InfluenceResponse<String> nullResponse = new InfluenceResponse<>();
        nullResponse.setData(null);
        assertEquals(null, nullResponse.getData());
    }
    
    /**
     * 测试验证方法的异常代码
     */
    @Test
    void testValidateInfluenceResponseExceptionCode() {
        // 设置失败响应
        influenceResponse.setResult(false);
        
        // 验证抛出的异常包含正确的HTTP状态码
        InfluenceApiException exception = assertThrows(InfluenceApiException.class,
                () -> influenceResponse.validateInfluenceResponse());
        
        // HttpStatus.INTERNAL_SERVER_ERROR.value() = 500
        assertEquals("500", exception.getCode());
    }
    
    /**
     * 测试多次验证调用
     */
    @Test
    void testMultipleValidationCalls() {
        // 设置成功响应
        influenceResponse.setResult(true);
        
        // 多次调用验证方法
        assertDoesNotThrow(() -> influenceResponse.validateInfluenceResponse());
        assertDoesNotThrow(() -> influenceResponse.validateInfluenceResponse());
        assertDoesNotThrow(() -> influenceResponse.validateInfluenceResponse());
        
        // 改为失败响应
        influenceResponse.setResult(false);
        
        // 多次调用验证方法，都应该抛出异常
        assertThrows(InfluenceApiException.class, () -> influenceResponse.validateInfluenceResponse());
        assertThrows(InfluenceApiException.class, () -> influenceResponse.validateInfluenceResponse());
    }
    
    /**
     * 测试复杂数据类型的InfluenceResponse
     */
    @Test
    void testInfluenceResponseWithComplexDataType() {
        // 创建复杂数据类型的响应
        InfluenceResponse<TestData> complexResponse = new InfluenceResponse<>();
        TestData testData = new TestData("test", 123);
        complexResponse.setData(testData);
        complexResponse.setResult(true);
        
        // 验证数据设置正确
        assertEquals(testData, complexResponse.getData());
        assertEquals("test", complexResponse.getData().getName());
        assertEquals(123, complexResponse.getData().getValue());
        
        // 验证成功响应
        assertDoesNotThrow(() -> complexResponse.validateInfluenceResponse());
    }
    
    /**
     * 测试用的内部类
     */
    private static class TestData {
        
        private String name;
        
        private int value;
        
        TestData(String name, int value) {
            this.name = name;
            this.value = value;
        }
        
        public String getName() {
            return name;
        }
        
        public int getValue() {
            return value;
        }
    }
}
