/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.test.influence.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.UserUtils;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.config.InfluenceProperties;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.pojo.FaultAggregateBoardInfo;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.pojo.InfluenceResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.service.impl.InfluenceServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestClient;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * InfluenceServiceImpl 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class InfluenceServiceImplTest {
    
    @Mock
    private RestClient restClient;
    
    @Mock
    private InfluenceProperties influenceProperties;
    
    @Mock
    private RestClient.RequestHeadersUriSpec requestHeadersUriSpec;
    
    @Mock
    private RestClient.RequestHeadersSpec requestHeadersSpec;
    
    @Mock
    private RestClient.ResponseSpec responseSpec;
    
    @InjectMocks
    private InfluenceServiceImpl influenceService;
    
    private InfluenceResponse<FaultAggregateBoardInfo> mockInfluenceResponse;
    
    private FaultAggregateBoardInfo mockFaultAggregateBoardInfo;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockFaultAggregateBoardInfo = new FaultAggregateBoardInfo();
        mockFaultAggregateBoardInfo.setSelfRelated(5);
        
        // 初始化处理效率信息
        FaultAggregateBoardInfo.HandlingEfficiencyInfo handlingEfficiencyInfo =
                new FaultAggregateBoardInfo.HandlingEfficiencyInfo();
        handlingEfficiencyInfo.setToTimeOut(3);
        handlingEfficiencyInfo.setTimedOut(2);
        handlingEfficiencyInfo.setUntimelyResponse(1);
        mockFaultAggregateBoardInfo.setHandlingEfficiencyInfo(handlingEfficiencyInfo);
        
        // 初始化故障等级信息
        FaultAggregateBoardInfo.FaultLevelInfo faultLevelInfo =
                new FaultAggregateBoardInfo.FaultLevelInfo();
        faultLevelInfo.setGeneralEvent(10);
        faultLevelInfo.setNormalFault(8);
        faultLevelInfo.setSeriousFault(5);
        faultLevelInfo.setMajorFault(3);
        mockFaultAggregateBoardInfo.setFaultLevelInfo(faultLevelInfo);
        
        // 初始化影响面响应
        mockInfluenceResponse = new InfluenceResponse<>();
        mockInfluenceResponse.setResult(true);
        mockInfluenceResponse.setData(mockFaultAggregateBoardInfo);
    }
    
    /**
     * 测试获取工单的故障等级统计信息
     */
    @Test
    void testQueryFaultAggregateBoardInfo() {
        // 模拟配置属性
        when(influenceProperties.getBaseUrl()).thenReturn("http://test-influence-api");
        when(influenceProperties.getFaultLevelStatisticsRemoteUrl()).thenReturn("/api/fault/statistics");
        
        // 模拟 UserUtils.getToken() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getToken).thenReturn("test-token");
            
            // 模拟 RestClient 调用链
            when(restClient.get()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.header(anyString(), anyString())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(mockInfluenceResponse);
            
            // 执行测试
            InfluenceResponse<FaultAggregateBoardInfo> result = influenceService.queryFaultAggregateBoardInfo();
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result.isResult());
            assertNotNull(result.getData());
            
            // 验证故障聚合板信息
            FaultAggregateBoardInfo boardInfo = result.getData();
            assertEquals(5, boardInfo.getSelfRelated());
            
            // 验证处理效率信息
            FaultAggregateBoardInfo.HandlingEfficiencyInfo efficiencyInfo = boardInfo.getHandlingEfficiencyInfo();
            assertNotNull(efficiencyInfo);
            assertEquals(3, efficiencyInfo.getToTimeOut());
            assertEquals(2, efficiencyInfo.getTimedOut());
            assertEquals(1, efficiencyInfo.getUntimelyResponse());
            
            // 验证故障等级信息
            FaultAggregateBoardInfo.FaultLevelInfo levelInfo = boardInfo.getFaultLevelInfo();
            assertNotNull(levelInfo);
            assertEquals(10, levelInfo.getGeneralEvent());
            assertEquals(8, levelInfo.getNormalFault());
            assertEquals(5, levelInfo.getSeriousFault());
            assertEquals(3, levelInfo.getMajorFault());
            
            // 验证方法调用
            verify(restClient, times(1)).get();
            verify(requestHeadersUriSpec, times(1)).uri("http://test-influence-api/api/fault/statistics");
            verify(requestHeadersSpec, times(1)).header(eq("Cookie"), eq("bk_token=test-token"));
            verify(requestHeadersSpec, times(1)).accept(MediaType.APPLICATION_JSON);
        }
    }
    
    /**
     * 测试获取工单的故障等级统计信息 - 验证URL拼接
     */
    @Test
    void testQueryFaultAggregateBoardInfoUrlConstruction() {
        // 模拟配置属性
        when(influenceProperties.getBaseUrl()).thenReturn("http://test-influence-api");
        when(influenceProperties.getFaultLevelStatisticsRemoteUrl()).thenReturn("/api/fault/statistics");
        
        // 模拟 UserUtils.getToken() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getToken).thenReturn("test-token");
            
            // 模拟 RestClient 调用链
            when(restClient.get()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.header(anyString(), anyString())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(mockInfluenceResponse);
            
            // 执行测试
            InfluenceResponse<FaultAggregateBoardInfo> result = influenceService.queryFaultAggregateBoardInfo();
            
            // 验证结果
            assertNotNull(result);
            
            // 验证配置属性的调用
            verify(influenceProperties, times(1)).getBaseUrl();
            verify(influenceProperties, times(1)).getFaultLevelStatisticsRemoteUrl();
            
            // 验证完整URL的构建
            verify(requestHeadersUriSpec, times(1)).uri("http://test-influence-api/api/fault/statistics");
        }
    }
    
    /**
     * 测试获取工单的故障等级统计信息 - 不同的token值
     */
    @Test
    void testQueryFaultAggregateBoardInfoWithDifferentToken() {
        // 模拟配置属性
        when(influenceProperties.getBaseUrl()).thenReturn("http://test-influence-api");
        when(influenceProperties.getFaultLevelStatisticsRemoteUrl()).thenReturn("/api/fault/statistics");
        
        // 模拟 UserUtils.getToken() 方法返回不同的token
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getToken).thenReturn("different-test-token");
            
            // 模拟 RestClient 调用链
            when(restClient.get()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.header(anyString(), anyString())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(mockInfluenceResponse);
            
            // 执行测试
            InfluenceResponse<FaultAggregateBoardInfo> result = influenceService.queryFaultAggregateBoardInfo();
            
            // 验证结果
            assertNotNull(result);
            
            // 验证使用了正确的token
            verify(requestHeadersSpec, times(1)).header(eq("Cookie"), eq("bk_token=different-test-token"));
        }
    }
    
    /**
     * 测试获取工单的故障等级统计信息 - 验证响应处理
     */
    @Test
    void testQueryFaultAggregateBoardInfoResponseHandling() {
        // 模拟配置属性
        when(influenceProperties.getBaseUrl()).thenReturn("http://test-influence-api");
        when(influenceProperties.getFaultLevelStatisticsRemoteUrl()).thenReturn("/api/fault/statistics");
        
        // 模拟 UserUtils.getToken() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getToken).thenReturn("test-token");
            
            // 模拟 RestClient 调用链
            when(restClient.get()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.header(anyString(), anyString())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(mockInfluenceResponse);
            
            // 执行测试
            InfluenceResponse<FaultAggregateBoardInfo> result = influenceService.queryFaultAggregateBoardInfo();
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result.isResult());
            
            // 验证响应体的解析
            verify(responseSpec, times(1)).body(any(ParameterizedTypeReference.class));
        }
    }
    
    /**
     * 测试获取工单的故障等级统计信息 - 验证响应验证调用
     */
    @Test
    void testQueryFaultAggregateBoardInfoValidation() {
        // 模拟配置属性
        when(influenceProperties.getBaseUrl()).thenReturn("http://test-influence-api");
        when(influenceProperties.getFaultLevelStatisticsRemoteUrl()).thenReturn("/api/fault/statistics");
        
        // 创建一个可以验证validateInfluenceResponse调用的spy对象
        InfluenceResponse<FaultAggregateBoardInfo> spyResponse = Mockito.spy(mockInfluenceResponse);
        
        // 模拟 UserUtils.getToken() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getToken).thenReturn("test-token");
            
            // 模拟 RestClient 调用链
            when(restClient.get()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.header(anyString(), anyString())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.accept(MediaType.APPLICATION_JSON)).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.body(any(ParameterizedTypeReference.class))).thenReturn(spyResponse);
            
            // 执行测试
            InfluenceResponse<FaultAggregateBoardInfo> result = influenceService.queryFaultAggregateBoardInfo();
            
            // 验证结果
            assertNotNull(result);
            
            // 验证validateInfluenceResponse方法被调用
            verify(spyResponse, times(1)).validateInfluenceResponse();
        }
    }
}
