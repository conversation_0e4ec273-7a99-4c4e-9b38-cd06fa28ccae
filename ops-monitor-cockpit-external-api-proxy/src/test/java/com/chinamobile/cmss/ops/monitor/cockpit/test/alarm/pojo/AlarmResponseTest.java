/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.test.alarm.pojo;

import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.exception.AlarmApiException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * AlarmResponse 的单元测试类
 */
public class AlarmResponseTest {
    
    private AlarmResponse<String> alarmResponse;
    
    private AlarmResponse.Meta meta;
    
    @BeforeEach
    void setUp() {
        alarmResponse = new AlarmResponse<>();
        meta = new AlarmResponse.Meta();
        alarmResponse.setMeta(meta);
        alarmResponse.setData("test data");
    }
    
    /**
     * 测试成功响应验证
     */
    @Test
    void testValidateAlarmResponseSuccess() {
        // 设置成功响应
        meta.setCode("20000");
        meta.setMessage("success");
        
        // 验证不抛出异常
        assertDoesNotThrow(() -> alarmResponse.validateAlarmResponse());
    }
    
    /**
     * 测试失败响应验证 - 错误代码
     */
    @Test
    void testValidateAlarmResponseFailureWithErrorCode() {
        // 设置失败响应
        meta.setCode("50001");
        meta.setMessage("告警API调用失败");
        
        // 验证抛出AlarmApiException
        AlarmApiException exception = assertThrows(AlarmApiException.class,
                () -> alarmResponse.validateAlarmResponse());
        
        assertEquals("告警API调用失败", exception.getMessage());
        assertEquals("50001", exception.getCode());
    }
    
    /**
     * 测试失败响应验证 - 空代码
     */
    @Test
    void testValidateAlarmResponseFailureWithNullCode() {
        // 设置失败响应
        meta.setCode(null);
        meta.setMessage("代码为空");
        
        // 验证抛出AlarmApiException
        AlarmApiException exception = assertThrows(AlarmApiException.class,
                () -> alarmResponse.validateAlarmResponse());
        
        assertEquals("代码为空", exception.getMessage());
        assertEquals("null", exception.getCode());
    }
    
    /**
     * 测试失败响应验证 - 空字符串代码
     */
    @Test
    void testValidateAlarmResponseFailureWithEmptyCode() {
        // 设置失败响应
        meta.setCode("");
        meta.setMessage("代码为空字符串");
        
        // 验证抛出AlarmApiException
        AlarmApiException exception = assertThrows(AlarmApiException.class,
                () -> alarmResponse.validateAlarmResponse());
        
        assertEquals("代码为空字符串", exception.getMessage());
        assertEquals("", exception.getCode());
    }
    
    /**
     * 测试Meta类的getter和setter
     */
    @Test
    void testMetaGettersAndSetters() {
        AlarmResponse.Meta testMeta = new AlarmResponse.Meta();
        
        // 测试设置和获取code
        testMeta.setCode("20000");
        assertEquals("20000", testMeta.getCode());
        
        // 测试设置和获取message
        testMeta.setMessage("success");
        assertEquals("success", testMeta.getMessage());
    }
    
    /**
     * 测试AlarmResponse的getter和setter
     */
    @Test
    void testAlarmResponseGettersAndSetters() {
        AlarmResponse<Integer> response = new AlarmResponse<>();
        AlarmResponse.Meta testMeta = new AlarmResponse.Meta();
        
        // 测试设置和获取meta
        response.setMeta(testMeta);
        assertEquals(testMeta, response.getMeta());
        
        // 测试设置和获取data
        response.setData(123);
        assertEquals(Integer.valueOf(123), response.getData());
    }
    
    /**
     * 测试不同数据类型的AlarmResponse
     */
    @Test
    void testAlarmResponseWithDifferentDataTypes() {
        // 测试Integer类型
        AlarmResponse<Integer> intResponse = new AlarmResponse<>();
        intResponse.setData(42);
        assertEquals(Integer.valueOf(42), intResponse.getData());
        
        // 测试Boolean类型
        AlarmResponse<Boolean> boolResponse = new AlarmResponse<>();
        boolResponse.setData(true);
        assertEquals(Boolean.TRUE, boolResponse.getData());
        
        // 测试null数据
        AlarmResponse<String> nullResponse = new AlarmResponse<>();
        nullResponse.setData(null);
        assertEquals(null, nullResponse.getData());
    }
}
