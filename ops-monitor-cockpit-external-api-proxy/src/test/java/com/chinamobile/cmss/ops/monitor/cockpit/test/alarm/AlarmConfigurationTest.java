/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.test.alarm;

import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.AlarmConfiguration;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.config.AlarmClientHttpRequestInterceptor;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.config.AlarmProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.web.client.RestClient;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

/**
 * AlarmConfiguration 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class AlarmConfigurationTest {
    
    @Mock
    private AlarmProperties alarmProperties;
    
    private AlarmConfiguration alarmConfiguration;
    
    @BeforeEach
    void setUp() {
        alarmConfiguration = new AlarmConfiguration(alarmProperties);
    }
    
    /**
     * 测试RestClient Bean创建
     */
    @Test
    void testRestClientBeanCreation() {
        // 模拟配置属性
        when(alarmProperties.getBaseUrl()).thenReturn("http://test-alarm-api");
        
        // 创建RestClient Bean
        RestClient restClient = alarmConfiguration.restClient();
        
        // 验证RestClient不为空
        assertNotNull(restClient);
    }
    
    /**
     * 测试ClientHttpRequestInterceptor Bean创建
     */
    @Test
    void testClientHttpRequestInterceptorBeanCreation() {
        // 创建ClientHttpRequestInterceptor Bean
        ClientHttpRequestInterceptor interceptor = alarmConfiguration.clientHttpRequestInterceptor();
        
        // 验证拦截器不为空且类型正确
        assertNotNull(interceptor);
        assertTrue(interceptor instanceof AlarmClientHttpRequestInterceptor);
    }
    
    /**
     * 测试使用不同baseUrl的RestClient创建
     */
    @Test
    void testRestClientWithDifferentBaseUrls() {
        // 测试不同的baseUrl
        String[] baseUrls = {
                "http://alarm-api.example.com",
                "https://secure-alarm-api.example.com",
                "http://localhost:8080",
                "https://alarm-service:443"
        };
        
        for (String baseUrl : baseUrls) {
            when(alarmProperties.getBaseUrl()).thenReturn(baseUrl);
            
            // 创建RestClient Bean
            RestClient restClient = alarmConfiguration.restClient();
            
            // 验证RestClient不为空
            assertNotNull(restClient);
        }
    }
    
    /**
     * 测试使用空baseUrl的RestClient创建
     */
    @Test
    void testRestClientWithEmptyBaseUrl() {
        // 模拟空baseUrl
        when(alarmProperties.getBaseUrl()).thenReturn("");
        
        // 创建RestClient Bean
        RestClient restClient = alarmConfiguration.restClient();
        
        // 验证RestClient不为空
        assertNotNull(restClient);
    }
    
    /**
     * 测试使用null baseUrl的RestClient创建
     */
    @Test
    void testRestClientWithNullBaseUrl() {
        // 模拟null baseUrl
        when(alarmProperties.getBaseUrl()).thenReturn(null);
        
        // 创建RestClient Bean
        RestClient restClient = alarmConfiguration.restClient();
        
        // 验证RestClient不为空
        assertNotNull(restClient);
    }
    
    /**
     * 测试配置类构造函数
     */
    @Test
    void testAlarmConfigurationConstructor() {
        // 验证配置类构造函数正常工作
        AlarmConfiguration config = new AlarmConfiguration(alarmProperties);
        assertNotNull(config);
    }
    
    /**
     * 测试多次调用Bean创建方法
     */
    @Test
    void testMultipleBeanCreations() {
        // 模拟配置属性
        when(alarmProperties.getBaseUrl()).thenReturn("http://test-alarm-api");
        
        // 多次创建Bean
        RestClient restClient1 = alarmConfiguration.restClient();
        RestClient restClient2 = alarmConfiguration.restClient();
        ClientHttpRequestInterceptor interceptor1 = alarmConfiguration.clientHttpRequestInterceptor();
        
        // 验证所有Bean都不为空
        assertNotNull(restClient1);
        assertNotNull(restClient2);
        assertNotNull(interceptor1);
        
        ClientHttpRequestInterceptor interceptor2 = alarmConfiguration.clientHttpRequestInterceptor();
        assertNotNull(interceptor2);
        // 验证拦截器类型正确
        assertTrue(interceptor1 instanceof AlarmClientHttpRequestInterceptor);
        assertTrue(interceptor2 instanceof AlarmClientHttpRequestInterceptor);
    }
}
