/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.test.alarm.config;

import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.config.AlarmClientHttpRequestInterceptor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;
import java.net.URI;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AlarmClientHttpRequestInterceptor 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class AlarmClientHttpRequestInterceptorTest {
    
    @Mock
    private HttpRequest httpRequest;
    
    @Mock
    private ClientHttpRequestExecution execution;
    
    @Mock
    private ClientHttpResponse clientHttpResponse;
    
    private AlarmClientHttpRequestInterceptor interceptor;
    
    private byte[] requestBody;
    
    @BeforeEach
    void setUp() {
        interceptor = new AlarmClientHttpRequestInterceptor();
        requestBody = "test request body".getBytes();
    }
    
    /**
     * 测试拦截器正常执行
     */
    @Test
    void testInterceptSuccess() throws IOException {
        // 模拟请求URI
        URI testUri = URI.create("http://test-alarm-api/api/test");
        when(httpRequest.getURI()).thenReturn(testUri);
        when(execution.execute(httpRequest, requestBody)).thenReturn(clientHttpResponse);
        
        // 执行拦截器
        ClientHttpResponse result = interceptor.intercept(httpRequest, requestBody, execution);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(clientHttpResponse, result);
        
        // 验证方法调用
        verify(httpRequest, times(2)).getURI();
        verify(execution, times(1)).execute(httpRequest, requestBody);
    }
    
    /**
     * 测试拦截器处理不同的URI
     */
    @Test
    void testInterceptWithDifferentUris() throws IOException {
        // 测试不同的URI
        String[] testUris = {
                "http://alarm-api/api/v1/alarms",
                "https://secure-alarm-api/api/v2/statistics",
                "http://localhost:8080/alarm/test"
        };
        
        for (String uriString : testUris) {
            URI testUri = URI.create(uriString);
            when(httpRequest.getURI()).thenReturn(testUri);
            when(execution.execute(httpRequest, requestBody)).thenReturn(clientHttpResponse);
            
            // 执行拦截器
            ClientHttpResponse result = interceptor.intercept(httpRequest, requestBody, execution);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(clientHttpResponse, result);
        }
    }
    
    /**
     * 测试拦截器处理空请求体
     */
    @Test
    void testInterceptWithEmptyBody() throws IOException {
        // 模拟请求URI
        URI testUri = URI.create("http://test-alarm-api/api/test");
        when(httpRequest.getURI()).thenReturn(testUri);
        
        byte[] emptyBody = new byte[0];
        when(execution.execute(httpRequest, emptyBody)).thenReturn(clientHttpResponse);
        
        // 执行拦截器
        ClientHttpResponse result = interceptor.intercept(httpRequest, emptyBody, execution);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(clientHttpResponse, result);
        
        // 验证方法调用
        verify(execution, times(1)).execute(httpRequest, emptyBody);
    }
    
    /**
     * 测试拦截器处理null请求体
     */
    @Test
    void testInterceptWithNullBody() throws IOException {
        // 模拟请求URI
        URI testUri = URI.create("http://test-alarm-api/api/test");
        when(httpRequest.getURI()).thenReturn(testUri);
        when(execution.execute(eq(httpRequest), any())).thenReturn(clientHttpResponse);
        
        // 执行拦截器
        ClientHttpResponse result = interceptor.intercept(httpRequest, null, execution);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(clientHttpResponse, result);
        
        // 验证方法调用
        verify(execution, times(1)).execute(eq(httpRequest), any());
    }
    
    /**
     * 测试拦截器处理执行异常
     */
    @Test
    void testInterceptWithExecutionException() throws IOException {
        // 模拟请求URI
        URI testUri = URI.create("http://test-alarm-api/api/test");
        when(httpRequest.getURI()).thenReturn(testUri);
        
        // 模拟执行异常
        IOException expectedException = new IOException("Network error");
        when(execution.execute(httpRequest, requestBody)).thenThrow(expectedException);
        
        // 验证异常被抛出
        try {
            interceptor.intercept(httpRequest, requestBody, execution);
        } catch (IOException e) {
            assertEquals(expectedException, e);
        }
        
        // 验证方法调用
        verify(execution, times(1)).execute(httpRequest, requestBody);
    }
    
    /**
     * 测试拦截器的时间测量功能
     */
    @Test
    void testInterceptTimeMeasurement() throws IOException {
        // 模拟请求URI
        URI testUri = URI.create("http://test-alarm-api/api/test");
        when(httpRequest.getURI()).thenReturn(testUri);
        
        // 模拟执行延迟
        when(execution.execute(httpRequest, requestBody)).thenAnswer(invocation -> {
            Thread.sleep(10);
            return clientHttpResponse;
        });
        
        // 执行拦截器
        ClientHttpResponse result = interceptor.intercept(httpRequest, requestBody, execution);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(clientHttpResponse, result);
        
        // 验证方法调用
        verify(execution, times(1)).execute(httpRequest, requestBody);
    }
}
