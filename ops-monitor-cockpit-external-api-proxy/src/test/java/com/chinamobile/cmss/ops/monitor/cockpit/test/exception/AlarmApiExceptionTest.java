/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.test.exception;

import com.chinamobile.cmss.ops.monitor.cockpit.external.exception.AlarmApiException;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * AlarmApiException 的单元测试类
 */
public class AlarmApiExceptionTest {
    
    /**
     * 测试AlarmApiException构造函数
     */
    @Test
    void testAlarmApiExceptionConstructor() {
        // 测试数据
        String message = "告警API调用失败";
        String code = "50001";
        
        // 创建异常实例
        AlarmApiException exception = new AlarmApiException(message, code);
        
        // 验证异常属性
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
    
    /**
     * 测试AlarmApiException继承关系
     */
    @Test
    void testAlarmApiExceptionInheritance() {
        // 测试数据
        String message = "告警API异常";
        String code = "50002";
        
        // 创建异常实例
        AlarmApiException exception = new AlarmApiException(message, code);
        
        // 验证继承关系
        assertNotNull(exception);
        assertEquals(RuntimeException.class, exception.getClass().getSuperclass().getSuperclass());
    }
    
    /**
     * 测试AlarmApiException空消息
     */
    @Test
    void testAlarmApiExceptionWithEmptyMessage() {
        // 测试数据
        String message = "";
        String code = "50003";
        
        // 创建异常实例
        AlarmApiException exception = new AlarmApiException(message, code);
        
        // 验证异常属性
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
    
    /**
     * 测试AlarmApiException空代码
     */
    @Test
    void testAlarmApiExceptionWithEmptyCode() {
        // 测试数据
        String message = "告警API异常";
        String code = "";
        
        // 创建异常实例
        AlarmApiException exception = new AlarmApiException(message, code);
        
        // 验证异常属性
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
    
    /**
     * 测试AlarmApiException null值
     */
    @Test
    void testAlarmApiExceptionWithNullValues() {
        // 测试数据
        String message = null;
        String code = null;
        
        // 创建异常实例
        AlarmApiException exception = new AlarmApiException(message, code);
        
        // 验证异常属性
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
}
