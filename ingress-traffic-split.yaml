apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ops-foc-fault-replay
  namespace: ops-foc
  annotations:
    kubernetes.io/ingress.class: nginx
    # 启用CORS支持
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, PATCH, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-expose-headers: "*"
    # 设置请求体大小限制
    nginx.ingress.kubernetes.io/proxy-body-size: "4096m"
    # 基于HTTP方法的流量分流 - 使用upstream指令
    nginx.ingress.kubernetes.io/upstream-vhost: "foc.ccops.bcopstest.com"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      # 根据HTTP方法设置upstream
      if ($request_method ~ ^(POST|PUT|PATCH|DELETE)$) {
        set $service_name "svc-ops-foc-fault-replay-write";
        set $service_port "8080";
      }
      if ($request_method ~ ^(GET|HEAD|OPTIONS)$) {
        set $service_name "svc-ops-foc-fault-replay-read";
        set $service_port "8080";
      }
      # 默认使用读服务
      if ($service_name = "") {
        set $service_name "svc-ops-foc-fault-replay-read";
        set $service_port "8080";
      }
spec:
  rules:
    - host: foc.ccops.bcopstest.com
      http:
        paths:
          # 主路径 - 所有请求都通过这里，由configuration-snippet处理分流
          - path: /ops-foc/fault-replay
            pathType: Prefix
            backend:
              service:
                name: svc-ops-foc-fault-replay-read
                port:
                  number: 8080
