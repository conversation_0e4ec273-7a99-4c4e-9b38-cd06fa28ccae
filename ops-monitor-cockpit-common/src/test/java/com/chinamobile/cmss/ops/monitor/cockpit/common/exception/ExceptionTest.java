/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Unit tests for exception classes in the common package
 */
public class ExceptionTest {
    
    @Test
    void testAuthException() {
        // Arrange
        String message = "Authentication failed";
        
        // Act
        AuthException exception = new AuthException(message);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals("401", exception.getCode());
    }
    
    @Test
    void testParamInvalidException() {
        // Arrange
        String message = "Invalid parameter";
        
        // Act
        ParamInvalidException exception = new ParamInvalidException(message);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals("400", exception.getCode());
    }
    
    @Test
    void testServerException() {
        // Arrange
        String message = "Server error occurred";
        
        // Act
        ServerException exception = new ServerException(message);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals("500", exception.getCode());
    }
    
    @Test
    void testSystemException() {
        // Arrange
        String message = "System error occurred";
        
        // Act
        SystemException exception = new SystemException(message);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals("500", exception.getCode());
    }
    
    @Test
    void testSystemExceptionWithCustomCode() {
        // Arrange
        String message = "System error with custom code";
        String code = "600";
        
        // Act
        SystemException exception = new SystemException(message, code);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
    
    @Test
    void testMethodNotSupportedException() {
        // Arrange
        String message = "Method not supported";
        
        // Act
        MethodNotSupportedException exception = new MethodNotSupportedException(message);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals("405", exception.getCode());
    }
    
    @Test
    void testRootException() {
        // Arrange
        String message = "Root exception";
        String code = "999";
        
        // Act
        RootException exception = new RootException(message, code);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }
}
