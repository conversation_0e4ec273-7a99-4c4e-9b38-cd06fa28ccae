/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.interceptor;

import cn.hutool.core.date.SystemClock;
import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.UserUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.MDC;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Unit tests for {@link AccessLogInterceptor}
 */
@ExtendWith(MockitoExtension.class)
public class AccessLogInterceptorTest {
    
    @InjectMocks
    private AccessLogInterceptor accessLogInterceptor;
    
    @Mock
    private Object handler;
    
    private MockHttpServletRequest mockRequest;
    
    private MockHttpServletResponse mockResponse;
    
    @BeforeEach
    void setUp() {
        mockRequest = new MockHttpServletRequest();
        mockRequest.setRequestURI("/api/test");
        mockRequest.setMethod("GET");
        mockRequest.setParameter("param1", "value1");
        mockRequest.setParameter("param2", "value2");
        // Set server name and port instead of directly setting requestURL
        mockRequest.setServerName("localhost");
        mockRequest.setServerPort(8080);
        mockRequest.setContextPath("");
        mockRequest.setServletPath("/api/test");
        mockRequest.setProtocol("HTTP/1.1");
        
        mockResponse = new MockHttpServletResponse();
        
        // Clear MDC before each test
        MDC.clear();
    }
    
    @AfterEach
    void tearDown() {
        // Clear MDC after each test
        MDC.clear();
    }
    
    @Test
    void testPreHandle() {
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class); MockedStatic<SystemClock> mockedSystemClock = Mockito.mockStatic(SystemClock.class)) {
            
            // Arrange
            mockedUserUtils.when(UserUtils::getUserName).thenReturn("testUser");
            mockedSystemClock.when(SystemClock::now).thenReturn(1000L);
            
            // Act
            boolean result = accessLogInterceptor.preHandle(mockRequest, mockResponse, handler);
            
            // Assert
            assertTrue(result);
            assertNotNull(mockRequest.getAttribute("startTime"));
            assertNotNull(mockRequest.getAttribute("uuid"));
        }
    }
    
    @Test
    void testAfterCompletion() throws Exception {
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class); MockedStatic<SystemClock> mockedSystemClock = Mockito.mockStatic(SystemClock.class)) {
            
            // Arrange
            mockedUserUtils.when(UserUtils::getUserName).thenReturn("testUser");
            mockedSystemClock.when(SystemClock::now).thenReturn(1100L);
            
            // Set up request attributes that would have been set in preHandle
            mockRequest.setAttribute("startTime", 1000L);
            mockRequest.setAttribute("uuid", "test-uuid");
            
            // Act
            accessLogInterceptor.afterCompletion(mockRequest, mockResponse, handler, null);
            
            // No assertions needed as we're just verifying it doesn't throw exceptions
        }
    }
    
    @Test
    void testAfterCompletionWithException() throws Exception {
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class); MockedStatic<SystemClock> mockedSystemClock = Mockito.mockStatic(SystemClock.class)) {
            
            // Arrange
            mockedUserUtils.when(UserUtils::getUserName).thenReturn("testUser");
            mockedSystemClock.when(SystemClock::now).thenReturn(1100L);
            
            // Set up request attributes that would have been set in preHandle
            mockRequest.setAttribute("startTime", 1000L);
            mockRequest.setAttribute("uuid", "test-uuid");
            
            Exception testException = new RuntimeException("Test exception");
            
            // Act
            accessLogInterceptor.afterCompletion(mockRequest, mockResponse, handler, testException);
            
            // No assertions needed as we're just verifying it doesn't throw exceptions
        }
    }
}
