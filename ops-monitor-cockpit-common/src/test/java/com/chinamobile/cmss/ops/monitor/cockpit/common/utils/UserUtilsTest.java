/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.utils;

import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link UserUtils}
 */
@ExtendWith(MockitoExtension.class)
public class UserUtilsTest {
    
    @Mock
    private HttpServletRequest request;
    
    @Mock
    private ServletRequestAttributes attributes;
    
    private JSONObject userJson;
    
    @BeforeEach
    void setUp() {
        userJson = new JSONObject();
        userJson.put("username", "testUser");
        userJson.put("id", "12345");
    }
    
    @Test
    void testGetCurrentRequest() {
        try (MockedStatic<RequestContextHolder> mockedRequestContextHolder = Mockito.mockStatic(RequestContextHolder.class)) {
            // Arrange
            mockedRequestContextHolder.when(RequestContextHolder::getRequestAttributes).thenReturn(attributes);
            when(attributes.getRequest()).thenReturn(request);
            
            // Act
            HttpServletRequest result = UserUtils.getCurrentRequest();
            
            // Assert
            assertEquals(request, result);
        }
    }
    
    @Test
    void testGetCurrentRequestWhenNoRequestContext() {
        try (MockedStatic<RequestContextHolder> mockedRequestContextHolder = Mockito.mockStatic(RequestContextHolder.class)) {
            // Arrange
            mockedRequestContextHolder.when(RequestContextHolder::getRequestAttributes).thenReturn(null);
            
            // Act
            HttpServletRequest result = UserUtils.getCurrentRequest();
            
            // Assert
            assertNull(result);
        }
    }
    
    @Test
    void testGetToken() {
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class, Mockito.CALLS_REAL_METHODS)) {
            // Arrange
            mockedUserUtils.when(UserUtils::getCurrentRequest).thenReturn(request);
            Cookie[] cookies = new Cookie[]{new Cookie("bk_token", "test-token")};
            when(request.getCookies()).thenReturn(cookies);
            
            // Act
            String token = UserUtils.getToken();
            
            // Assert
            assertEquals("test-token", token);
        }
    }
    
    @Test
    void testGetTokenWhenNoCookies() {
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class, Mockito.CALLS_REAL_METHODS)) {
            // Arrange
            mockedUserUtils.when(UserUtils::getCurrentRequest).thenReturn(request);
            when(request.getCookies()).thenThrow(new NullPointerException());
            
            // Act
            String token = UserUtils.getToken();
            
            // Assert
            assertNull(token);
        }
    }
    
    @Test
    void testGetUserName() {
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class, Mockito.CALLS_REAL_METHODS)) {
            // Arrange
            mockedUserUtils.when(UserUtils::getCurrentRequest).thenReturn(request);
            when(request.getHeader("bk-user")).thenReturn(userJson.toString());
            
            // Act
            String userName = UserUtils.getUserName();
            
            // Assert
            assertEquals("testUser", userName);
        }
    }
    
    @Test
    void testGetUserNameWithRequest() {
        // Arrange
        when(request.getHeader("bk-user")).thenReturn(userJson.toString());
        
        // Act
        String userName = UserUtils.getUserName(request);
        
        // Assert
        assertEquals("testUser", userName);
    }
    
    @Test
    void testGetUserNameWithNullRequest() {
        // Act
        String userName = UserUtils.getUserName(null);
        
        // Assert
        assertNull(userName);
    }
    
    @Test
    void testGetUserNameWithEmptyHeader() {
        // Arrange
        when(request.getHeader("bk-user")).thenReturn("");
        
        // Act
        String userName = UserUtils.getUserName(request);
        
        // Assert
        assertNull(userName);
    }
    
    @Test
    void testGetUserNameWithInvalidJson() {
        // Arrange
        when(request.getHeader("bk-user")).thenReturn("invalid-json");
        
        // Act
        String userName = UserUtils.getUserName(request);
        
        // Assert
        assertNull(userName);
    }
    
    @Test
    void testGetUserId() {
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class, Mockito.CALLS_REAL_METHODS)) {
            // Arrange
            mockedUserUtils.when(UserUtils::getCurrentRequest).thenReturn(request);
            when(request.getHeader("bk-user")).thenReturn(userJson.toString());
            
            // Act
            String userId = UserUtils.getUserId();
            
            // Assert
            assertEquals("12345", userId);
        }
    }
    
    @Test
    void testGetUserIdWithRequest() {
        // Arrange
        when(request.getHeader("bk-user")).thenReturn(userJson.toString());
        
        // Act
        String userId = UserUtils.getUserId(request);
        
        // Assert
        assertEquals("12345", userId);
    }
    
    @Test
    void testGetUserIdWithNullRequest() {
        // Act
        String userId = UserUtils.getUserId(null);
        
        // Assert
        assertNull(userId);
    }
    
    @Test
    void testGetUserInfo() {
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class, Mockito.CALLS_REAL_METHODS)) {
            // Arrange
            mockedUserUtils.when(UserUtils::getCurrentRequest).thenReturn(request);
            when(request.getHeader("bk-user")).thenReturn(userJson.toString());
            
            // Act
            JSONObject result = UserUtils.getUserInfo();
            
            // Assert
            assertEquals("testUser", result.getString("username"));
            assertEquals("12345", result.getString("id"));
        }
    }
    
    @Test
    void testGetUserInfoWithRequest() {
        // Arrange
        when(request.getHeader("bk-user")).thenReturn(userJson.toString());
        
        // Act
        JSONObject result = UserUtils.getUserInfo(request);
        
        // Assert
        assertEquals("testUser", result.getString("username"));
        assertEquals("12345", result.getString("id"));
    }
    
    @Test
    void testGetUserInfoWithNullRequest() {
        // Act
        JSONObject result = UserUtils.getUserInfo(null);
        
        // Assert
        assertNull(result);
    }
    
    @Test
    void testGetUserInfoWithInvalidJson() {
        // Arrange
        when(request.getHeader("bk-user")).thenReturn("invalid-json");
        
        // Act
        JSONObject result = UserUtils.getUserInfo(request);
        
        // Assert
        assertNull(result);
    }
}
