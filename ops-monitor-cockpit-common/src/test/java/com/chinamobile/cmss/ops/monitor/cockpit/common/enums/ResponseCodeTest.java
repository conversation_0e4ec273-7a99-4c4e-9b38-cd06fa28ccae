/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.enums;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Unit tests for {@link ResponseCode}
 */
public class ResponseCodeTest {
    
    @Test
    void testResponseCodeValues() {
        // Test SUCCESS
        assertEquals(200, ResponseCode.SUCCESS.getCode());
        assertEquals("Success", ResponseCode.SUCCESS.getMessage());
        
        // Test INTERNAL_ERROR
        assertEquals(500, ResponseCode.INTERNAL_ERROR.getCode());
        assertEquals(" 服务器遇到错误，无法完成请求！", ResponseCode.INTERNAL_ERROR.getMessage());
        
        // Test USER_INPUT_ERROR
        assertEquals(400, ResponseCode.USER_INPUT_ERROR.getCode());
        assertEquals("用户输入错误", ResponseCode.USER_INPUT_ERROR.getMessage());
        
        // Test METHOD_NOT_ALLOWED
        assertEquals(405, ResponseCode.METHOD_NOT_ALLOWED.getCode());
        assertEquals("未支持的方法", ResponseCode.METHOD_NOT_ALLOWED.getMessage());
        
        // Test AUTHENTICATION_NEEDED
        assertEquals(401, ResponseCode.AUTHENTICATION_NEEDED.getCode());
        assertEquals("未授权认证！", ResponseCode.AUTHENTICATION_NEEDED.getMessage());
        
        // Test FORBIDDEN
        assertEquals(403, ResponseCode.FORBIDDEN.getCode());
        assertEquals("服务器拒绝请求！", ResponseCode.FORBIDDEN.getMessage());
        
        // Test TOO_FREQUENT_VISIT
        assertEquals(503, ResponseCode.TOO_FREQUENT_VISIT.getCode());
        assertEquals("服务器目前无法使用！", ResponseCode.TOO_FREQUENT_VISIT.getMessage());
        
        // Test PARAM_ERROR
        assertEquals(10000, ResponseCode.PARAM_ERROR.getCode());
        assertEquals("数据源存在问题！", ResponseCode.PARAM_ERROR.getMessage());
        
        // Test BAD_DATASOURCE_REQUEST
        assertEquals(10002, ResponseCode.BAD_DATASOURCE_REQUEST.getCode());
        assertEquals("源表和目标表必须属于同一数据源！", ResponseCode.BAD_DATASOURCE_REQUEST.getMessage());
        
        // Test RESOURCE_NOT_FOUND
        assertEquals(10001, ResponseCode.RESOURCE_NOT_FOUND.getCode());
        assertEquals("元数据表不存在！", ResponseCode.RESOURCE_NOT_FOUND.getMessage());
    }
}
