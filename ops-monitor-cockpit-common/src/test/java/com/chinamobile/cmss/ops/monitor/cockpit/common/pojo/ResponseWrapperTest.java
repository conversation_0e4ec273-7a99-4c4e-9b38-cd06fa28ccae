/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.pojo;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Unit tests for {@link ResponseWrapper}
 */
public class ResponseWrapperTest {
    
    @Test
    void testSuccessWithCodeOnly() {
        // Arrange
        ResponseWrapper<String> wrapper = new ResponseWrapper<>();
        String code = "200";
        
        // Act
        wrapper.success(code);
        
        // Assert
        assertEquals(code, wrapper.getMeta().getCode());
        assertEquals("OK", wrapper.getMeta().getMessage());
        assertNull(wrapper.getData());
    }
    
    @Test
    void testSuccessWithCodeAndData() {
        // Arrange
        ResponseWrapper<String> wrapper = new ResponseWrapper<>();
        String code = "200";
        String data = "Test Data";
        
        // Act
        wrapper.success(code, data);
        
        // Assert
        assertEquals(code, wrapper.getMeta().getCode());
        assertEquals("OK", wrapper.getMeta().getMessage());
        assertEquals(data, wrapper.getData());
    }
    
    @Test
    void testFailure() {
        // Arrange
        ResponseWrapper<String> wrapper = new ResponseWrapper<>();
        String code = "500";
        String message = "Internal Server Error";
        
        // Act
        wrapper.failure(code, message);
        
        // Assert
        assertEquals(code, wrapper.getMeta().getCode());
        assertEquals(message, wrapper.getMeta().getMessage());
        assertNull(wrapper.getData());
    }
    
    @Test
    void testMetaConstructor() {
        // Arrange
        String code = "200";
        String message = "Success";
        
        // Act
        ResponseWrapper.Meta meta = new ResponseWrapper.Meta(code, message);
        
        // Assert
        assertEquals(code, meta.getCode());
        assertEquals(message, meta.getMessage());
    }
    
    @Test
    void testMetaSettersAndGetters() {
        // Arrange
        ResponseWrapper.Meta meta = new ResponseWrapper.Meta();
        String code = "404";
        String message = "Not Found";
        
        // Act
        meta.setCode(code);
        meta.setMessage(message);
        
        // Assert
        assertEquals(code, meta.getCode());
        assertEquals(message, meta.getMessage());
    }
}
