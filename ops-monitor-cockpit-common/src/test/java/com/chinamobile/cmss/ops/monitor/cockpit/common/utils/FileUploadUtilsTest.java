/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.utils;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Unit tests for {@link FileUploadUtils}
 */
public class FileUploadUtilsTest {
    
    @TempDir
    Path tempDir;
    
    private MultipartFile testFile;
    
    private String uploadDir;
    
    private String savedFilePath;
    
    @BeforeEach
    void setUp() {
        // Create a test file
        testFile = new MockMultipartFile(
                "testFile",
                "test-file.txt",
                "text/plain",
                "This is a test file content".getBytes());
        
        uploadDir = tempDir.toString();
    }
    
    @AfterEach
    void tearDown() throws IOException {
        // Clean up any files created during tests
        if (savedFilePath != null) {
            Files.deleteIfExists(Paths.get(savedFilePath));
        }
    }
    
    @Test
    void testUploadFileWithValidFile() throws IOException {
        // Act
        savedFilePath = FileUploadUtils.uploadFile(uploadDir, testFile);
        
        // Assert
        assertTrue(Files.exists(Paths.get(savedFilePath)));
        assertEquals("This is a test file content", Files.readString(Paths.get(savedFilePath)));
        assertTrue(savedFilePath.endsWith(".txt"), "File should have the correct extension");
    }
    
    @Test
    void testUploadFileWithNoExtension() throws IOException {
        // Arrange
        MultipartFile fileWithoutExtension = new MockMultipartFile(
                "fileWithoutExtension",
                "test-file-no-extension",
                "text/plain",
                "File without extension".getBytes());
        
        // Act
        savedFilePath = FileUploadUtils.uploadFile(uploadDir, fileWithoutExtension);
        
        // Assert
        assertTrue(Files.exists(Paths.get(savedFilePath)));
        assertEquals("File without extension", Files.readString(Paths.get(savedFilePath)));
        assertTrue(savedFilePath.endsWith(".tmp"), "File should have the default .tmp extension");
    }
    
    @Test
    void testUploadFileWithNullFileName() throws IOException {
        // Arrange
        MultipartFile fileWithNullName = new MockMultipartFile(
                "fileWithNullName",
                null,
                "text/plain",
                "File with null name".getBytes());
        
        // Act
        savedFilePath = FileUploadUtils.uploadFile(uploadDir, fileWithNullName);
        
        // Assert
        assertTrue(Files.exists(Paths.get(savedFilePath)));
        assertEquals("File with null name", Files.readString(Paths.get(savedFilePath)));
        assertTrue(savedFilePath.endsWith(".tmp"), "File should have the default .tmp extension");
    }
    
    @Test
    void testDeleteFile() throws IOException {
        // Arrange
        savedFilePath = FileUploadUtils.uploadFile(uploadDir, testFile);
        assertTrue(Files.exists(Paths.get(savedFilePath)), "File should exist before deletion");
        
        // Act
        boolean result = FileUploadUtils.deleteFile(savedFilePath);
        
        // Assert
        assertTrue(result, "Delete operation should return true");
        assertFalse(Files.exists(Paths.get(savedFilePath)), "File should be deleted");
    }
    
    @Test
    void testDeleteNonExistentFile() throws IOException {
        // Arrange
        String nonExistentFilePath = tempDir.resolve("non-existent-file.txt").toString();
        
        // Act
        boolean result = FileUploadUtils.deleteFile(nonExistentFilePath);
        
        // Assert
        assertFalse(result, "Delete operation should return false for non-existent file");
    }
}
