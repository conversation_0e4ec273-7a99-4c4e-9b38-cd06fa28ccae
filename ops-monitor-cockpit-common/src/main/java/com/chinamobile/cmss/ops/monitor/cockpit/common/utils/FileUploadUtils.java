/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.utils;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Describe
 */
public class FileUploadUtils {
    
    /**
     * 上传文件并生成随机文件名
     * @param uploadDir 上传目录
     * @param file 文件
     * @return 文件路径
     * @throws IOException i
     */
    public static String uploadFile(String uploadDir, MultipartFile file) throws IOException {
        
        // 生成随机文件名
        String originalFileName = file.getOriginalFilename();
        String fileExtension = ".tmp";
        if (originalFileName != null && originalFileName.contains(".")) {
            fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
        }
        String randomFileName = UUID.randomUUID().toString() + fileExtension;
        
        // 创建文件保存路径
        Path path = Paths.get(uploadDir, randomFileName);
        Files.createDirectories(path.getParent());
        
        // 将文件保存到指定路径
        Files.copy(file.getInputStream(), path);
        
        // 返回保存的文件路径
        return path.toString();
    }
    
    /**
     * 删除文件
     * @param filePath f
     * @return bool
     * @throws IOException i
     */
    public static Boolean deleteFile(String filePath) throws IOException {
        
        File file = new File(filePath);
        if (file.exists()) {
            return file.delete();
        }
        return false;
    }
    
}
