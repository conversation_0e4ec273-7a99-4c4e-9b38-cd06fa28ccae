/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response Wrapper
 *
 * @param <T> body
 */
@Data
public class ResponseWrapper<T> {
    
    private Meta meta;
    
    private T data;
    
    /**
     * success
     *
     * @param code code
     * @return ResponseWrapper
     */
    public ResponseWrapper success(final String code) {
        return this.success(code, null);
    }
    
    /**
     * success
     *
     * @param code code
     * @param data data
     * @return ResponseWrapper
     */
    public ResponseWrapper success(final String code, final T data) {
        this.meta = new Meta(code, "OK");
        this.data = data;
        return this;
    }
    
    /**
     * failure
     *
     * @param code    code
     * @param message message
     * @return ResponseWrapper
     */
    public ResponseWrapper failure(final String code, final String message) {
        this.meta = new Meta(code, message);
        return this;
    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Meta {
        
        private String code;
        
        private String message;
    }
}
