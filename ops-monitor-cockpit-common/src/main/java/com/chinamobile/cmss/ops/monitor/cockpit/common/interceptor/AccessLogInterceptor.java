/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.interceptor;

import cn.hutool.core.date.SystemClock;
import com.alibaba.fastjson2.JSONObject;
import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.UserUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.UUID;

/**
 * AccessLogInterceptor
 */
@Slf4j
@Component
public class AccessLogInterceptor implements HandlerInterceptor {
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) {
        
        String uuid = UUID.randomUUID().toString();
        MDC.put("username", UserUtils.getUserName());
        MDC.put("traceId", uuid);
        MDC.put("uri", request.getRequestURL().toString());
        
        log.info(String.format("Request[%s] URL:[%s],username:[%s], Protocol:[%s], Params:%s",
                uuid,
                request.getRequestURL().toString(),
                UserUtils.getUserName(),
                request.getProtocol(),
                JSONObject.toJSONString(request.getParameterMap())));
        request.setAttribute("startTime", SystemClock.now());
        request.setAttribute("uuid", uuid);
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception e) throws Exception {
        long timeout = SystemClock.now() - (Long) request.getAttribute("startTime");
        String uuid = (String) request.getAttribute("uuid");
        log.info(String.format("Response[%s][%s] Timeout:[%s ms],username:[%s], ResponseStatus:[%s], ResponseBodySize:[%s], Error:[%s]",
                uuid,
                request.getRequestURI(),
                timeout,
                UserUtils.getUserName(),
                response.getStatus(),
                response.getBufferSize(),
                e != null ? String.valueOf(e) : "null"));
        MDC.clear();
    }
}
