/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.constant;

public class MessageConstant {
    
    /**
     * 泰岳消息：短信和邮件
     */
    public static class Message {
        
        /**
         * 成功码
         */
        public static final String SUCCESS_CODE = "0";
        
        /**
         * 失败码
         */
        public static final String FAIL_CODE = "1";
    }
    
    /**
     * 云空间相关枚举参数
     */
    public static class CloudSpace {
        
        /**
         * 故障通告的相关参数
         */
        public static class FaultNotification {
            
            /**
             * 会话列表透出的标题
             */
            public static final String MESSAGE = "你收到新的督办通告！";
            
            /**
             * 消息标题
             */
            public static final String TITLE = "督办通告";
            
            /**
             * 消息分类名称
             */
            public static final String CATEGORY_NAME = "驾驶舱.消息通知";
            
            /**
             * 消息发送者名称
             */
            public static final String SENDER = "智维平台";
        }
        
        /**
         * 云空间发送结果
         */
        public static class SendResult {
            
            /**
             * 成功码
             */
            public static final String SUCCESS_CODE = "0";
            
        }
        
        /**
         * 云空间发送失败后是否补偿短信
         */
        public static class IsCompensateShortMsg {
            
            /**
             * 不补偿
             */
            public static final Integer NO = 0;
            
            /**
             * 补偿
             */
            public static final Integer YES = 1;
        }
        
        /**
         * 消息类型, 0:文本消息;1:应用消息 空或不传：应用消息
         */
        public static class MsgType {
            
            /**
             * 文本消息
             */
            public static final String TEXT = "0";
            
            /**
             * 应用消息
             */
            public static final String APPLICATION = "1";
        }
        
        /**
         * 用户类型,枚举值：外协、自有
         * 当msgType为应用消息时，入参sendUserType无效，该参数的获取接口内部实现（取userMobiles对应ops账号的用户类型）
         */
        public static class SendUserType {
            
            /**
             * 自有
             */
            public static final String INTERNAL = "自有";
            
            /**
             * 外协
             */
            public static final String OUTSOURCE = "外协";
        }
        
        /**
         * rstSend 失败后是否需要重推短信
         */
        public static class RstSend {
            
            /**
             * 是
             */
            public static final String YES = "是";
            
            /**
             * 否
             */
            public static final String NO = "否";
        }
    }
    
    /**
     * 接受消息的用户的发送结果
     * <AUTHOR>
     */
    public static class UserSendResult {
        
        /**
         * 成功
         */
        public static final Integer SUCCESS = 0;
        
        /**
         * 失败
         */
        public static final Integer FAIL = 1;
        
        /**
         * 失败已补偿
         */
        public static final Integer COMPENSATE = 2;
    }
    
    /**
     * 泰岳签名
     */
    public static class EmailAndMessageSignature {
        
        /**
         * 生成的随机数的边界起始【六位数】
         */
        public static final Integer NONCE_START = 100000;
        
        /**
         * 生成的随机数的边界结束【六位数】
         */
        public static final Integer NONCE_END = 999999;
        
    }
    
}
