/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.utils;

import org.apache.commons.codec.binary.Hex;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

/**
 * SHA256Utils
 */
public class SHA256Utils {
    
    private static final String MAC_SHA1 = "HmacSHA1";
    
    private static final String UTF_8 = "utf-8";
    
    private static final String SHA_256 = "SHA-256";
    
    /**
     * Description: SHA 加密
     * @param data 加密之前的数据
     * @return java.lang.String
     * @throws NoSuchAlgorithmException 没有类似算法异常
     * @throws UnsupportedEncodingException 未支持编码异常
     */
    public static String encode(String data) throws UnsupportedEncodingException, NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance(SHA_256);
        byte[] hash = digest.digest(data.getBytes(UTF_8));
        return Hex.encodeHexString(hash);
    }
    
    /**
     * 签名
     * @param secretKey 秘钥
     * @param data 数据
     * @return java.lang.String
     * @throws NoSuchAlgorithmException 没有类似算法异常
     * @throws UnsupportedEncodingException 未支持编码异常
     * @throws InvalidKeyException 非法key异常
     */
    public static String sign(String secretKey, String data) throws NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException {
        Mac mac = Mac.getInstance(MAC_SHA1);
        byte[] secretKeyByte = secretKey.getBytes(UTF_8);
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKeyByte, MAC_SHA1);
        mac.init(secretKeySpec);
        return Hex.encodeHexString(mac.doFinal(data.getBytes(UTF_8)));
    }
}
