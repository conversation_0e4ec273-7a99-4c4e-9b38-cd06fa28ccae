/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.enums;

import lombok.Getter;

/**
 * Response Code
 */
@Getter
public enum ResponseCode {
    
    SUCCESS(200, "Success"),
    
    INTERNAL_ERROR(500, " 服务器遇到错误，无法完成请求！"),
    
    USER_INPUT_ERROR(400, "用户输入错误"),
    
    METHOD_NOT_ALLOWED(405, "未支持的方法"),
    
    AUTHENTICATION_NEEDED(401, "未授权认证！"),
    
    FORBIDDEN(403, "服务器拒绝请求！"),
    
    TOO_FREQUENT_VISIT(503, "服务器目前无法使用！"),
    PARAM_ERROR(10000, "数据源存在问题！"),
    BAD_DATASOURCE_REQUEST(10002, "源表和目标表必须属于同一数据源！"),
    RESOURCE_NOT_FOUND(10001, "元数据表不存在！");
    
    private final int code;
    
    private final String message;
    
    ResponseCode(final int code, final String message) {
        this.code = code;
        this.message = message;
    }
    
}
