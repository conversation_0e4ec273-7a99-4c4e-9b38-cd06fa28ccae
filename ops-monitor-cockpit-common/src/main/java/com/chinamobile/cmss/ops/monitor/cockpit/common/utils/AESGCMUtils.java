/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.utils;

import cn.hutool.core.util.StrUtil;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.SystemException;
import lombok.extern.slf4j.Slf4j;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * AES-GCM加密解密工具类
 */
@Slf4j
public class AESGCMUtils {
    
    // IV length in bytes
    private static final int GCM_IV_LENGTH = 12;
    
    // Tag length in bytes
    private static final int GCM_TAG_LENGTH = 16;
    
    // Key size in bits
    private static final int AES_KEY_SIZE = 128;
    
    // 从配置加载
    private static final String BASE64_KEY = "hUt9kiV837I2YwK8ZZaVFg==";
    
    /**
     * 加密文本
     * @param plaintext 原始的文本
     * @param key 秘钥
     * @return 加密后的文本
     * @throws Exception e
     */
    public static String encrypt(String plaintext, SecretKey key) throws Exception {
        
        byte[] iv = new byte[GCM_IV_LENGTH];
        SecureRandom random = new SecureRandom();
        random.nextBytes(iv);
        
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.ENCRYPT_MODE, key, gcmSpec);
        
        byte[] ciphertext = cipher.doFinal(plaintext.getBytes());
        
        byte[] encrypted = new byte[iv.length + ciphertext.length];
        System.arraycopy(iv, 0, encrypted, 0, iv.length);
        System.arraycopy(ciphertext, 0, encrypted, iv.length, ciphertext.length);
        
        return Base64.getEncoder().encodeToString(encrypted);
    }
    
    /**
     * 解密文本
     * @param encryptedText 加密的文本
     * @param key 秘钥
     * @return 解密后的文本
     * @throws Exception e
     */
    public static String decrypt(String encryptedText, SecretKey key) throws Exception {
        
        byte[] decoded = Base64.getDecoder().decode(encryptedText);
        byte[] iv = new byte[GCM_IV_LENGTH];
        System.arraycopy(decoded, 0, iv, 0, iv.length);
        
        byte[] ciphertext = new byte[decoded.length - iv.length];
        System.arraycopy(decoded, iv.length, ciphertext, 0, ciphertext.length);
        
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.DECRYPT_MODE, key, gcmSpec);
        
        byte[] plaintext = cipher.doFinal(ciphertext);
        return new String(plaintext);
    }
    
    /**
     * 从Base64加载秘钥
     * @param base64Key base64秘钥
     * @return SecretKey
     */
    public static SecretKey loadKeyFromBase64(String base64Key) {
        
        byte[] decodedKey = Base64.getDecoder().decode(base64Key);
        return new SecretKeySpec(decodedKey, 0, decodedKey.length, "AES");
    }
    
    /**
     * 加密原始文本
     * @param plainText 原始文本
     * @return 加密后的文本
     * @throws SystemException se
     */
    public static String encryptText(String plainText) {
        
        // 从配置加载密钥
        SecretKey secretKey = loadKeyFromBase64(BASE64_KEY);
        String cipherText = null;
        try {
            if (StrUtil.isNotBlank(plainText)) {
                cipherText = encrypt(plainText, secretKey);
            }
        } catch (Exception e) {
            log.error("AES-GCM明文加密异常:明文:{},异常信息：{}", plainText, e.getMessage(), e);
            throw new SystemException("AES-GCM明文加密异常");
        }
        return cipherText;
    }
    
    /**
     * AES-GCM密文解密
     * <AUTHOR>
     * @param cipherText 加密的文本
     * @return 解密后的文本
     * @throws SystemException se
     */
    public static String decryptText(final String cipherText) {
        // 从配置加载密钥
        SecretKey secretKey = loadKeyFromBase64(BASE64_KEY);
        String plainText = null;
        try {
            if (StrUtil.isNotBlank(cipherText)) {
                plainText = decrypt(cipherText, secretKey);
            }
        } catch (Exception e) {
            log.error("AES-GCM密文解密异常:密文：{},异常信息：{}", cipherText, e.getMessage(), e);
            throw new SystemException("AES-GCM密文解密异常");
        }
        return plainText;
    }
    
}
