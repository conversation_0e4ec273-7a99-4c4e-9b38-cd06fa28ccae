/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.advice;

import com.chinamobile.cmss.ops.monitor.cockpit.common.enums.ResponseCode;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.AuthException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.MethodNotSupportedException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ParamInvalidException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.RootException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ServerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.stream.Collectors;

@ControllerAdvice
@ResponseBody
@Slf4j
public class ExceptionHandlerAdvice {
    
    /**
     * handle HttpMessageNotReadableException/ParamInvalidException
     *
     * @param ex 异常
     * @return RootException
     */
    @ExceptionHandler({HttpMessageNotReadableException.class, ParamInvalidException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public RootException handleHttpMessageNotReadableException(final Exception ex) {
        log.error("throw HttpMessageNotReadableException:", ex);
        final String msg = ResponseCode.USER_INPUT_ERROR.getMessage();
        return new ParamInvalidException(msg);
    }
    
    /**
     * handle AuthException
     *
     * @return RootException
     */
    @ExceptionHandler(AuthException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public AuthException handleAuthException() {
        return new AuthException(ResponseCode.AUTHENTICATION_NEEDED.getMessage());
    }
    
    /**
     * handle HttpRequestMethodNotSupportedException
     *
     * @param ex exception
     * @return RootException
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public MethodNotSupportedException handleHttpRequestMethodNotSupportedException(final HttpRequestMethodNotSupportedException ex) {
        log.error("throw HttpRequestMethodNotSupportedException:", ex);
        final String msg = ResponseCode.METHOD_NOT_ALLOWED.getMessage();
        return new MethodNotSupportedException(msg);
    }
    
    /**
     * handle Exception
     *
     * @param ex 异常
     * @return RootException
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public RootException handleSystemException(final Exception ex) {
        log.error("throw Exception: ", ex);
        final String msg = ResponseCode.INTERNAL_ERROR.getMessage();
        return new ServerException(msg);
    }
    
    /**
     * handle MethodArgumentNotValidException
     *
     * @param ex 异常
     * @return RootException
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    public RootException handleMethodArgumentNotValidException(final MethodArgumentNotValidException ex) {
        log.error("throw Exception: ", ex);
        BindingResult bindingResult = ex.getBindingResult();
        String msg = "Invalid Request:" + bindingResult.getFieldErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining(","));
        return new ServerException(msg);
    }
    
    /**
     * handle RootException
     *
     * @param ex 异常
     * @return RootException
     */
    @ExceptionHandler({ServerException.class, RootException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public RootException handleInternalServerErrorException(final RootException ex) {
        return ex;
    }
}
