/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.utils;

import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 用户工具类
 */
public class UserUtils {
    
    /**
     * 获取当前请求对象
     *
     * @return 当前请求对象，如果不在请求上下文中则返回null
     */
    public static HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
    
    /**
     * 获取当前请求的蓝鲸用户token
     *
     * @return 当前请求对象，如果不在请求上下文中则返回null
     */
    public static String getToken() {
        HttpServletRequest request = getCurrentRequest();
        String token = null;
        try {
            Cookie[] cookies = request.getCookies();
            for (Cookie cookie : cookies) {
                if ("bk_token".equals(cookie.getName())) {
                    token = cookie.getValue();
                }
            }
        } catch (Exception e) {
            return null;
        }
        return token;
    }
    
    /**
     * 从当前请求中获取用户名
     *
     * @return 用户名，如果获取不到则返回null
     */
    public static String getUserName() {
        HttpServletRequest request = getCurrentRequest();
        return getUserName(request);
    }
    
    /**
     * 从请求头中获取用户名
     *
     * @param request HTTP请求
     * @return 用户名，如果获取不到则返回null
     */
    public static String getUserName(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        
        String userHeader = request.getHeader("bk-user");
        if (StringUtils.isBlank(userHeader)) {
            return null;
        }
        
        try {
            JSONObject jsonObject = JSONObject.parseObject(userHeader);
            return jsonObject.getString("username");
        } catch (Exception e) {
            // 解析异常时返回null
            return null;
        }
    }
    
    /**
     * 从当前请求中获取用户ID
     *
     * @return 用户ID，如果获取不到则返回null
     */
    public static String getUserId() {
        HttpServletRequest request = getCurrentRequest();
        return getUserId(request);
    }
    
    /**
     * 从请求头中获取用户ID
     *
     * @param request HTTP请求
     * @return 用户ID，如果获取不到则返回null
     */
    public static String getUserId(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        
        String userHeader = request.getHeader("bk-user");
        if (StringUtils.isBlank(userHeader)) {
            return null;
        }
        
        try {
            JSONObject jsonObject = JSONObject.parseObject(userHeader);
            return jsonObject.getString("id");
        } catch (Exception e) {
            // 解析异常时返回null
            return null;
        }
    }
    
    /**
     * 从当前请求中获取用户信息JSON对象
     *
     * @return 用户信息JSON对象，如果获取不到则返回null
     */
    public static JSONObject getUserInfo() {
        HttpServletRequest request = getCurrentRequest();
        return getUserInfo(request);
    }
    
    /**
     * 从请求头中获取用户信息JSON对象
     *
     * @param request HTTP请求
     * @return 用户信息JSON对象，如果获取不到则返回null
     */
    public static JSONObject getUserInfo(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        
        String userHeader = request.getHeader("bk-user");
        if (StringUtils.isBlank(userHeader)) {
            return null;
        }
        
        try {
            return JSONObject.parseObject(userHeader);
        } catch (Exception e) {
            // 解析异常时返回null
            return null;
        }
    }
}
