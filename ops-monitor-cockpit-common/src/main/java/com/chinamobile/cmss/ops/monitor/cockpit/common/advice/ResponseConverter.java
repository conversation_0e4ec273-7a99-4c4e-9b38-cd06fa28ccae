/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.advice;

import com.chinamobile.cmss.ops.monitor.cockpit.common.annotation.SkipResponseConvert;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.RootException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.pojo.ResponseWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.annotation.Annotation;
import java.util.Arrays;
import java.util.Optional;

/**
 * 统一返回
 *
 * @param <T> body
 */
@Slf4j
@ControllerAdvice(annotations = {RestController.class, ControllerAdvice.class})
public class ResponseConverter<T> implements ResponseBodyAdvice<T> {
    
    private static final Integer SUCCESS = 20000;
    
    /**
     * supports
     *
     * @param returnType MethodParameter
     * @param converterType converterType
     * @return boolean
     */
    @Override
    public boolean supports(final MethodParameter returnType, final Class converterType) {
        if (returnType.getMethodAnnotation(SkipResponseConvert.class) != null) {
            log.debug("this method has SkipResponseConverter annotation, so its return value will not be convert");
            return false;
        }
        return !returnType.getGenericParameterType().equals(ResponseWrapper.class);
    }
    
    /**
     * beforeBodyWrite
     * @param body body
     * @param methodParameter methodParameter
     * @param mediaType mediaType
     * @param aClass aClass
     * @param serverHttpRequest serverHttpRequest
     * @param serverHttpResponse serverHttpResponse
     * @return Object
     */
    @Override
    public Object beforeBodyWrite(final Object body,
                                  final MethodParameter methodParameter,
                                  final MediaType mediaType,
                                  final Class aClass,
                                  final ServerHttpRequest serverHttpRequest,
                                  final ServerHttpResponse serverHttpResponse) {
        if (body instanceof RootException) {
            RootException exception = (RootException) body;
            return new ResponseWrapper().failure(exception.getCode(), exception.getMessage());
        }
        
        int code = 20000;
        
        Annotation[] annotations = methodParameter.getMethodAnnotations();
        Optional<Annotation> statusAnnotation = Arrays.stream(annotations)
                .filter(annotation -> annotation.annotationType().equals(ResponseStatus.class))
                .findFirst();
        
        if (statusAnnotation.isPresent()) {
            ResponseStatus resStatus = (ResponseStatus) statusAnnotation.get();
            code = resStatus.value().value();
        }
        
        ResponseWrapper wrapper = new ResponseWrapper().success(String.valueOf(code), body);
        
        // 如果返回值类型是String，进行序列化返回
        if (body instanceof String) {
            log.debug("return type is String, so convert to string");
            try {
                return new ObjectMapper().writeValueAsString(wrapper);
            } catch (JsonProcessingException e) {
                log.error("convert response wrapper to json failed, due to: {}", e.getMessage(), e);
                return body;
            }
        }
        return wrapper;
    }
}
