/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.common.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "spring.task.pool")
@Component
public class TaskThreadPoolProperties {
    
    // 核心线程数
    private int corePoolSize;
    
    // 最大线程数
    private int maxPoolSize;
    
    // 存活时间
    private int keepAliveSeconds;
    
    // 队列容量
    private int queueCapacity;
    
    // 线程名称前缀
    private String threadNamePrefix;
    
    public int getCorePoolSize() {
        
        return corePoolSize;
    }
    
    public void setCorePoolSize(final int corePoolSize) {
        
        this.corePoolSize = corePoolSize;
    }
    
    public int getMaxPoolSize() {
        
        return maxPoolSize;
    }
    
    public void setMaxPoolSize(final int maxPoolSize) {
        
        this.maxPoolSize = maxPoolSize;
    }
    
    public int getKeepAliveSeconds() {
        
        return keepAliveSeconds;
    }
    
    public void setKeepAliveSeconds(final int keepAliveSeconds) {
        
        this.keepAliveSeconds = keepAliveSeconds;
    }
    
    public int getQueueCapacity() {
        
        return queueCapacity;
    }
    
    public void setQueueCapacity(final int queueCapacity) {
        
        this.queueCapacity = queueCapacity;
    }
    
    public String getThreadNamePrefix() {
        
        return threadNamePrefix;
    }
    
    public void setThreadNamePrefix(final String threadNamePrefix) {
        
        this.threadNamePrefix = threadNamePrefix;
    }
}
