<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<ruleset xmlns="http://pmd.sourceforge.net/ruleset/2.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         name="full-pmd-ruleset" xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 http://pmd.sourceforge.net/ruleset_2_0_0.xsd">
    <description>PMD of Apache ShardingSphere</description>
    <exclude-pattern>.*/target/.*</exclude-pattern>
    
    <rule ref="rulesets/java/quickstart.xml">
        <exclude name="AbstractClassWithoutAnyMethod" />
        <exclude name="AvoidAccessibilityAlteration" />
        <exclude name="AvoidBranchingStatementAsLastInLoop" />
        <exclude name="AvoidCatchingThrowable" />
        <exclude name="AvoidStringBufferField" />
        <exclude name="AvoidUsingHardCodedIP" />
        <exclude name="AvoidUsingVolatile" />
        <exclude name="CloseResource" />
        <exclude name="FinalFieldCouldBeStatic" />
        <exclude name="GuardLogStatement" />
        <exclude name="OverrideBothEqualsAndHashcode" />
        <exclude name="PreserveStackTrace" />
        <exclude name="UncommentedEmptyMethodBody" />
        <exclude name="UnusedPrivateMethod" />
        <exclude name="UseLocaleWithCaseConversions" />
    </rule>
    
    <rule ref="category/java/bestpractices.xml/AvoidPrintStackTrace" />
    <rule ref="category/java/bestpractices.xml/AvoidReassigningCatchVariables" />
    <rule ref="category/java/bestpractices.xml/AvoidReassigningLoopVariables" />
    <rule ref="category/java/bestpractices.xml/AvoidReassigningParameters" />
    <rule ref="category/java/bestpractices.xml/ForLoopVariableCount">
        <properties>
            <property name="maximumVariables" value="2" />
        </properties>
    </rule>
    <rule ref="category/java/bestpractices.xml/JUnit5TestShouldBePackagePrivate" />
    <rule ref="category/java/bestpractices.xml/JUnitTestsShouldIncludeAssert" />
    <rule ref="category/java/bestpractices.xml/MethodReturnsInternalArray" />
    <rule ref="category/java/bestpractices.xml/ReplaceEnumerationWithIterator" />
    <rule ref="category/java/bestpractices.xml/ReplaceHashtableWithMap" />
    <rule ref="category/java/bestpractices.xml/ReplaceVectorWithList" />
    <rule ref="category/java/bestpractices.xml/SystemPrintln" />
    <rule ref="category/java/bestpractices.xml/UnusedAssignment" />
    <rule ref="category/java/bestpractices.xml/UseTryWithResources" />
    <rule ref="category/java/bestpractices.xml/WhileLoopWithLiteralBoolean" />
    
    <rule ref="category/java/codestyle.xml/AvoidUsingNativeCode" />
    <rule ref="category/java/codestyle.xml/BooleanGetMethodName" />
    <rule ref="category/java/codestyle.xml/CommentDefaultAccessModifier" />
    <rule ref="category/java/codestyle.xml/ConfusingTernary">
        <properties>
            <property name="ignoreElseIf" value="false" />
        </properties>
    </rule>
    <rule ref="category/java/codestyle.xml/EmptyMethodInAbstractClassShouldBeAbstract" />
    <rule ref="category/java/codestyle.xml/FieldDeclarationsShouldBeAtStartOfClass" />
    <rule ref="category/java/codestyle.xml/FieldNamingConventions" />
    <rule ref="category/java/codestyle.xml/LinguisticNaming" />
    <rule ref="category/java/codestyle.xml/PrematureDeclaration" />
    <rule ref="category/java/codestyle.xml/UnnecessaryCast" />
    <rule ref="category/java/codestyle.xml/UseDiamondOperator" />
    <rule ref="category/java/codestyle.xml/UseShortArrayInitializer" />
    
    <rule ref="category/java/design.xml/AvoidThrowingNewInstanceOfSameException" />
    <rule ref="category/java/design.xml/AvoidThrowingNullPointerException" />
    <rule ref="category/java/design.xml/AvoidThrowingRawExceptionTypes" />
    <rule ref="category/java/design.xml/AvoidUncheckedExceptionsInSignatures" />
    <rule ref="category/java/design.xml/CognitiveComplexity" />
    <rule ref="category/java/design.xml/CollapsibleIfStatements" />
    <rule ref="category/java/design.xml/CouplingBetweenObjects" />
    <rule ref="category/java/design.xml/DataClass" />
    <rule ref="category/java/design.xml/ExceptionAsFlowControl" />
    <rule ref="category/java/design.xml/GodClass" />
    <rule ref="category/java/design.xml/ImmutableField" />
    <rule ref="category/java/design.xml/MutableStaticState" />
    <rule ref="category/java/design.xml/SignatureDeclareThrowsException" />
    <rule ref="category/java/design.xml/SimplifyBooleanExpressions" />
    <rule ref="category/java/design.xml/UseObjectForClearerAPI" />
    <rule ref="category/java/design.xml/UseUtilityClass">
        <properties>
            <property name="ignoredAnnotations" value="lombok.NoArgsConstructor | org.springframework.boot.autoconfigure.SpringBootApplication" />
        </properties>
    </rule>
    
    <rule ref="category/java/documentation.xml/CommentSize">
        <properties>
            <property name="maxLines" value="40" />
            <property name="maxLineLength" value="200" />
        </properties>
    </rule>
    
    <rule ref="category/java/errorprone.xml/AssignmentInOperand">
        <properties>
            <property name="allowIf" value="true" />
            <property name="allowFor" value="true" />
            <property name="allowWhile" value="true" />
        </properties>
    </rule>
    <rule ref="category/java/errorprone.xml/AvoidCallingFinalize" />
    <rule ref="category/java/errorprone.xml/AvoidCatchingNPE" />
    <rule ref="category/java/errorprone.xml/AvoidFieldNameMatchingTypeName" />
    <rule ref="category/java/errorprone.xml/AvoidLosingExceptionInformation" />
    <rule ref="category/java/errorprone.xml/ConstructorCallsOverridableMethod" />
    <rule ref="category/java/errorprone.xml/DetachedTestCase" />
    <rule ref="category/java/errorprone.xml/DoNotHardCodeSDCard" />
    <rule ref="category/java/errorprone.xml/DoNotTerminateVM" />
    <rule ref="category/java/errorprone.xml/DoNotThrowExceptionInFinally" />
    <rule ref="category/java/errorprone.xml/DontImportSun" />
    <rule ref="category/java/errorprone.xml/InvalidLogMessageFormat" />
    <rule ref="category/java/errorprone.xml/MethodWithSameNameAsEnclosingClass" />
    <rule ref="category/java/errorprone.xml/MissingSerialVersionUID" />
    <rule ref="category/java/errorprone.xml/MoreThanOneLogger" />
    <rule ref="category/java/errorprone.xml/NonSerializableClass" />
    <rule ref="category/java/errorprone.xml/NullAssignment" />
    <rule ref="category/java/errorprone.xml/SimpleDateFormatNeedsLocale" />
    <rule ref="category/java/errorprone.xml/StringBufferInstantiationWithChar" />
    <rule ref="category/java/errorprone.xml/TestClassWithoutTestCases">
        <properties>
            <property name="testClassPattern" value="^(?:.*\.)?Test$" />
        </properties>
    </rule>
    <rule ref="category/java/errorprone.xml/UnnecessaryBooleanAssertion" />
    <rule ref="category/java/errorprone.xml/UnnecessaryCaseChange" />
    <rule ref="category/java/errorprone.xml/UseCorrectExceptionLogging" />
    <rule ref="category/java/errorprone.xml/UseProperClassLoader" />
    
    <rule ref="category/java/performance.xml/AddEmptyString" />
    <rule ref="category/java/performance.xml/AppendCharacterWithChar" />
    <rule ref="category/java/performance.xml/AvoidArrayLoops" />
    <rule ref="category/java/performance.xml/AvoidCalendarDateCreation" />
    <rule ref="category/java/performance.xml/AvoidFileStream" />
    <rule ref="category/java/performance.xml/ConsecutiveAppendsShouldReuse" />
    <rule ref="category/java/performance.xml/ConsecutiveLiteralAppends" />
    <rule ref="category/java/performance.xml/InefficientEmptyStringCheck" />
    <rule ref="category/java/performance.xml/InefficientStringBuffering" />
    <rule ref="category/java/performance.xml/RedundantFieldInitializer" />
    <rule ref="category/java/performance.xml/StringInstantiation" />
    <rule ref="category/java/performance.xml/StringToString" />
    <rule ref="category/java/performance.xml/TooFewBranchesForASwitchStatement" />
    <rule ref="category/java/performance.xml/UseArrayListInsteadOfVector" />
    <rule ref="category/java/performance.xml/UseArraysAsList" />
    <rule ref="category/java/performance.xml/UseIndexOfChar" />
    <rule ref="category/java/performance.xml/UseIOStreamsWithApacheCommonsFileItem" />
    <rule ref="category/java/performance.xml/UselessStringValueOf" />
    <rule ref="category/java/performance.xml/UseStringBufferForStringAppends" />
    <rule ref="category/java/performance.xml/UseStringBufferLength" />
    
    <rule ref="category/java/security.xml/HardCodedCryptoKey" />
    <rule ref="category/java/security.xml/InsecureCryptoIv" />
</ruleset>
