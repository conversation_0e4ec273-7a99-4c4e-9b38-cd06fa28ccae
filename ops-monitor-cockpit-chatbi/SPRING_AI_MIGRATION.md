# Spring AI Conversation Management Migration Guide

## Overview

The chat-bi module has been refactored to use Spring AI's built-in conversation management capabilities instead of our custom session handling logic. This migration provides better maintainability, reduced complexity, and leverages Spring AI's robust conversation management features.

## What Changed

### Before (Custom Session Management)
- **ConversationService & ConversationServiceImpl**: Custom conversation lifecycle management
- **ConversationContext**: Custom data model for conversation history
- **ConversationDatabaseService**: Custom database persistence
- **Manual caching**: In-memory `ConcurrentHashMap` with expiration logic
- **Custom session lifecycle**: Manual creation, updates, cleanup, and expiration

### After (Spring AI Conversation Management)
- **ChatMemory interface**: Spring AI's core conversation memory abstraction
- **MessageWindowChatMemory**: Built-in implementation with configurable message window
- **JdbcChatMemoryRepository**: JDBC-based storage backend using PostgreSQL
- **MessageChatMemoryAdvisor**: Automatic conversation context injection
- **ChatClient integration**: Seamless conversation management with conversation IDs

## Key Benefits

1. **Reduced Complexity**: Eliminated ~1000+ lines of custom session management code
2. **Better Maintainability**: Using Spring AI's well-tested conversation management
3. **Automatic Context Injection**: Spring AI automatically manages conversation history
4. **Multiple Storage Backends**: Easy to switch between In-Memory, JDBC, Cassandra, Neo4j
5. **Built-in Message Window Management**: Automatic cleanup of old messages
6. **Standardized API**: Following Spring AI conventions

## Architecture Changes

### New Components
- **ChatClientConfig**: Configures ChatClient with conversation memory
- **SpringAiConversationService**: Simplified conversation operations
- **ConversationBridgeService**: Backward compatibility bridge

### Updated Components
- **TextToSqlServiceImpl**: Now uses ChatClient with conversation management
- **NaturalLanguageQueryServiceImpl**: Uses Spring AI conversation IDs

### Database Changes
- **New Table**: `SPRING_AI_CHAT_MEMORY` (auto-created by Spring AI)
- **Old Tables**: `conversation` and `conversation_exchange` (deprecated but preserved)

## Configuration Changes

### New Spring AI Configuration
```yaml
spring:
  ai:
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: always
```

### Updated Application Configuration
```yaml
app:
  chatbi:
    conversation:
      max-history-length: 10  # Now controls MessageWindowChatMemory
      context-enabled: true   # Enables Spring AI conversation management
```

## API Changes

### TextToSqlService
```java
// Old method signature
TextToSqlResult generateSqlWithContext(String query, DatabaseConnectionService dataSource, ConversationContext context);

// New method signature  
TextToSqlResult generateSqlWithContext(String query, DatabaseConnectionService dataSource, String conversationId);
```

### Conversation Management
```java
// Old approach
ConversationContext context = conversationService.getOrCreateConversation(userId);
conversationService.updateConversation(userId, conversationId, query, sql, results);

// New approach
String conversationId = springAiConversationService.createNewConversation(userId);
springAiConversationService.updateConversation(conversationId, query, sql, results);
```

## Backward Compatibility

The migration includes a **ConversationBridgeService** that provides backward compatibility for existing API endpoints:

- Controller endpoints continue to work with `ConversationContext`
- Bridge service converts between Spring AI messages and legacy format
- Gradual migration path for frontend applications

## Migration Steps

1. **Dependencies Added**: Spring AI chat memory repository for JDBC
2. **Configuration Updated**: Added Spring AI chat memory settings
3. **Services Refactored**: TextToSqlService and NaturalLanguageQueryService updated
4. **Bridge Service Created**: Maintains backward compatibility
5. **Database Schema**: Spring AI automatically creates required tables

## Testing the Migration

1. **Start the application** - Spring AI will auto-create the `SPRING_AI_CHAT_MEMORY` table
2. **Test conversation flow** - Create new conversations and verify context is maintained
3. **Check database** - Verify conversations are stored in Spring AI format
4. **API compatibility** - Existing endpoints should continue to work

## Performance Improvements

- **Reduced Memory Usage**: No more in-memory conversation caching
- **Better Database Efficiency**: Spring AI's optimized storage format
- **Automatic Cleanup**: Built-in message window management
- **Reduced Code Complexity**: Fewer custom components to maintain

## Future Enhancements

With Spring AI conversation management in place, we can easily add:

1. **Vector Store Memory**: For semantic conversation search
2. **Multiple Memory Types**: Different strategies for different use cases
3. **Advanced Advisors**: RAG, tool calling, and other Spring AI features
4. **Conversation Analytics**: Better insights into conversation patterns

## Troubleshooting

### Common Issues

1. **Database Connection**: Ensure PostgreSQL is accessible for JDBC repository
2. **Schema Initialization**: Check `spring.ai.chat.memory.repository.jdbc.initialize-schema` setting
3. **Conversation ID Format**: Spring AI uses different ID format than custom implementation

### Monitoring

- Check application logs for Spring AI conversation operations
- Monitor `SPRING_AI_CHAT_MEMORY` table for conversation storage
- Use Spring AI's built-in observability features

## Rollback Plan

If needed, the migration can be rolled back by:

1. Reverting to previous version of the codebase
2. Custom conversation tables (`conversation`, `conversation_exchange`) are preserved
3. Re-enabling custom ConversationService implementation

The bridge service ensures a smooth transition with minimal disruption to existing functionality.
