<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.cmss.ops.fms.chatbi.mapper.DataSourceConfigMapper">

    <!-- 基础列映射 -->
    <resultMap id="BaseResultMap" type="com.chinamobile.cmss.ops.fms.chatbi.entity.DataSourceConfigEntity">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="jdbc_url" property="jdbcUrl"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="driver_class_name" property="driverClassName"/>
        <result column="database_name" property="databaseName"/>
        <result column="is_default" property="isDefault"/>
        <result column="enabled" property="enabled"/>
        <result column="description" property="description"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="max_pool_size" property="maxPoolSize"/>
        <result column="min_idle" property="minIdle"/>
        <result column="connection_timeout" property="connectionTimeout"/>
        <result column="idle_timeout" property="idleTimeout"/>
        <result column="max_lifetime" property="maxLifetime"/>
        <result column="properties" property="properties"/>
    </resultMap>

    <!-- 分页查询数据源配置 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT * FROM data_source_config
        <where>
            <if test="ew != null">
                <if test="ew.sqlSegment != null and ew.sqlSegment != ''">
                    ${ew.sqlSegment}
                </if>
            </if>
        </where>
        ORDER BY is_default DESC, id ASC
    </select>

    <!-- 根据条件查询数据源配置 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT * FROM data_source_config
        <where>
            <if test="ew != null">
                <if test="ew.sqlSegment != null and ew.sqlSegment != ''">
                    ${ew.sqlSegment}
                </if>
            </if>
        </where>
        ORDER BY is_default DESC, id ASC
    </select>

    <!-- 插入数据源配置 -->
    <insert id="insert" parameterType="com.chinamobile.cmss.ops.fms.chatbi.entity.DataSourceConfigEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO data_source_config (
            name, type, jdbc_url, username, password, driver_class_name, database_name,
            is_default, enabled, description, create_time, update_time,
            max_pool_size, min_idle, connection_timeout, idle_timeout, max_lifetime, properties
        ) VALUES (
            #{name}, #{type}, #{jdbcUrl}, #{username}, #{password}, #{driverClassName}, #{databaseName},
            #{isDefault}, #{enabled}, #{description}, #{createTime}, #{updateTime},
            #{maxPoolSize}, #{minIdle}, #{connectionTimeout}, #{idleTimeout}, #{maxLifetime}, #{properties}
        )
    </insert>

    <!-- 更新数据源配置 -->
    <update id="updateById" parameterType="com.chinamobile.cmss.ops.fms.chatbi.entity.DataSourceConfigEntity">
        UPDATE data_source_config
        SET
            name = #{et.name},
            type = #{et.type},
            jdbc_url = #{et.jdbcUrl},
            username = #{et.username},
            password = #{et.password},
            driver_class_name = #{et.driverClassName},
            database_name = #{et.databaseName},
            is_default = #{et.isDefault},
            enabled = #{et.enabled},
            description = #{et.description},
            update_time = #{et.updateTime},
            max_pool_size = #{et.maxPoolSize},
            min_idle = #{et.minIdle},
            connection_timeout = #{et.connectionTimeout},
            idle_timeout = #{et.idleTimeout},
            max_lifetime = #{et.maxLifetime},
            properties = #{et.properties}
        WHERE id = #{et.id}
    </update>

    <!-- 删除数据源配置 -->
    <delete id="deleteById">
        DELETE FROM data_source_config WHERE id = #{id}
    </delete>

    <!-- 根据ID查询数据源配置 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM data_source_config WHERE id = #{id}
    </select>

    <!-- 根据名称查询数据源配置 -->
    <select id="selectByName" resultMap="BaseResultMap">
        SELECT * FROM data_source_config WHERE name = #{name} LIMIT 1
    </select>

    <!-- 根据类型查询数据源配置 -->
    <select id="selectByType" resultMap="BaseResultMap">
        SELECT * FROM data_source_config WHERE type = #{type} AND enabled = true LIMIT 1
    </select>

    <!-- 查询默认数据源配置 -->
    <select id="selectDefault" resultMap="BaseResultMap">
        SELECT * FROM data_source_config WHERE is_default = true AND enabled = true LIMIT 1
    </select>

    <!-- 查询所有启用的数据源配置 -->
    <select id="selectAllEnabled" resultMap="BaseResultMap">
        SELECT * FROM data_source_config WHERE enabled = true ORDER BY is_default DESC, id ASC
    </select>

    <!-- 清除所有默认标记 -->
    <update id="clearAllDefault">
        UPDATE data_source_config SET is_default = false
    </update>

    <!-- 设置默认数据源 -->
    <update id="setDefault">
        UPDATE data_source_config SET is_default = true WHERE id = #{id}
    </update>

    <!-- 设置数据源启用状态 -->
    <update id="setEnabled">
        UPDATE data_source_config SET enabled = #{enabled} WHERE id = #{id}
    </update>

    <!-- 自定义更新数据源配置 -->
    <update id="updateDataSourceConfig">
        UPDATE data_source_config
        SET
            name = #{config.name},
            type = #{config.type},
            jdbc_url = #{config.jdbcUrl},
            username = #{config.username},
            password = #{config.password},
            driver_class_name = #{config.driverClassName},
            database_name = #{config.databaseName},
            is_default = #{config.isDefault},
            enabled = #{config.enabled},
            description = #{config.description},
            update_time = #{config.updateTime},
            max_pool_size = #{config.maxPoolSize},
            min_idle = #{config.minIdle},
            connection_timeout = #{config.connectionTimeout},
            idle_timeout = #{config.idleTimeout},
            max_lifetime = #{config.maxLifetime},
            properties = #{config.properties}
        WHERE id = #{config.id}
    </update>
</mapper>
