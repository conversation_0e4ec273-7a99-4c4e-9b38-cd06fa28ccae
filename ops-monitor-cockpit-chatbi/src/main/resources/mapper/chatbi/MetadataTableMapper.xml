<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.cmss.ops.fms.chatbi.mapper.MetadataTableMapper">

    <!-- 分页查询启用的元数据表 -->
    <select id="selectEnabledTablesPage" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataTable">
        SELECT id, data_source_id, database_name, table_name, description, enable, sort_order,
               create_time, update_time, create_by, update_by
        FROM metadata_table
        WHERE enable = 1
        ORDER BY sort_order DESC, id ASC
    </select>

    <!-- 查询所有启用的元数据表 -->
    <select id="selectEnabledTables" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataTable">
        SELECT id, data_source_id, database_name, table_name, description, enable, sort_order,
               create_time, update_time, create_by, update_by
        FROM metadata_table
        WHERE enable = 1
        ORDER BY sort_order DESC, id ASC
    </select>

    <!-- 根据数据库名称查询启用的元数据表 -->
    <select id="selectEnabledTablesByDatabase" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataTable">
        SELECT id, data_source_id, database_name, table_name, description, enable, sort_order,
               create_time, update_time, create_by, update_by
        FROM metadata_table
        WHERE enable = 1
        AND database_name = #{databaseName}
        ORDER BY sort_order DESC, id ASC
    </select>

    <!-- 根据数据源ID和数据库名称查询启用的元数据表 -->
    <select id="selectEnabledTablesByDataSourceAndDatabase" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataTable">
        SELECT id, data_source_id, database_name, table_name, description, enable, sort_order,
               create_time, update_time, create_by, update_by
        FROM metadata_table
        WHERE enable = 1
        AND data_source_id = #{dataSourceId}
        AND database_name = #{databaseName}
        ORDER BY sort_order DESC, id ASC
    </select>
    <!-- 基本的查询方法，用于支持 BaseMapper 的 selectList 方法 -->
    <select id="selectList" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataTable">
        SELECT id, data_source_id, database_name, table_name, description, enable, sort_order,
               create_time, update_time, create_by, update_by
        FROM metadata_table
        <if test="ew != null">
            ${ew.customSqlSegment}
        </if>
    </select>

    <!-- 根据ID查询元数据表，用于支持 BaseMapper 的 selectById 方法 -->
    <select id="selectById" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataTable">
        SELECT id, data_source_id, database_name, table_name, description, enable, sort_order,
               create_time, update_time, create_by, update_by
        FROM metadata_table
        WHERE id = #{id}
    </select>

    <!-- 插入元数据表，用于支持 BaseMapper 的 insert 方法 -->
    <insert id="insert" parameterType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataTable" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO metadata_table (
            data_source_id, database_name, table_name, description, enable, sort_order,
            create_time, update_time, create_by, update_by
        ) VALUES (
            #{dataSourceId}, #{databaseName}, #{tableName}, #{description}, #{enable}, #{sortOrder},
            #{createTime}, #{updateTime}, #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 根据ID更新元数据表，用于支持 BaseMapper 的 updateById 方法 -->
    <update id="updateById" parameterType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataTable">
        UPDATE metadata_table
        SET data_source_id = #{dataSourceId},
            database_name = #{databaseName},
            table_name = #{tableName},
            description = #{description},
            enable = #{enable},
            sort_order = #{sortOrder},
            update_time = #{updateTime},
            update_by = #{updateBy}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除元数据表，用于支持 BaseMapper 的 deleteById 方法 -->
    <delete id="deleteById">
        DELETE FROM metadata_table
        WHERE id = #{id}
    </delete>
</mapper>
