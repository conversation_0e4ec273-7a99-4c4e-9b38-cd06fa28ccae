<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.cmss.ops.fms.chatbi.mapper.MetadataColumnMapper">

    <!-- 根据表ID查询启用的列 -->
    <select id="selectEnabledColumnsByTableId" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataColumn">
        SELECT id, table_id, column_name, data_type, primary_key, description, chinese_name,
               enable, sort_order, create_time, update_time, create_by, update_by
        FROM metadata_column
        WHERE enable = 1
        AND table_id = #{tableId}
        ORDER BY sort_order DESC, id ASC
    </select>

    <!-- 根据表名查询启用的列 -->
    <select id="selectEnabledColumnsByTableName" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataColumn">
        SELECT c.id, c.table_id, c.column_name, c.data_type, c.primary_key, c.description, c.chinese_name,
               c.enable, c.sort_order, c.create_time, c.update_time, c.create_by, c.update_by
        FROM metadata_column c
        JOIN metadata_table t ON c.table_id = t.id
        WHERE c.enable = 1
        AND t.table_name = #{tableName}
        ORDER BY c.sort_order DESC, c.id ASC
    </select>

    <!-- 基本的查询方法，用于支持 BaseMapper 的 selectList 方法 -->
    <select id="selectList" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataColumn">
        SELECT id, table_id, column_name, data_type, primary_key, description, chinese_name,
               enable, sort_order, create_time, update_time, create_by, update_by
        FROM metadata_column
        <if test="ew != null">
            ${ew.customSqlSegment}
        </if>
    </select>

    <!-- 根据ID查询元数据列，用于支持 BaseMapper 的 selectById 方法 -->
    <select id="selectById" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataColumn">
        SELECT id, table_id, column_name, data_type, primary_key, description, chinese_name,
               enable, sort_order, create_time, update_time, create_by, update_by
        FROM metadata_column
        WHERE id = #{id}
    </select>

    <!-- 插入元数据列，用于支持 BaseMapper 的 insert 方法 -->
    <insert id="insert" parameterType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataColumn" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO metadata_column (
            table_id, column_name, data_type, primary_key, description, chinese_name,
            enable, sort_order, create_time, update_time, create_by, update_by
        ) VALUES (
            #{tableId}, #{columnName}, #{dataType}, #{primaryKey}, #{description}, #{chineseName},
            #{enable}, #{sortOrder}, #{createTime}, #{updateTime}, #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 根据ID更新元数据列，用于支持 BaseMapper 的 updateById 方法 -->
    <update id="updateById" parameterType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataColumn">
        UPDATE metadata_column
        SET table_id = #{et.tableId},
            column_name = #{et.columnName},
            data_type = #{et.dataType},
            primary_key = #{et.primaryKey},
            description = #{et.description},
            chinese_name = #{et.chineseName},
            enable = #{et.enable},
            sort_order = #{et.sortOrder},
            update_time = #{et.updateTime},
            update_by = #{et.updateBy}
        WHERE id = #{et.id}
    </update>

    <!-- 根据ID删除元数据列，用于支持 BaseMapper 的 deleteById 方法 -->
    <delete id="deleteById">
        DELETE FROM metadata_column
        WHERE id = #{id}
    </delete>
</mapper>
