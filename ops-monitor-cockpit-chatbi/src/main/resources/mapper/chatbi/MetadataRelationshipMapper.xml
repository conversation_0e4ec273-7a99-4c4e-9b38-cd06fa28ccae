<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.cmss.ops.fms.chatbi.mapper.MetadataRelationshipMapper">

    <!-- 查询所有启用的表关系 -->
    <select id="selectEnabledRelationships" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataRelationship">
        SELECT id, source_table, source_column, target_table, target_column,
               relationship_type, enable, sort_order, create_time, update_time, create_by, update_by
        FROM metadata_relationship
        WHERE enable = 1
        ORDER BY sort_order DESC, id ASC
    </select>

    <!-- 基本的查询方法，用于支持 BaseMapper 的 selectList 方法 -->
    <select id="selectList" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataRelationship">
        SELECT id, source_table, source_column, target_table, target_column,
               relationship_type, enable, sort_order, create_time, update_time, create_by, update_by
        FROM metadata_relationship
        <if test="ew != null">
            ${ew.customSqlSegment}
        </if>
    </select>

    <!-- 根据ID查询元数据关系，用于支持 BaseMapper 的 selectById 方法 -->
    <select id="selectById" resultType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataRelationship">
        SELECT id, source_table, source_column, target_table, target_column,
               relationship_type, enable, sort_order, create_time, update_time, create_by, update_by
        FROM metadata_relationship
        WHERE id = #{id}
    </select>

    <!-- 插入元数据关系，用于支持 BaseMapper 的 insert 方法 -->
    <insert id="insert" parameterType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataRelationship" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO metadata_relationship (
            source_table, source_column, target_table, target_column,
            relationship_type, enable, sort_order, create_time, update_time, create_by, update_by
        ) VALUES (
            #{sourceTable}, #{sourceColumn}, #{targetTable}, #{targetColumn},
            #{relationshipType}, #{enable}, #{sortOrder}, #{createTime}, #{updateTime}, #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 根据ID更新元数据关系，用于支持 BaseMapper 的 updateById 方法 -->
    <update id="updateById" parameterType="com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataRelationship">
        UPDATE metadata_relationship
        SET source_table = #{sourceTable},
            source_column = #{sourceColumn},
            target_table = #{targetTable},
            target_column = #{targetColumn},
            relationship_type = #{relationshipType},
            enable = #{enable},
            sort_order = #{sortOrder},
            update_time = #{updateTime},
            update_by = #{updateBy}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除元数据关系，用于支持 BaseMapper 的 deleteById 方法 -->
    <delete id="deleteById">
        DELETE FROM metadata_relationship
        WHERE id = #{id}
    </delete>
</mapper>
