<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>告警数据自然语言查询</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 20px 30px;
        }
        .header {
            margin-bottom: 30px;
            text-align: center;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }
        .header h1 {
            color: #2c3e50;
            font-weight: 600;
        }
        .nav-links {
            margin-top: 15px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 8px 15px;
            text-decoration: none;
            color: #6c757d;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .nav-links a:hover {
            background-color: #e9ecef;
            color: #2c3e50;
        }
        .nav-links a.active {
            color: #3498db;
            font-weight: bold;
            border-bottom: 2px solid #3498db;
            background-color: rgba(52, 152, 219, 0.1);
        }
        .query-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }
        .result-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }
        .sql-display {
            font-family: 'Fira Code', 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 18px;
            border-radius: 8px;
            margin-bottom: 20px;
            position: relative;
            border: 1px solid #e9ecef;
        }
        .sql-explanation {
            background-color: #ebf5fb;
            padding: 18px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
            color: #2c3e50;
        }
        .result-table {
            overflow-x: auto;
            margin-top: 15px;
        }
        .result-table table {
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            border-radius: 8px;
            overflow: hidden;
        }
        .result-table th {
            background-color: #f1f8ff;
            color: #2c3e50;
            font-weight: 600;
            padding: 12px 15px;
            text-align: left;
            border-bottom: 2px solid #e9ecef;
        }
        .result-table td {
            padding: 10px 15px;
            border-bottom: 1px solid #e9ecef;
        }
        .result-table tr:last-child td {
            border-bottom: none;
        }
        .result-table tr:hover {
            background-color: #f8f9fa;
        }
        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background-color: #e9ecef;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #2c3e50;
        }
        .copy-btn:hover {
            background-color: #d4d9de;
        }
        .chart-container {
            height: 400px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #fff;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .feedback-container {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        .spinner {
            width: 3rem;
            height: 3rem;
            color: #3498db;
        }
        .history-item {
            cursor: pointer;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .history-item:hover {
            background-color: #f1f8ff;
            border-left: 3px solid #3498db;
        }
        /* 会话列表样式 */
        .conversation-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
            border-left: 3px solid transparent;
        }
        .conversation-item:hover {
            background-color: #f1f8ff;
            transform: translateX(2px);
        }
        .conversation-item.active {
            background-color: #e3f2fd;
            border-left: 3px solid #3498db;
        }
        .conversation-icon {
            margin-right: 12px;
            color: #3498db;
            font-size: 1.2rem;
        }
        .conversation-info {
            flex: 1;
            overflow: hidden;
        }
        .conversation-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .conversation-preview {
            font-size: 0.85rem;
            color: #7f8c8d;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .conversation-time {
            font-size: 0.75rem;
            color: #95a5a6;
        }
        .examples-container {
            margin-top: 20px;
        }
        .example-query {
            cursor: pointer;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 8px;
            background-color: #f1f8ff;
            transition: all 0.3s ease;
            border-left: 3px solid #3498db;
            color: #2c3e50;
        }
        .example-query:hover {
            background-color: #d6eaf8;
            transform: translateX(3px);
        }
        .tab-content {
            padding-top: 20px;
        }
        /* 对话历史样式 */
        .conversation-exchanges {
            max-height: calc(100vh - 350px);
            overflow-y: auto;
            padding-right: 5px;
        }
        .conversation-exchanges::-webkit-scrollbar {
            width: 6px;
        }
        .conversation-exchanges::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        .conversation-exchanges::-webkit-scrollbar-thumb {
            background: #c1d9f0;
            border-radius: 10px;
        }
        .conversation-exchanges::-webkit-scrollbar-thumb:hover {
            background: #3498db;
        }
        .conversation-exchange {
            padding: 16px;
            margin-bottom: 20px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            border: 1px solid #eaeaea;
        }
        .conversation-exchange:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .user-query {
            margin-bottom: 12px;
            font-weight: 500;
            color: #2c3e50;
            align-self: flex-start;
            background-color: #f0f7ff;
            padding: 12px 18px;
            border-radius: 18px 18px 18px 0;
            max-width: 85%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            border-left: 3px solid #3498db;
        }
        .system-response {
            align-self: flex-start;
            background-color: #ffffff;
            padding: 15px 20px;
            border-radius: 12px;
            width: 100%;
            margin-top: 10px;
            border: 1px solid #eaeaea;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.03);
        }
        .query-label, .response-label {
            font-weight: bold;
            color: #3498db;
            padding: 2px 5px;
            border-radius: 3px;
            background-color: rgba(52, 152, 219, 0.1);
            display: none; /* 隐藏标签，使用气泡样式代替 */
        }
        .system-response code {
            display: block;
            margin-top: 8px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            font-size: 0.9em;
            white-space: pre-wrap;
            word-break: break-all;
            border: 1px solid #e9ecef;
            font-family: 'Fira Code', 'Courier New', monospace;
            color: #333;
            line-height: 1.5;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
            overflow-x: auto;
        }
        .conversation-sql {
            margin: 5px 0 15px 0;
            position: relative;
        }
        .conversation-sql code {
            background-color: #f8f9fa;
            border-left: 3px solid #3498db;
        }
        .copy-sql-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: #f1f1f1;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            color: #555;
            transition: all 0.2s ease;
            z-index: 2;
        }
        .copy-sql-btn:hover {
            background-color: #e1e1e1;
            color: #333;
        }
        .conversation-result-table {
            margin: 5px 0 15px 0;
            max-height: 350px;
            overflow-y: auto;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
        }
        .conversation-result-table table {
            width: 100%;
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
        }
        .conversation-result-table th {
            position: sticky;
            top: 0;
            background-color: #f1f8ff;
            z-index: 1;
            padding: 12px 15px;
            font-weight: 600;
            color: #2c3e50;
            border-bottom: 2px solid #e9ecef;
            text-align: left;
        }
        .conversation-result-table td {
            padding: 10px 15px;
            border-bottom: 1px solid #f0f0f0;
            color: #333;
        }
        .conversation-result-table tr:last-child td {
            border-bottom: none;
        }
        .conversation-result-table tr:hover {
            background-color: #f8f9fa;
        }
        .conversation-chart-container {
            height: 300px;
            margin: 5px 0 15px 0;
            padding: 15px;
            background-color: #fff;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
        }
        .conversation-tabs {
            margin-top: 18px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            flex-wrap: wrap;
            padding-left: 0;
            margin-bottom: 0;
            list-style: none;
            gap: 5px;
        }
        .conversation-tabs .nav-item {
            margin-bottom: -1px;
        }
        .conversation-tabs .nav-link {
            padding: 10px 18px;
            border-radius: 8px 8px 0 0;
            margin-right: 2px;
            font-size: 0.9rem;
            color: #6c757d;
            cursor: pointer;
            border: 1px solid transparent;
            transition: all 0.2s ease;
            text-decoration: none;
            display: block;
        }
        .conversation-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #e9ecef;
            color: #3498db;
        }
        .conversation-tabs .nav-link.active {
            background-color: #f8faff;
            color: #3498db;
            border: 1px solid #e9ecef;
            border-bottom: 1px solid #f8faff;
            font-weight: 500;
        }
        .conversation-tab-content {
            padding: 20px 0 5px 0;
            background-color: #f8faff;
            border: 1px solid #e9ecef;
            border-top: none;
            border-radius: 0 0 8px 8px;
        }
        .conversation-tab-pane {
            display: none;
            padding: 0 15px 15px 15px;
        }
        .conversation-tab-pane.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .response-meta {
            font-size: 0.85em;
            color: #6c757d;
            margin: 10px 0;
            padding: 5px 10px;
            background-color: #f8f9fa;
            border-radius: 6px;
            display: inline-block;
            border-left: 2px solid #3498db;
        }
        .exchange-time {
            font-size: 0.75em;
            color: #95a5a6;
            text-align: right;
            margin: 10px 0 0 0;
            font-style: italic;
        }
        .system-response.loading {
            color: #7f8c8d;
            font-style: italic;
        }
        .system-response.loading i {
            animation: spin 1.5s linear infinite;
            margin-right: 5px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        /* 会话窗口样式 */
        .chat-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 250px);
            background-color: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }
        .chat-header {
            padding: 15px 20px;
            background-color: #ffffff;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        .chat-title {
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
            display: flex;
            align-items: center;
        }
        .chat-title i {
            margin-right: 8px;
            color: #3498db;
        }
        .chat-body {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background-color: #f8f9fa;
            background-image: linear-gradient(rgba(52, 152, 219, 0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(52, 152, 219, 0.03) 1px, transparent 1px);
            background-size: 20px 20px;
        }
        .chat-footer {
            padding: 15px 20px;
            background-color: #ffffff;
            border-top: 1px solid #e9ecef;
        }
        /* 查询输入框样式 */
        #nlQuery {
            border-radius: 8px 0 0 8px;
            padding: 12px 15px;
            border: 1px solid #e9ecef;
            box-shadow: none;
            transition: all 0.3s ease;
        }
        #nlQuery:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        #queryForm .btn-primary {
            background-color: #3498db;
            border-color: #3498db;
            border-radius: 0 8px 8px 0;
            padding: 12px 20px;
            transition: all 0.3s ease;
        }
        #queryForm .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            transform: translateX(2px);
        }
        #dataSourceSelect {
            border-radius: 8px;
            padding: 10px 15px;
            border: 1px solid #e9ecef;
            background-color: #f8f9fa;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>告警数据自然语言查询</h1>
            <p class="text-muted">使用自然语言查询告警数据</p>
            <div class="nav-links">
                <a href="index.html" class="active">首页</a>
                <a href="metadata-management.html">元数据管理</a>
                <a href="datasource-management.html">数据源管理</a>
            </div>
        </div>

        <div class="row gx-4">
            <div class="col-md-3">
                <div class="card shadow-sm" style="border-radius: 15px; border: none; height: calc(100vh - 150px); display: flex; flex-direction: column;">
                    <div class="card-header d-flex justify-content-between align-items-center" style="background-color: #f1f8ff; border-radius: 15px 15px 0 0; border-bottom: 1px solid #e9ecef;">
                        <h5 class="mb-0" style="color: #2c3e50; font-weight: 600;">会话列表</h5>
                        <button class="btn btn-sm btn-primary" id="newChatBtn" title="创建新会话" style="border-radius: 8px; transition: all 0.3s ease;">
                            <i class="bi bi-plus-lg"></i> 新会话
                        </button>
                    </div>
                    <div class="card-body" style="padding: 15px; overflow-y: auto; flex-grow: 1;">
                        <div id="conversationList">
                            <!-- 会话列表将在这里动态生成 -->
                            <div class="conversation-item active" data-id="new" onclick="switchConversation('new')">
                                <div class="conversation-icon">
                                    <i class="bi bi-chat-dots"></i>
                                </div>
                                <div class="conversation-info">
                                    <div class="conversation-title">新对话</div>
                                    <div class="conversation-preview">开始新的对话...</div>
                                </div>
                                <div class="conversation-time">Now</div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer" style="background-color: #f8f9fa; border-radius: 0 0 15px 15px; border-top: 1px solid #e9ecef; padding: 15px;">
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-sm btn-outline-primary" id="clearCacheBtn" title="清除查询缓存" style="border-radius: 8px; transition: all 0.3s ease;">
                                <i class="bi bi-trash"></i> 清除缓存
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" id="showExamplesBtn" title="显示示例查询" style="border-radius: 8px; transition: all 0.3s ease;">
                                <i class="bi bi-lightbulb"></i> 示例查询
                            </button>
                        </div>

                        <!-- 示例查询面板，默认隐藏 -->
                        <div id="examplesPanel" class="mt-3" style="display: none;">
                            <div class="examples-container">
                                <div class="example-query" onclick="setExampleQuery('近30天告警数量')">
                                    近30天告警数量
                                </div>
                                <div class="example-query" onclick="setExampleQuery('近30天告警数量按照等级分类统计')">
                                    近30天告警数量按照等级分类统计
                                </div>
                                <div class="example-query" onclick="setExampleQuery('近30天工单号不为空的告警数')">
                                    近30天工单号不为空的告警数
                                </div>
                                <div class="example-query" onclick="setExampleQuery('查询告警id为dr-5qDVH6lxPAejxej6-1724378774964的告警详情')">
                                    查询告警id为dr-5qDVH6lxPAejxej6-1724378774964的告警详情
                                </div>
                                <div class="example-query" onclick="setExampleQuery('近7天各级别告警数量趋势')">
                                    近7天各级别告警数量趋势
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <div class="chat-container">
                    <div class="chat-header">
                        <h5 class="chat-title"><i class="bi bi-chat-square-text"></i> 当前会话</h5>
                        <div>

                            <div class="form-check form-check-inline form-switch">
                                <input class="form-check-input" type="checkbox" id="contextualMode" checked>
                                <label class="form-check-label" for="contextualMode">
                                    对话模式
                                </label>
                            </div>
                            <button id="clearContextBtn" class="btn btn-sm btn-outline-primary ms-2" style="border-radius: 8px; transition: all 0.3s ease;">
                                <i class="bi bi-eraser"></i> 清除上下文
                            </button>
                        </div>
                    </div>

                    <div class="chat-body">
                        <div id="conversationExchanges" class="conversation-exchanges">
                            <!-- 对话历史将在这里动态生成 -->
                            <div class="exchange-time">今天</div>
                            <div class="conversation-exchange">
                                <div class="user-query">欢迎使用ChatBI，您可以开始提问了</div>
                                <div class="system-response">您好！我是ChatBI助手，请问有什么可以帮您查询的数据？</div>
                            </div>
                        </div>
                    </div>

                    <div class="chat-footer">
                        <form id="queryForm" class="d-flex">
                            <div class="form-group me-2" style="width: 150px;">
                                <select class="form-select" id="dataSourceSelect">
                                    <!-- 数据源选项将通过JavaScript动态加载 -->
                                </select>
                            </div>
                            <div class="input-group">
                                <input type="text" class="form-control" id="nlQuery"
                                       placeholder="例如：近30天告警数量按照等级分类统计">
                                <button type="submit" class="btn btn-primary" style="padding: 12px 25px;">
                                    <i class="bi bi-send"></i> 发送
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div id="loadingIndicator" class="loading" style="display: none;">
                    <div class="spinner-border text-primary spinner" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <div id="resultSection" style="display: none;" class="animate__animated animate__fadeIn">
                    <div class="result-card">
                        <ul class="nav nav-tabs" id="resultTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="results-tab" data-bs-toggle="tab"
                                        data-bs-target="#results" type="button" role="tab"
                                        aria-controls="results" aria-selected="true">
                                    查询结果
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="sql-tab" data-bs-toggle="tab"
                                        data-bs-target="#sql" type="button" role="tab"
                                        aria-controls="sql" aria-selected="false">
                                    SQL查询
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="visualization-tab" data-bs-toggle="tab"
                                        data-bs-target="#visualization" type="button" role="tab"
                                        aria-controls="visualization" aria-selected="false">
                                    可视化
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="resultTabsContent">
                            <div class="tab-pane fade show active" id="results" role="tabpanel"
                                 aria-labelledby="results-tab">


                                <div class="result-table">
                                    <table class="table table-striped" id="resultTable">
                                        <!-- 结果将在这里动态生成 -->
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="sql" role="tabpanel" aria-labelledby="sql-tab">
                                <div class="sql-display" id="sqlDisplay">
                                    <!-- SQL查询将在这里显示 -->
                                    <button class="copy-btn" onclick="copySql()">
                                        <i class="bi bi-clipboard"></i> 复制
                                    </button>
                                </div>

                                <div class="mb-3">
                                    <label for="sqlEditor" class="form-label">编辑SQL查询：</label>
                                    <textarea class="form-control" id="sqlEditor" rows="5"></textarea>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-primary" onclick="executeEditedSql()">
                                        <i class="bi bi-play"></i> 执行SQL
                                    </button>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="visualization" role="tabpanel"
                                 aria-labelledby="visualization-tab">
                                <div class="mb-3">
                                    <label for="chartType" class="form-label">图表类型：</label>
                                    <select class="form-select" id="chartType" onchange="updateChart()">
                                        <option value="bar">柱状图</option>
                                        <option value="line">折线图</option>
                                        <option value="pie">饼图</option>
                                    </select>
                                </div>

                                <div class="chart-container">
                                    <canvas id="resultChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="feedback-container">
                        <h5>这个结果对您有帮助吗？</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-success feedback-btn" data-value="helpful">
                                <i class="bi bi-hand-thumbs-up"></i> 有帮助
                            </button>
                            <button type="button" class="btn btn-outline-danger feedback-btn" data-value="not-helpful">
                                <i class="bi bi-hand-thumbs-down"></i> 没帮助
                            </button>
                        </div>
                        <div class="mt-2" id="feedbackForm" style="display: none;">
                            <textarea class="form-control" id="feedbackText"
                                      placeholder="请告诉我们如何改进..."></textarea>
                            <button class="btn btn-sm btn-primary mt-2" id="submitFeedback">提交反馈</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/sql.min.js"></script>
    <script src="js/api-utils.js"></script>
    <script src="js/conversation.js"></script>
    <script src="js/main.js"></script>

    <!-- 消息容器 -->
    <div id="messageContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>
</body>
</html>
