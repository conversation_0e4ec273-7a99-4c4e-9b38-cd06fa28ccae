<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据源管理</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="js/api-utils.js"></script>
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            margin-bottom: 30px;
            text-align: center;
        }
        .nav-links {
            margin-top: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 5px 10px;
            text-decoration: none;
            color: #6c757d;
            border-radius: 5px;
        }
        .nav-links a:hover {
            background-color: #e9ecef;
        }
        .nav-links a.active {
            color: #0d6efd;
            font-weight: bold;
            border-bottom: 2px solid #0d6efd;
        }
        .datasource-management {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
        }
        .search-bar {
            margin-bottom: 20px;
        }
        .table-container {
            margin-bottom: 20px;
        }
        .form-container {
            max-width: 600px;
        }
        .form-footer {
            margin-top: 20px;
            text-align: right;
        }
        .el-form-item__label {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>ChatBI - 数据查询平台</h1>
                <div class="nav-links">
                    <a href="index.html">首页</a>
                    <a href="metadata-management.html">元数据管理</a>
                    <a href="datasource-management.html" class="active">数据源管理</a>
                </div>
            </div>

            <div class="datasource-management">
            <div class="page-header">
                <h2>数据源管理</h2>
                <el-button type="primary" @click="showAddDialog">添加数据源</el-button>
            </div>

            <div class="table-container">
                <el-table :data="datasources" border style="width: 100%">
                    <el-table-column prop="id" label="ID" width="80"></el-table-column>
                    <el-table-column prop="name" label="数据源名称" width="180"></el-table-column>
                    <el-table-column prop="type" label="数据源类型" width="120"></el-table-column>
                    <el-table-column prop="databaseName" label="数据库名称" width="150"></el-table-column>
                    <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
                    <el-table-column label="状态" width="100">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.enabled ? 'success' : 'danger'">
                                {{ scope.row.enabled ? '启用' : '禁用' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="默认" width="80">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.isDefault" type="primary">默认</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="250">
                        <template slot-scope="scope">
                            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
                            <el-button size="mini" type="success" @click="handleTest(scope.row)">测试连接</el-button>
                            <el-button size="mini" type="primary" @click="handleSetDefault(scope.row)" :disabled="scope.row.isDefault">设为默认</el-button>
                            <el-button size="mini" :type="scope.row.enabled ? 'warning' : 'success'" @click="handleToggleEnabled(scope.row)">
                                {{ scope.row.enabled ? '禁用' : '启用' }}
                            </el-button>
                            <el-button size="mini" type="danger" @click="handleDelete(scope.row)" :disabled="scope.row.isDefault">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>

            <!-- 添加/编辑数据源对话框 -->
            <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%">
                <div class="form-container">
                    <el-form :model="form" :rules="rules" ref="form" label-width="120px">
                        <el-form-item label="数据源名称" prop="name">
                            <el-input v-model="form.name" placeholder="请输入数据源名称"></el-input>
                        </el-form-item>
                        <el-form-item label="数据源类型" prop="type">
                            <el-select v-model="form.type" placeholder="请选择数据源类型">
                                <el-option label="MySQL" value="mysql"></el-option>
                                <el-option label="PostgreSQL" value="postgresql"></el-option>
                                <el-option label="Doris" value="doris"></el-option>
                                <el-option label="Oracle" value="oracle"></el-option>
                                <el-option label="SQL Server" value="sqlserver"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="JDBC URL" prop="jdbcUrl">
                            <el-input v-model="form.jdbcUrl" placeholder="请输入JDBC URL"></el-input>
                        </el-form-item>
                        <el-form-item label="用户名" prop="username">
                            <el-input v-model="form.username" placeholder="请输入用户名"></el-input>
                        </el-form-item>
                        <el-form-item label="密码" prop="password">
                            <el-input v-model="form.password" type="password" placeholder="请输入密码"></el-input>
                        </el-form-item>
                        <el-form-item label="驱动类名" prop="driverClassName">
                            <el-input v-model="form.driverClassName" placeholder="请输入驱动类名"></el-input>
                        </el-form-item>
                        <el-form-item label="数据库名称" prop="databaseName">
                            <el-input v-model="form.databaseName" placeholder="请输入数据库名称"></el-input>
                        </el-form-item>
                        <el-form-item label="是否默认">
                            <el-switch v-model="form.isDefault"></el-switch>
                        </el-form-item>
                        <el-form-item label="是否启用">
                            <el-switch v-model="form.enabled"></el-switch>
                        </el-form-item>
                        <el-form-item label="描述">
                            <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入描述"></el-input>
                        </el-form-item>
                        <el-form-item label="最大连接数">
                            <el-input-number v-model="form.maxPoolSize" :min="1" :max="100"></el-input-number>
                        </el-form-item>
                        <el-form-item label="最小空闲连接">
                            <el-input-number v-model="form.minIdle" :min="1" :max="50"></el-input-number>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="form-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitForm">确定</el-button>
                    <el-button type="success" @click="testConnection">测试连接</el-button>
                </div>
            </el-dialog>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    datasources: [],
                    currentPage: 1,
                    pageSize: 10,
                    total: 0,
                    dialogVisible: false,
                    dialogTitle: '添加数据源',
                    isEdit: false,
                    form: {
                        id: null,
                        name: '',
                        type: '',
                        jdbcUrl: '',
                        username: '',
                        password: '',
                        driverClassName: '',
                        databaseName: '',
                        isDefault: false,
                        enabled: true,
                        description: '',
                        maxPoolSize: 10,
                        minIdle: 5,
                        connectionTimeout: 30000,
                        idleTimeout: 600000,
                        maxLifetime: 1800000
                    },
                    rules: {
                        name: [
                            { required: true, message: '请输入数据源名称', trigger: 'blur' },
                            { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
                        ],
                        type: [
                            { required: true, message: '请选择数据源类型', trigger: 'change' }
                        ],
                        jdbcUrl: [
                            { required: true, message: '请输入JDBC URL', trigger: 'blur' }
                        ],
                        username: [
                            { required: true, message: '请输入用户名', trigger: 'blur' }
                        ],
                        password: [
                            { required: true, message: '请输入密码', trigger: 'blur' }
                        ],
                        driverClassName: [
                            { required: true, message: '请输入驱动类名', trigger: 'blur' }
                        ],
                        databaseName: [
                            { required: true, message: '请输入数据库名称', trigger: 'blur' }
                        ]
                    }
                };
            },
            created() {
                this.fetchData();
            },
            methods: {
                fetchData() {
                    axios.get(`/api/datasource-config?page=${this.currentPage}&pageSize=${this.pageSize}`)
                        .then(response => {
                            // 处理统一响应格式
                            handleApiResponse(response.data,
                                data => {
                                    this.datasources = data.records;
                                    this.total = data.total;
                                },
                                error => {
                                    console.error('获取数据源列表失败:', error);
                                    this.$message.error('获取数据源列表失败: ' + error);
                                }
                            );
                        })
                        .catch(error => {
                            console.error('获取数据源列表失败:', error);
                            this.$message.error('获取数据源列表失败');
                        });
                },
                handleSizeChange(val) {
                    this.pageSize = val;
                    this.fetchData();
                },
                handleCurrentChange(val) {
                    this.currentPage = val;
                    this.fetchData();
                },
                showAddDialog() {
                    this.isEdit = false;
                    this.dialogTitle = '添加数据源';
                    this.resetForm();
                    this.dialogVisible = true;
                },
                handleEdit(row) {
                    this.isEdit = true;
                    this.dialogTitle = '编辑数据源';
                    this.form = { ...row };
                    this.dialogVisible = true;
                },
                handleTest(row) {
                    this.testConnectionWithConfig(row);
                },
                handleSetDefault(row) {
                    this.$confirm('确认将该数据源设为默认数据源?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.put(`/api/datasource-config/${row.id}/default`)
                            .then(response => {
                                // 处理统一响应格式
                                handleApiResponse(response.data,
                                    data => {
                                        if (data.success) {
                                            this.$message.success('设置默认数据源成功');
                                            this.fetchData();
                                        } else {
                                            this.$message.error(data.message || '设置默认数据源失败');
                                        }
                                    },
                                    error => {
                                        this.$message.error('设置默认数据源失败: ' + error);
                                    }
                                );
                            })
                            .catch(error => {
                                console.error('设置默认数据源失败:', error);
                                this.$message.error('设置默认数据源失败');
                            });
                    }).catch(() => {
                        // 取消操作
                    });
                },
                handleToggleEnabled(row) {
                    const action = row.enabled ? '禁用' : '启用';
                    this.$confirm(`确认${action}该数据源?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.put(`/api/datasource-config/${row.id}/enabled?enabled=${!row.enabled}`)
                            .then(response => {
                                // 处理统一响应格式
                                handleApiResponse(response.data,
                                    data => {
                                        if (data.success) {
                                            this.$message.success(`${action}数据源成功`);
                                            this.fetchData();
                                        } else {
                                            this.$message.error(data.message || `${action}数据源失败`);
                                        }
                                    },
                                    error => {
                                        this.$message.error(`${action}数据源失败: ` + error);
                                    }
                                );
                            })
                            .catch(error => {
                                console.error(`${action}数据源失败:`, error);
                                this.$message.error(`${action}数据源失败`);
                            });
                    }).catch(() => {
                        // 取消操作
                    });
                },
                handleDelete(row) {
                    this.$confirm('确认删除该数据源? 此操作不可逆!', '警告', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'danger'
                    }).then(() => {
                        axios.delete(`/api/datasource-config/${row.id}`)
                            .then(response => {
                                // 处理统一响应格式
                                handleApiResponse(response.data,
                                    data => {
                                        this.$message.success('删除数据源成功');
                                        this.fetchData();
                                    },
                                    error => {
                                        this.$message.error('删除数据源失败: ' + error);
                                    }
                                );
                            })
                            .catch(error => {
                                console.error('删除数据源失败:', error);
                                if (error.response && error.response.data) {
                                    // 尝试提取错误消息
                                    if (error.response.data.meta && error.response.data.meta.message) {
                                        this.$message.error('删除数据源失败: ' + error.response.data.meta.message);
                                    } else {
                                        this.$message.error('删除数据源失败: ' + (error.response.data.message || error.message));
                                    }
                                } else {
                                    this.$message.error('删除数据源失败');
                                }
                            });
                    }).catch(() => {
                        // 取消操作
                    });
                },
                resetForm() {
                    this.form = {
                        id: null,
                        name: '',
                        type: '',
                        jdbcUrl: '',
                        username: '',
                        password: '',
                        driverClassName: '',
                        databaseName: '',
                        isDefault: false,
                        enabled: true,
                        description: '',
                        maxPoolSize: 10,
                        minIdle: 5,
                        connectionTimeout: 30000,
                        idleTimeout: 600000,
                        maxLifetime: 1800000
                    };
                    if (this.$refs.form) {
                        this.$refs.form.resetFields();
                    }
                },
                submitForm() {
                    this.$refs.form.validate((valid) => {
                        if (valid) {
                            if (this.isEdit) {
                                // 编辑数据源
                                axios.put(`/api/datasource-config/${this.form.id}`, this.form)
                                    .then(response => {
                                        // 处理统一响应格式
                                        handleApiResponse(response.data,
                                            data => {
                                                this.$message.success('更新数据源成功');
                                                this.dialogVisible = false;
                                                this.fetchData();
                                            },
                                            error => {
                                                this.$message.error('更新数据源失败: ' + error);
                                            }
                                        );
                                    })
                                    .catch(error => {
                                        console.error('更新数据源失败:', error);
                                        this.$message.error('更新数据源失败: ' + (error.response?.data?.message || error.message));
                                    });
                            } else {
                                // 添加数据源
                                axios.post('/api/datasource-config', this.form)
                                    .then(response => {
                                        // 处理统一响应格式
                                        handleApiResponse(response.data,
                                            data => {
                                                this.$message.success('添加数据源成功');
                                                this.dialogVisible = false;
                                                this.fetchData();
                                            },
                                            error => {
                                                this.$message.error('添加数据源失败: ' + error);
                                            }
                                        );
                                    })
                                    .catch(error => {
                                        console.error('添加数据源失败:', error);
                                        this.$message.error('添加数据源失败: ' + (error.response?.data?.message || error.message));
                                    });
                            }
                        } else {
                            return false;
                        }
                    });
                },
                testConnection() {
                    this.$refs.form.validate((valid) => {
                        if (valid) {
                            this.testConnectionWithConfig(this.form);
                        } else {
                            return false;
                        }
                    });
                },
                testConnectionWithConfig(config) {
                    this.$message.info('正在测试连接...');
                    axios.post('/api/datasource-config/test-connection', config)
                        .then(response => {
                            // 处理统一响应格式
                            handleApiResponse(response.data,
                                data => {
                                    this.$message.success('连接测试成功');
                                },
                                error => {
                                    this.$message.error('连接测试失败: ' + error);
                                }
                            );
                        })
                        .catch(error => {
                            console.error('连接测试失败:', error);
                            if (error.response && error.response.data) {
                                // 尝试提取错误消息
                                if (error.response.data.meta && error.response.data.meta.message) {
                                    this.$message.error('连接测试失败: ' + error.response.data.meta.message);
                                } else {
                                    this.$message.error('连接测试失败: ' + (error.response.data.message || error.message));
                                }
                            } else {
                                this.$message.error('连接测试失败: ' + error.message);
                            }
                        });
                },
                // 根据数据源类型自动填充驱动类名
                handleTypeChange() {
                    switch (this.form.type) {
                        case 'mysql':
                            this.form.driverClassName = 'com.mysql.cj.jdbc.Driver';
                            break;
                        case 'postgresql':
                            this.form.driverClassName = 'org.postgresql.Driver';
                            break;
                        case 'doris':
                            this.form.driverClassName = 'com.mysql.cj.jdbc.Driver';
                            break;
                        case 'oracle':
                            this.form.driverClassName = 'oracle.jdbc.OracleDriver';
                            break;
                        case 'sqlserver':
                            this.form.driverClassName = 'com.microsoft.sqlserver.jdbc.SQLServerDriver';
                            break;
                    }
                }
            },
            watch: {
                'form.type': function(val) {
                    if (val) {
                        this.handleTypeChange();
                    }
                }
            }
        });
    </script>
</body>
</html>
