/* 通用样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    margin: 0;
    padding: 0;
    color: #333;
    background-color: #f5f7fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background-color: #409EFF;
    color: white;
    padding: 15px 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header h1 {
    margin: 0;
    font-size: 24px;
}

/* 卡片样式 */
.card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 20px;
}

.card-header {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.card-title {
    margin: 0;
    font-size: 18px;
    color: #303133;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-sizing: border-box;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    cursor: pointer;
    background: #fff;
    border: 1px solid #dcdfe6;
    color: #606266;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    transition: .1s;
    font-weight: 500;
    padding: 12px 20px;
    font-size: 14px;
    border-radius: 4px;
}

.btn-primary {
    color: #fff;
    background-color: #409EFF;
    border-color: #409EFF;
}

.btn-success {
    color: #fff;
    background-color: #67C23A;
    border-color: #67C23A;
}

.btn-danger {
    color: #fff;
    background-color: #F56C6C;
    border-color: #F56C6C;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ebeef5;
}

.table th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 500;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .card {
        padding: 15px;
    }
    
    .btn {
        padding: 10px 15px;
    }
}

/* 加载指示器 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
}

.loading-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border-left-color: #09f;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
