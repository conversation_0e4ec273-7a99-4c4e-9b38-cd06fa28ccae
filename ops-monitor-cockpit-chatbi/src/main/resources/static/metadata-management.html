<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>元数据管理 - ChatBI</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="css/common.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            margin-bottom: 30px;
            text-align: center;
        }
        .nav-links {
            margin-top: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 5px 10px;
            text-decoration: none;
            color: #6c757d;
            border-radius: 5px;
        }
        .nav-links a:hover {
            background-color: #e9ecef;
        }
        .nav-links a.active {
            color: #0d6efd;
            font-weight: bold;
            border-bottom: 2px solid #0d6efd;
        }
        .metadata-management {
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .page-header {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .table-operations {
            margin-bottom: 20px;
        }
        .el-table .success-row {
            background: #f0f9eb;
        }
        .el-table .disabled-row {
            background: #f9f9f9;
            color: #999;
        }
        .form-footer {
            margin-top: 20px;
            text-align: right;
        }
        .metadata-tabs {
            margin-top: 20px;
        }
        .search-bar {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>ChatBI - 数据查询平台</h1>
                <div class="nav-links">
                    <a href="index.html">首页</a>
                    <a href="metadata-management.html" class="active">元数据管理</a>
                    <a href="datasource-management.html">数据源管理</a>
                </div>
            </div>

            <div class="metadata-management">
            <div class="page-header">
                <h2>元数据管理</h2>
                <el-button type="primary" @click="refreshCache">刷新缓存</el-button>
            </div>

            <el-tabs v-model="activeTab" class="metadata-tabs">
                <el-tab-pane label="元数据表" name="tables">
                    <div class="search-bar">
                        <el-form :inline="true" :model="tableSearchForm">
                            <el-form-item label="数据库名称">
                                <el-input v-model="tableSearchForm.databaseName" placeholder="数据库名称"></el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="searchTables">查询</el-button>
                                <el-button @click="resetTableSearch">重置</el-button>
                                <el-button type="success" @click="showAddTableDialog">添加表</el-button>
                            </el-form-item>
                        </el-form>
                    </div>

                    <el-table :data="tables" style="width: 100%" border :row-class-name="tableRowClassName">
                        <el-table-column prop="id" label="ID" width="80"></el-table-column>
                        <el-table-column prop="databaseName" label="数据库名称" width="150"></el-table-column>
                        <el-table-column prop="tableName" label="表名" width="200"></el-table-column>
                        <el-table-column prop="description" label="描述"></el-table-column>
                        <el-table-column prop="enable" label="状态" width="100">
                            <template slot-scope="scope">
                                <el-tag :type="scope.row.enable === 1 ? 'success' : 'info'">
                                    {{ scope.row.enable === 1 ? '启用' : '禁用' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="sortOrder" label="排序" width="80"></el-table-column>
                        <el-table-column label="操作" width="250">
                            <template slot-scope="scope">
                                <el-button size="mini" @click="showTableDetail(scope.row)">详情</el-button>
                                <el-button size="mini" type="primary" @click="showEditTableDialog(scope.row)">编辑</el-button>
                                <el-button size="mini" type="danger" @click="confirmDeleteTable(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <div class="pagination-container" style="margin-top: 20px; text-align: right;">
                        <el-pagination
                            @size-change="handleTableSizeChange"
                            @current-change="handleTableCurrentChange"
                            :current-page="tablePageNum"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="tablePageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="tableTotal">
                        </el-pagination>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="元数据关系" name="relationships">
                    <div class="table-operations">
                        <el-button type="success" @click="showAddRelationshipDialog">添加关系</el-button>
                    </div>

                    <el-table :data="relationships" style="width: 100%" border :row-class-name="tableRowClassName">
                        <el-table-column prop="id" label="ID" width="80"></el-table-column>
                        <el-table-column prop="sourceTable" label="源表" width="150"></el-table-column>
                        <el-table-column prop="sourceColumn" label="源列" width="150"></el-table-column>
                        <el-table-column prop="targetTable" label="目标表" width="150"></el-table-column>
                        <el-table-column prop="targetColumn" label="目标列" width="150"></el-table-column>
                        <el-table-column prop="relationshipType" label="关系类型" width="100"></el-table-column>
                        <el-table-column prop="enable" label="状态" width="100">
                            <template slot-scope="scope">
                                <el-tag :type="scope.row.enable === 1 ? 'success' : 'info'">
                                    {{ scope.row.enable === 1 ? '启用' : '禁用' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="200">
                            <template slot-scope="scope">
                                <el-button size="mini" type="primary" @click="showEditRelationshipDialog(scope.row)">编辑</el-button>
                                <el-button size="mini" type="danger" @click="confirmDeleteRelationship(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>

            <!-- 表详情对话框 -->
            <el-dialog title="表详情" :visible.sync="tableDetailDialogVisible" width="80%">
                <div v-if="currentTable">
                    <h3>{{ currentTable.databaseName }}.{{ currentTable.tableName }}</h3>
                    <p>{{ currentTable.description }}</p>

                    <div class="table-operations" style="margin-top: 20px;">
                        <el-button type="success" @click="showAddColumnDialog">添加列</el-button>
                    </div>

                    <el-table :data="columns" style="width: 100%" border :row-class-name="tableRowClassName">
                        <el-table-column prop="id" label="ID" width="80"></el-table-column>
                        <el-table-column prop="columnName" label="列名" width="150"></el-table-column>
                        <el-table-column prop="dataType" label="数据类型" width="120"></el-table-column>
                        <el-table-column prop="primaryKey" label="主键" width="80">
                            <template slot-scope="scope">
                                <el-tag :type="scope.row.primaryKey === 1 ? 'danger' : 'info'">
                                    {{ scope.row.primaryKey === 1 ? '是' : '否' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="chineseName" label="中文名" width="150"></el-table-column>
                        <el-table-column prop="description" label="描述"></el-table-column>
                        <el-table-column prop="enable" label="状态" width="80">
                            <template slot-scope="scope">
                                <el-tag :type="scope.row.enable === 1 ? 'success' : 'info'">
                                    {{ scope.row.enable === 1 ? '启用' : '禁用' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="sortOrder" label="排序" width="80"></el-table-column>
                        <el-table-column label="操作" width="200">
                            <template slot-scope="scope">
                                <el-button size="mini" type="primary" @click="showEditColumnDialog(scope.row)">编辑</el-button>
                                <el-button size="mini" type="danger" @click="confirmDeleteColumn(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-dialog>

            <!-- 添加/编辑表对话框 -->
            <el-dialog :title="tableFormTitle" :visible.sync="tableFormDialogVisible">
                <el-form :model="tableForm" :rules="tableFormRules" ref="tableForm" label-width="100px">
                    <el-form-item label="数据源" prop="dataSourceId">
                        <el-select v-model="tableForm.dataSourceId" placeholder="请选择数据源" style="width: 100%;">
                            <el-option
                                v-for="item in dataSources"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="数据库名称" prop="databaseName">
                        <el-input v-model="tableForm.databaseName"></el-input>
                    </el-form-item>
                    <el-form-item label="表名" prop="tableName">
                        <el-input v-model="tableForm.tableName"></el-input>
                    </el-form-item>
                    <el-form-item label="描述" prop="description">
                        <el-input v-model="tableForm.description" type="textarea" :rows="3"></el-input>
                    </el-form-item>
                    <el-form-item label="状态" prop="enable">
                        <el-switch v-model="tableForm.enableSwitch" active-text="启用" inactive-text="禁用"></el-switch>
                    </el-form-item>
                    <el-form-item label="排序" prop="sortOrder">
                        <el-input-number v-model="tableForm.sortOrder" :min="0" :max="1000"></el-input-number>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="tableFormDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitTableForm">确定</el-button>
                </div>
            </el-dialog>

            <!-- 添加/编辑列对话框 -->
            <el-dialog :title="columnFormTitle" :visible.sync="columnFormDialogVisible">
                <el-form :model="columnForm" :rules="columnFormRules" ref="columnForm" label-width="100px">
                    <el-form-item label="列名" prop="columnName">
                        <el-input v-model="columnForm.columnName"></el-input>
                    </el-form-item>
                    <el-form-item label="数据类型" prop="dataType">
                        <el-select v-model="columnForm.dataType" placeholder="请选择数据类型" style="width: 100%;">
                            <el-option label="VARCHAR(50)" value="VARCHAR(50)"></el-option>
                            <el-option label="VARCHAR(100)" value="VARCHAR(100)"></el-option>
                            <el-option label="VARCHAR(200)" value="VARCHAR(200)"></el-option>
                            <el-option label="TEXT" value="TEXT"></el-option>
                            <el-option label="INT" value="INT"></el-option>
                            <el-option label="BIGINT" value="BIGINT"></el-option>
                            <el-option label="DECIMAL(10,2)" value="DECIMAL(10,2)"></el-option>
                            <el-option label="DATETIME" value="DATETIME"></el-option>
                            <el-option label="DATE" value="DATE"></el-option>
                            <el-option label="TIMESTAMP" value="TIMESTAMP"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="主键" prop="primaryKey">
                        <el-switch v-model="columnForm.primaryKeySwitch" active-text="是" inactive-text="否"></el-switch>
                    </el-form-item>
                    <el-form-item label="描述" prop="description">
                        <el-input v-model="columnForm.description" type="textarea" :rows="3"></el-input>
                    </el-form-item>
                    <el-form-item label="中文名" prop="chineseName">
                        <el-input v-model="columnForm.chineseName" placeholder="如果不填写，将使用描述作为中文名"></el-input>
                    </el-form-item>
                    <el-form-item label="状态" prop="enable">
                        <el-switch v-model="columnForm.enableSwitch" active-text="启用" inactive-text="禁用"></el-switch>
                    </el-form-item>
                    <el-form-item label="排序" prop="sortOrder">
                        <el-input-number v-model="columnForm.sortOrder" :min="0" :max="1000"></el-input-number>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="columnFormDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitColumnForm">确定</el-button>
                </div>
            </el-dialog>

            <!-- 添加/编辑关系对话框 -->
            <el-dialog :title="relationshipFormTitle" :visible.sync="relationshipFormDialogVisible">
                <el-form :model="relationshipForm" :rules="relationshipFormRules" ref="relationshipForm" label-width="100px">
                    <el-form-item label="源表" prop="sourceTable">
                        <el-input v-model="relationshipForm.sourceTable"></el-input>
                    </el-form-item>
                    <el-form-item label="源列" prop="sourceColumn">
                        <el-input v-model="relationshipForm.sourceColumn"></el-input>
                    </el-form-item>
                    <el-form-item label="目标表" prop="targetTable">
                        <el-input v-model="relationshipForm.targetTable"></el-input>
                    </el-form-item>
                    <el-form-item label="目标列" prop="targetColumn">
                        <el-input v-model="relationshipForm.targetColumn"></el-input>
                    </el-form-item>
                    <el-form-item label="关系类型" prop="relationshipType">
                        <el-select v-model="relationshipForm.relationshipType" placeholder="请选择关系类型" style="width: 100%;">
                            <el-option label="一对一" value="一对一"></el-option>
                            <el-option label="一对多" value="一对多"></el-option>
                            <el-option label="多对一" value="多对一"></el-option>
                            <el-option label="多对多" value="多对多"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态" prop="enable">
                        <el-switch v-model="relationshipForm.enableSwitch" active-text="启用" inactive-text="禁用"></el-switch>
                    </el-form-item>
                    <el-form-item label="排序" prop="sortOrder">
                        <el-input-number v-model="relationshipForm.sortOrder" :min="0" :max="1000"></el-input-number>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="relationshipFormDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitRelationshipForm">确定</el-button>
                </div>
            </el-dialog>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="js/api-utils.js"></script>
    <script src="js/metadata-management.js"></script>
</body>
</html>
