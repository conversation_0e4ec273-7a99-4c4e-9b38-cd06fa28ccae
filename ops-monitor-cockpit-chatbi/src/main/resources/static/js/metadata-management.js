/**
 * 元数据管理页面脚本
 */
new Vue({
    el: '#app',
    data() {
        return {
            // 当前激活的标签页
            activeTab: 'tables',

            // 表格数据
            tables: [],
            relationships: [],
            columns: [],
            dataSources: [],

            // 分页参数
            tablePageNum: 1,
            tablePageSize: 10,
            tableTotal: 0,

            // 搜索表单
            tableSearchForm: {
                databaseName: ''
            },

            // 当前选中的表
            currentTable: null,

            // 对话框显示控制
            tableDetailDialogVisible: false,
            tableFormDialogVisible: false,
            columnFormDialogVisible: false,
            relationshipFormDialogVisible: false,

            // 表单标题
            tableFormTitle: '添加元数据表',
            columnFormTitle: '添加元数据列',
            relationshipFormTitle: '添加元数据关系',

            // 表单数据
            tableForm: {
                dataSourceId: null,
                databaseName: '',
                tableName: '',
                description: '',
                enableSwitch: true,
                sortOrder: 0
            },

            columnForm: {
                tableId: null,
                columnName: '',
                dataType: 'VARCHAR(100)',
                primaryKeySwitch: false,
                description: '',
                chineseName: '',
                enableSwitch: true,
                sortOrder: 0
            },

            relationshipForm: {
                dataSourceId: null,
                sourceTable: '',
                sourceColumn: '',
                targetTable: '',
                targetColumn: '',
                relationshipType: '一对多',
                enableSwitch: true,
                sortOrder: 0
            },

            // 表单验证规则
            tableFormRules: {
                dataSourceId: [
                    { required: true, message: '请选择数据源', trigger: 'change' }
                ],
                databaseName: [
                    { required: true, message: '请输入数据库名称', trigger: 'blur' },
                    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
                ],
                tableName: [
                    { required: true, message: '请输入表名', trigger: 'blur' },
                    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
                ],
                description: [
                    { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
                ]
            },

            columnFormRules: {
                columnName: [
                    { required: true, message: '请输入列名', trigger: 'blur' },
                    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
                ],
                dataType: [
                    { required: true, message: '请选择数据类型', trigger: 'change' }
                ],
                description: [
                    { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
                ],
                chineseName: [
                    { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
                ]
            },

            relationshipFormRules: {
                sourceTable: [
                    { required: true, message: '请输入源表', trigger: 'blur' },
                    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
                ],
                sourceColumn: [
                    { required: true, message: '请输入源列', trigger: 'blur' },
                    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
                ],
                targetTable: [
                    { required: true, message: '请输入目标表', trigger: 'blur' },
                    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
                ],
                targetColumn: [
                    { required: true, message: '请输入目标列', trigger: 'blur' },
                    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
                ],
                relationshipType: [
                    { required: true, message: '请选择关系类型', trigger: 'change' }
                ]
            }
        };
    },

    created() {
        this.fetchDataSources();
        this.fetchTables();
        this.fetchRelationships();
    },

    methods: {
        /**
         * 获取数据源列表
         */
        fetchDataSources() {
            axios.get('/api/datasource-config/enabled')
                .then(response => {
                    console.log('Data sources response:', response.data);

                    // 直接处理响应数据
                    let dataSourceList;

                    // 判断响应格式
                    if (response.data && response.data.meta && response.data.data && response.data.data.data && Array.isArray(response.data.data.data)) {
                        // 嵌套的响应格式
                        console.log('Using nested response format');
                        dataSourceList = response.data.data.data;
                    } else if (response.data && response.data.meta && response.data.data && Array.isArray(response.data.data)) {
                        // 统一响应格式
                        console.log('Using unified response format with meta');
                        dataSourceList = response.data.data;
                    } else if (response.data && response.data.code !== undefined && response.data.data && Array.isArray(response.data.data)) {
                        // 另一种统一响应格式
                        console.log('Using unified response format with code');
                        dataSourceList = response.data.data;
                    } else if (response.data && Array.isArray(response.data)) {
                        // 直接返回数组
                        console.log('Using direct array response format');
                        dataSourceList = response.data;
                    } else {
                        console.error('Unknown response format:', response.data);
                        this.$message.error('获取数据源列表失败: 响应格式错误');
                        return;
                    }

                    console.log('Processed data sources:', dataSourceList);

                    // 确保数据是数组
                    if (!Array.isArray(dataSourceList)) {
                        console.error('Data sources is not an array:', dataSourceList);
                        this.$message.error('获取数据源列表失败: 数据格式错误');
                        return;
                    }

                    this.dataSources = dataSourceList;
                    if (this.dataSources.length > 0) {
                        // 默认选择第一个数据源
                        const defaultSource = this.dataSources.find(ds => ds.isDefault) || this.dataSources[0];
                        this.tableForm.dataSourceId = defaultSource.id;
                        this.relationshipForm.dataSourceId = defaultSource.id;
                    }
                })
                .catch(error => {
                    console.error('获取数据源列表失败:', error);
                    this.$message.error('获取数据源列表失败');
                });
        },

        /**
         * 获取元数据表列表
         */
        fetchTables() {
            const params = {
                pageNum: this.tablePageNum,
                pageSize: this.tablePageSize
            };

            if (this.tableSearchForm.databaseName) {
                params.databaseName = this.tableSearchForm.databaseName;
            }

            // 如果选择了数据源，添加数据源ID参数
            if (this.tableForm.dataSourceId) {
                params.dataSourceId = this.tableForm.dataSourceId;
            }

            axios.get('/api/metadata-management/tables', { params })
                .then(response => {
                    if (response.data.meta && response.data.meta.code === '20000') {
                        this.tables = response.data.data.data;
                        this.tableTotal = response.data.data.total;
                    } else {
                        this.$message.error((response.data.meta && response.data.meta.message) || '获取元数据表列表失败');
                    }
                })
                .catch(error => {
                    console.error('获取元数据表列表失败:', error);
                    this.$message.error('获取元数据表列表失败: ' + error.message);
                });
        },

        /**
         * 获取元数据关系列表
         */
        fetchRelationships() {
            axios.get('/api/metadata-management/relationships')
                .then(response => {
                    if (response.data.meta && response.data.meta.code === '20000') {
                        this.relationships = response.data.data;
                    } else {
                        this.$message.error((response.data.meta && response.data.meta.message) || '获取元数据关系列表失败');
                    }
                })
                .catch(error => {
                    console.error('获取元数据关系列表失败:', error);
                    this.$message.error('获取元数据关系列表失败: ' + error.message);
                });
        },

        /**
         * 获取元数据列列表
         * @param {number} tableId 表ID
         */
        fetchColumns(tableId) {
            axios.get('/api/metadata-management/columns', { params: { tableId } })
                .then(response => {
                    if (response.data.meta && response.data.meta.code === '20000') {
                        this.columns = response.data.data.data;
                    } else {
                        this.$message.error((response.data.meta && response.data.meta.message) || '获取元数据列列表失败');
                    }
                })
                .catch(error => {
                    console.error('获取元数据列列表失败:', error);
                    this.$message.error('获取元数据列列表失败: ' + error.message);
                });
        },

        /**
         * 表格行样式
         * @param {Object} row 行数据
         * @returns {string} 样式类名
         */
        tableRowClassName({ row }) {
            if (row.enable === 1) {
                return '';
            } else {
                return 'disabled-row';
            }
        },

        /**
         * 搜索表
         */
        searchTables() {
            this.tablePageNum = 1;
            this.fetchTables();
        },

        /**
         * 重置表搜索
         */
        resetTableSearch() {
            this.tableSearchForm.databaseName = '';
            this.tablePageNum = 1;
            this.fetchTables();
        },

        /**
         * 处理表格每页显示数量变化
         * @param {number} size 每页显示数量
         */
        handleTableSizeChange(size) {
            this.tablePageSize = size;
            this.fetchTables();
        },

        /**
         * 处理表格当前页变化
         * @param {number} page 当前页
         */
        handleTableCurrentChange(page) {
            this.tablePageNum = page;
            this.fetchTables();
        },

        /**
         * 显示表详情
         * @param {Object} row 行数据
         */
        showTableDetail(row) {
            this.currentTable = row;
            this.fetchColumns(row.id);
            this.tableDetailDialogVisible = true;
        },

        /**
         * 显示添加表对话框
         */
        showAddTableDialog() {
            this.tableFormTitle = '添加元数据表';
            this.tableForm = {
                dataSourceId: this.dataSources.length > 0 ?
                    (this.dataSources.find(ds => ds.isDefault) || this.dataSources[0]).id : null,
                databaseName: '',
                tableName: '',
                description: '',
                enableSwitch: true,
                sortOrder: 0
            };
            this.tableFormDialogVisible = true;
        },

        /**
         * 显示编辑表对话框
         * @param {Object} row 行数据
         */
        showEditTableDialog(row) {
            this.tableFormTitle = '编辑元数据表';
            this.tableForm = {
                id: row.id,
                dataSourceId: row.dataSourceId,
                databaseName: row.databaseName,
                tableName: row.tableName,
                description: row.description,
                enableSwitch: row.enable === 1,
                sortOrder: row.sortOrder || 0
            };
            this.tableFormDialogVisible = true;
        },

        /**
         * 提交表单
         */
        submitTableForm() {
            this.$refs.tableForm.validate(valid => {
                if (valid) {
                    const formData = {
                        dataSourceId: this.tableForm.dataSourceId,
                        databaseName: this.tableForm.databaseName,
                        tableName: this.tableForm.tableName,
                        description: this.tableForm.description,
                        enable: this.tableForm.enableSwitch ? 1 : 0,
                        sortOrder: this.tableForm.sortOrder
                    };

                    if (this.tableForm.id) {
                        // 更新表
                        axios.put(`/api/metadata-management/tables/${this.tableForm.id}`, formData)
                            .then(response => {
                                if (response.data.meta && response.data.meta.code === '20000') {
                                    this.$message.success('元数据表更新成功');
                                    this.tableFormDialogVisible = false;
                                    this.fetchTables();
                                } else {
                                    this.$message.error((response.data.meta && response.data.meta.message) || '元数据表更新失败');
                                }
                            })
                            .catch(error => {
                                console.error('元数据表更新失败:', error);
                                this.$message.error('元数据表更新失败: ' + error.message);
                            });
                    } else {
                        // 创建表
                        axios.post('/api/metadata-management/tables', formData)
                            .then(response => {
                                if (response.data.meta && response.data.meta.code === '20000') {
                                    this.$message.success('元数据表创建成功');
                                    this.tableFormDialogVisible = false;
                                    this.fetchTables();
                                } else {
                                    this.$message.error((response.data.meta && response.data.meta.message) || '元数据表创建失败');
                                }
                            })
                            .catch(error => {
                                console.error('元数据表创建失败:', error);
                                this.$message.error('元数据表创建失败: ' + error.message);
                            });
                    }
                }
            });
        },

        /**
         * 确认删除表
         * @param {Object} row 行数据
         */
        confirmDeleteTable(row) {
            this.$confirm(`确定要删除元数据表 "${row.databaseName}.${row.tableName}" 吗？删除后将无法恢复，且会级联删除所有相关的列。`, '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/api/metadata-management/tables/${row.id}`)
                    .then(response => {
                        if (response.data.meta && response.data.meta.code === '20000') {
                            this.$message.success('元数据表删除成功');
                            this.fetchTables();
                        } else {
                            this.$message.error((response.data.meta && response.data.meta.message) || '元数据表删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('元数据表删除失败:', error);
                        this.$message.error('元数据表删除失败: ' + error.message);
                    });
            }).catch(() => {
                // 取消删除
            });
        },

        /**
         * 显示添加列对话框
         */
        showAddColumnDialog() {
            if (!this.currentTable) {
                this.$message.error('请先选择一个表');
                return;
            }

            this.columnFormTitle = '添加元数据列';
            this.columnForm = {
                tableId: this.currentTable.id,
                columnName: '',
                dataType: 'VARCHAR(100)',
                primaryKeySwitch: false,
                description: '',
                chineseName: '',
                enableSwitch: true,
                sortOrder: 0
            };
            this.columnFormDialogVisible = true;
        },

        /**
         * 显示编辑列对话框
         * @param {Object} row 行数据
         */
        showEditColumnDialog(row) {
            this.columnFormTitle = '编辑元数据列';
            this.columnForm = {
                id: row.id,
                tableId: row.tableId,
                columnName: row.columnName,
                dataType: row.dataType,
                primaryKeySwitch: row.primaryKey === 1,
                description: row.description,
                chineseName: row.chineseName,
                enableSwitch: row.enable === 1,
                sortOrder: row.sortOrder || 0
            };
            this.columnFormDialogVisible = true;
        },

        /**
         * 提交列表单
         */
        submitColumnForm() {
            this.$refs.columnForm.validate(valid => {
                if (valid) {
                    const formData = {
                        tableId: this.columnForm.tableId,
                        columnName: this.columnForm.columnName,
                        dataType: this.columnForm.dataType,
                        primaryKey: this.columnForm.primaryKeySwitch ? 1 : 0,
                        description: this.columnForm.description,
                        chineseName: this.columnForm.chineseName,
                        enable: this.columnForm.enableSwitch ? 1 : 0,
                        sortOrder: this.columnForm.sortOrder
                    };

                    if (this.columnForm.id) {
                        // 更新列
                        axios.put(`/api/metadata-management/columns/${this.columnForm.id}`, formData)
                            .then(response => {
                                if (response.data.meta && response.data.meta.code === '20000') {
                                    this.$message.success('元数据列更新成功');
                                    this.columnFormDialogVisible = false;
                                    this.fetchColumns(this.currentTable.id);
                                } else {
                                    this.$message.error((response.data.meta && response.data.meta.message) || '元数据列更新失败');
                                }
                            })
                            .catch(error => {
                                console.error('元数据列更新失败:', error);
                                this.$message.error('元数据列更新失败: ' + error.message);
                            });
                    } else {
                        // 创建列
                        axios.post('/api/metadata-management/columns', formData)
                            .then(response => {
                                if (response.data.meta && response.data.meta.code === '20000') {
                                    this.$message.success('元数据列创建成功');
                                    this.columnFormDialogVisible = false;
                                    this.fetchColumns(this.currentTable.id);
                                } else {
                                    this.$message.error((response.data.meta && response.data.meta.message) || '元数据列创建失败');
                                }
                            })
                            .catch(error => {
                                console.error('元数据列创建失败:', error);
                                this.$message.error('元数据列创建失败: ' + error.message);
                            });
                    }
                }
            });
        },

        /**
         * 确认删除列
         * @param {Object} row 行数据
         */
        confirmDeleteColumn(row) {
            this.$confirm(`确定要删除元数据列 "${row.columnName}" 吗？删除后将无法恢复。`, '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/api/metadata-management/columns/${row.id}`)
                    .then(response => {
                        if (response.data.meta && response.data.meta.code === '20000') {
                            this.$message.success('元数据列删除成功');
                            this.fetchColumns(this.currentTable.id);
                        } else {
                            this.$message.error((response.data.meta && response.data.meta.message) || '元数据列删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('元数据列删除失败:', error);
                        this.$message.error('元数据列删除失败: ' + error.message);
                    });
            }).catch(() => {
                // 取消删除
            });
        },

        /**
         * 显示添加关系对话框
         */
        showAddRelationshipDialog() {
            this.relationshipFormTitle = '添加元数据关系';
            this.relationshipForm = {
                sourceTable: '',
                sourceColumn: '',
                targetTable: '',
                targetColumn: '',
                relationshipType: '一对多',
                enableSwitch: true,
                sortOrder: 0
            };
            this.relationshipFormDialogVisible = true;
        },

        /**
         * 显示编辑关系对话框
         * @param {Object} row 行数据
         */
        showEditRelationshipDialog(row) {
            this.relationshipFormTitle = '编辑元数据关系';
            this.relationshipForm = {
                id: row.id,
                sourceTable: row.sourceTable,
                sourceColumn: row.sourceColumn,
                targetTable: row.targetTable,
                targetColumn: row.targetColumn,
                relationshipType: row.relationshipType,
                enableSwitch: row.enable === 1,
                sortOrder: row.sortOrder || 0
            };
            this.relationshipFormDialogVisible = true;
        },

        /**
         * 提交关系表单
         */
        submitRelationshipForm() {
            this.$refs.relationshipForm.validate(valid => {
                if (valid) {
                    const formData = {
                        sourceTable: this.relationshipForm.sourceTable,
                        sourceColumn: this.relationshipForm.sourceColumn,
                        targetTable: this.relationshipForm.targetTable,
                        targetColumn: this.relationshipForm.targetColumn,
                        relationshipType: this.relationshipForm.relationshipType,
                        enable: this.relationshipForm.enableSwitch ? 1 : 0,
                        sortOrder: this.relationshipForm.sortOrder
                    };

                    if (this.relationshipForm.id) {
                        // 更新关系
                        axios.put(`/api/metadata-management/relationships/${this.relationshipForm.id}`, formData)
                            .then(response => {
                                if (response.data.meta && response.data.meta.code === '20000') {
                                    this.$message.success('元数据关系更新成功');
                                    this.relationshipFormDialogVisible = false;
                                    this.fetchRelationships();
                                } else {
                                    this.$message.error((response.data.meta && response.data.meta.message) || '元数据关系更新失败');
                                }
                            })
                            .catch(error => {
                                console.error('元数据关系更新失败:', error);
                                this.$message.error('元数据关系更新失败: ' + error.message);
                            });
                    } else {
                        // 创建关系
                        axios.post('/api/metadata-management/relationships', formData)
                            .then(response => {
                                if (response.data.meta && response.data.meta.code === '20000') {
                                    this.$message.success('元数据关系创建成功');
                                    this.relationshipFormDialogVisible = false;
                                    this.fetchRelationships();
                                } else {
                                    this.$message.error((response.data.meta && response.data.meta.message) || '元数据关系创建失败');
                                }
                            })
                            .catch(error => {
                                console.error('元数据关系创建失败:', error);
                                this.$message.error('元数据关系创建失败: ' + error.message);
                            });
                    }
                }
            });
        },

        /**
         * 确认删除关系
         * @param {Object} row 行数据
         */
        confirmDeleteRelationship(row) {
            this.$confirm(`确定要删除元数据关系 "${row.sourceTable}.${row.sourceColumn} -> ${row.targetTable}.${row.targetColumn}" 吗？删除后将无法恢复。`, '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/api/metadata-management/relationships/${row.id}`)
                    .then(response => {
                        if (response.data.meta && response.data.meta.code === '20000') {
                            this.$message.success('元数据关系删除成功');
                            this.fetchRelationships();
                        } else {
                            this.$message.error((response.data.meta && response.data.meta.message) || '元数据关系删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('元数据关系删除失败:', error);
                        this.$message.error('元数据关系删除失败: ' + error.message);
                    });
            }).catch(() => {
                // 取消删除
            });
        },

        /**
         * 刷新元数据缓存
         */
        refreshCache() {
            axios.post('/api/metadata-management/refresh-cache')
                .then(response => {
                    if (response.data.meta && response.data.meta.code === '20000') {
                        this.$message.success('元数据缓存刷新成功');
                    } else {
                        this.$message.error((response.data.meta && response.data.meta.message) || '元数据缓存刷新失败');
                    }
                })
                .catch(error => {
                    console.error('元数据缓存刷新失败:', error);
                    this.$message.error('元数据缓存刷新失败: ' + error.message);
                });
        }
    }
});
