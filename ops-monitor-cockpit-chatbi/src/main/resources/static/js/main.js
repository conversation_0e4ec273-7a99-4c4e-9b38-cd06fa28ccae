// 全局变量
let currentResponse = null;
let chart = null;
let dataSources = [];
let currentDataSource = null;

// 当前SQL查询
let currentSql = '';

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 加载数据源列表
    loadDataSources();

    // 初始化对话模式
    initConversationMode();

    // 初始化表单提交事件
    document.getElementById('queryForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const query = document.getElementById('nlQuery').value;
        if (!query) return;

        // 根据对话模式选择不同的查询方式
        if (contextualModeEnabled) {
            executeContextualQuery(query);
        } else {
            executeQuery(query);
        }
    });

    // 初始化数据源选择事件
    document.getElementById('dataSourceSelect').addEventListener('change', function() {
        currentDataSource = this.value;
        console.log('切换数据源为: ' + currentDataSource);
    });

    // 初始化反馈按钮
    document.querySelectorAll('.feedback-btn').forEach(button => {
        button.addEventListener('click', function() {
            const feedbackValue = this.getAttribute('data-value');
            document.getElementById('feedbackForm').style.display = 'block';

            // 高亮选中的按钮
            document.querySelectorAll('.feedback-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');
        });
    });

    // 初始化提交反馈按钮
    document.getElementById('submitFeedback').addEventListener('click', function() {
        const feedbackText = document.getElementById('feedbackText').value;
        const selectedFeedback = document.querySelector('.feedback-btn.active');

        if (selectedFeedback && currentResponse) {
            const feedbackValue = selectedFeedback.getAttribute('data-value');
            submitFeedback(currentResponse.id, feedbackValue, feedbackText);
        }
    });

    // 初始化清除缓存按钮
    document.getElementById('clearCacheBtn').addEventListener('click', function() {
        clearQueryCache();
    });


});

/**
 * 加载数据源列表
 */
function loadDataSources() {
    fetch('/api/datasource-config/enabled')
        .then(response => {
            console.log('Raw response:', response);
            return response.json();
        })
        .then(response => {
            console.log('Data sources response:', response);

            // 直接处理响应数据
            let dataSourceList;

            // 判断响应格式
            if (response && response.meta && response.data && response.data.data && Array.isArray(response.data.data)) {
                // 嵌套的响应格式
                console.log('Using nested response format');
                dataSourceList = response.data.data;
            } else if (response && response.meta && response.data && Array.isArray(response.data)) {
                // 统一响应格式
                console.log('Using unified response format with meta');
                dataSourceList = response.data;
            } else if (response && response.code !== undefined && response.data && Array.isArray(response.data)) {
                // 另一种统一响应格式
                console.log('Using unified response format with code');
                dataSourceList = response.data;
            } else if (response && Array.isArray(response)) {
                // 直接返回数组
                console.log('Using direct array response format');
                dataSourceList = response;
            } else {
                console.error('Unknown response format:', response);
                return;
            }

            console.log('Processed data sources:', dataSourceList);

            // 确保数据是数组
            if (!Array.isArray(dataSourceList)) {
                console.error('Data sources is not an array:', dataSourceList);
                return;
            }

            dataSources = dataSourceList;
            const select = document.getElementById('dataSourceSelect');

            // 清空现有选项
            select.innerHTML = '';

            // 添加数据源选项
            dataSources.forEach(ds => {
                console.log('Processing data source:', ds);
                const option = document.createElement('option');
                option.value = ds.id;
                option.textContent = ds.name;
                if (ds.isDefault) {
                    option.selected = true;
                    currentDataSource = ds.id;
                }
                select.appendChild(option);
            });

            // 如果没有默认选中的数据源，选中第一个
            if (!currentDataSource && dataSources.length > 0) {
                currentDataSource = dataSources[0].id;
                select.value = currentDataSource;
            }
        })
        .catch(error => {
            console.error('Error loading data sources:', error);
        });
}

/**
 * 执行自然语言查询
 * @param {string} query - 自然语言查询
 */
function executeQuery(query) {
    // 显示加载状态
    document.getElementById('loadingIndicator').style.display = 'flex';
    document.getElementById('resultSection').style.display = 'none';

    // 获取选中的数据源
    const dataSourceSelect = document.getElementById('dataSourceSelect');
    const dataSourceId = parseInt(dataSourceSelect.value);

    // 构建请求
    const request = {
        query: query,
        needExplanation: false, // 始终不请求SQL解释
        saveHistory: true,
        dataSourceId: dataSourceId,
        userId: userId // 添加用户ID
    };

    // 发送请求
    fetch('/api/nl-query', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    })
    .then(response => response.json())
    .then(response => {
        console.log('NL query response:', response);
        // 隐藏加载状态
        document.getElementById('loadingIndicator').style.display = 'none';

        // 处理响应数据
        let data;

        // 判断响应格式
        if (response && response.meta && response.data) {
            // 统一响应格式
            console.log('Using unified response format for NL query');
            data = response.data;
        } else {
            // 直接使用响应
            console.log('Using direct response format for NL query');
            data = response;
        }

        console.log('Processed NL query data:', data);

        // 检查是否有错误
        if (data && data.success === false) {
            document.getElementById('resultSection').style.display = 'block';
            document.getElementById('resultTable').innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> ${data.message || data.errorMessage || '查询处理失败'}
                </div>
            `;
            return;
        }

        // 保存响应
        currentResponse = data;

        // 不再显示单独的结果区域，因为结果已经在对话框中显示
        // displayResults(data);
        document.getElementById('resultSection').style.display = 'none';

        // 将查询和响应添加到对话显示
        addToConversationDisplay(request.query, data);



        // 显示数据源信息
        if (data.dataSource) {
            console.log('查询使用数据源: ' + data.dataSource);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        alert('查询处理失败，请重试');
    });
}

/**
 * 显示查询结果
 * @param {Object} data - 查询响应数据
 */
function displayResults(data) {
    // 保存当前响应
    currentResponse = data;

    // 显示结果区域
    document.getElementById('resultSection').style.display = 'block';

    // 显示查询文本（如果有queryText元素）
    if (data.query && document.getElementById('queryText')) {
        document.getElementById('queryText').textContent = data.query;
    }

    // 更新上下文状态
    if (data.hasContext !== undefined) {
        hasActiveContext = data.hasContext;
        updateContextualModeUI();
    }

    if (data.success === false) {
        // 显示错误信息
        document.getElementById('resultTable').innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> ${data.errorMessage || data.message || '查询处理失败'}
            </div>
        `;
        return;
    }

    // 显示SQL查询
    const sqlDisplay = document.getElementById('sqlDisplay');
    sqlDisplay.textContent = data.generatedSql;

    // 设置SQL编辑器内容
    document.getElementById('sqlEditor').value = data.generatedSql;

    // 保存当前SQL供同比环比分析使用
    currentSql = data.generatedSql;

    // SQL解释功能已移除

    // 生成结果表格
    generateResultTable(data.results, data.columns);

    // 设置默认图表类型
    if (data.suggestedVisualization) {
        const chartTypeSelect = document.getElementById('chartType');
        if (chartTypeSelect.querySelector(`option[value="${data.suggestedVisualization}"]`)) {
            chartTypeSelect.value = data.suggestedVisualization;
        }
    }

    // 生成图表
    generateChart(data.results, data.columns);

    // 应用代码高亮
    setTimeout(() => {
        document.querySelectorAll('pre code').forEach((block) => {
            hljs.highlightElement(block);
        });
    }, 100);
}

/**
 * 生成结果表格
 * @param {Array} results - 查询结果
 * @param {Array} columns - 列信息
 */
function generateResultTable(results, columns) {
    // 添加数据源信息
    let dataSourceInfo = '';
    if (currentResponse && currentResponse.dataSource) {
        dataSourceInfo = `
            <div class="alert alert-info mb-3">
                <i class="bi bi-database"></i> 数据源: <strong>${currentResponse.dataSource}</strong>
            </div>
        `;
    }

    if (!results || results.length === 0) {
        document.getElementById('resultTable').innerHTML = dataSourceInfo + `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> 查询未返回任何结果
            </div>
        `;
        return;
    }

    let tableHtml = '<thead><tr>';

    // 生成表头
    if (columns && columns.length > 0) {
        columns.forEach(column => {
            tableHtml += `<th>${column.label || column.name}</th>`;
        });
    } else {
        // 如果没有提供列信息，使用第一行的键作为表头
        Object.keys(results[0]).forEach(key => {
            tableHtml += `<th>${formatColumnName(key)}</th>`;
        });
    }

    tableHtml += '</tr></thead><tbody>';

    // 生成表格内容
    results.forEach(row => {
        tableHtml += '<tr>';

        if (columns && columns.length > 0) {
            columns.forEach(column => {
                tableHtml += `<td>${formatCellValue(row[column.name])}</td>`;
            });
        } else {
            Object.values(row).forEach(value => {
                tableHtml += `<td>${formatCellValue(value)}</td>`;
            });
        }

        tableHtml += '</tr>';
    });

    tableHtml += '</tbody>';
    document.getElementById('resultTable').innerHTML = dataSourceInfo + tableHtml;
}

/**
 * 格式化列名
 * @param {string} columnName - 列名
 * @returns {string} 格式化后的列名
 */
function formatColumnName(columnName) {
    // 将下划线转换为空格，并将每个单词首字母大写
    return columnName.split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}

/**
 * 格式化单元格值
 * @param {*} value - 单元格值
 * @returns {string} 格式化后的单元格值
 */
function formatCellValue(value) {
    if (value === null || value === undefined) {
        return '-';
    }

    if (typeof value === 'number') {
        // 格式化数字
        if (Number.isInteger(value)) {
            return value.toString();
        } else {
            return value.toFixed(2);
        }
    }

    if (typeof value === 'boolean') {
        return value ? '是' : '否';
    }

    return value.toString();
}

/**
 * 生成图表
 * @param {Array} results - 查询结果
 * @param {Array} columns - 列信息
 */
function generateChart(results, columns) {
    if (!results || results.length === 0) {
        return;
    }

    const chartType = document.getElementById('chartType').value;
    const ctx = document.getElementById('resultChart').getContext('2d');

    // 销毁现有图表
    if (chart) {
        chart.destroy();
    }

    // 准备图表数据
    const chartData = prepareChartData(results, columns, chartType);

    // 创建图表
    chart = new Chart(ctx, {
        type: chartType,
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: '查询结果可视化'
                }
            }
        }
    });
}

/**
 * 准备图表数据
 * @param {Array} results - 查询结果
 * @param {Array} columns - 列信息
 * @param {string} chartType - 图表类型
 * @returns {Object} 图表数据
 */
function prepareChartData(results, columns, chartType) {
    // 确定标签列和数据列
    let labelColumn = null;
    let dataColumns = [];

    if (columns && columns.length > 0) {
        // 尝试找到合适的标签列和数据列
        columns.forEach(column => {
            const hasNameKeyword = column.name && (column.name.includes('name') || column.name.includes('category'));
            const isStringType = column.type === 'String';
            if (isStringType || hasNameKeyword) {
                if (!labelColumn) {
                    labelColumn = column.name;
                }
            } else if (column.type === 'Integer' || column.type === 'Double' || column.type === 'Float' ||
                      column.type === 'Long' || column.type === 'BigDecimal') {
                if (column.name) {
                    dataColumns.push(column.name);
                }
            }
        });

        // 如果没有找到合适的标签列，使用第一列
        if (!labelColumn) {
            labelColumn = columns[0].name;
        }

        // 如果没有找到合适的数据列，使用除标签列外的所有列
        if (dataColumns.length === 0) {
            dataColumns = columns.filter(column => column.name !== labelColumn)
                                .map(column => column.name);
        }
    } else {
        // 如果没有提供列信息，使用第一个键作为标签列，其余作为数据列
        const keys = Object.keys(results[0]);
        labelColumn = keys[0];
        dataColumns = keys.slice(1);
    }

    // 提取标签和数据
    const labels = results.map(row => row[labelColumn]);

    // 为每个数据列创建一个数据集
    const datasets = dataColumns.map((column, index) => {
        const data = results.map(row => row[column]);

        // 生成随机颜色
        const color = getRandomColor(index);

        return {
            label: formatColumnName(column),
            data: data,
            backgroundColor: chartType === 'line' ? 'rgba(0, 0, 0, 0.1)' : getRandomColors(data.length, index),
            borderColor: color,
            borderWidth: 1
        };
    });

    return {
        labels: labels,
        datasets: datasets
    };
}

/**
 * 获取随机颜色
 * @param {number} index - 索引
 * @returns {string} 随机颜色
 */
function getRandomColor(index) {
    const colors = [
        'rgba(255, 99, 132, 1)',
        'rgba(54, 162, 235, 1)',
        'rgba(255, 206, 86, 1)',
        'rgba(75, 192, 192, 1)',
        'rgba(153, 102, 255, 1)',
        'rgba(255, 159, 64, 1)',
        'rgba(199, 199, 199, 1)',
        'rgba(83, 102, 255, 1)',
        'rgba(40, 159, 64, 1)',
        'rgba(210, 199, 199, 1)'
    ];

    return colors[index % colors.length];
}

/**
 * 获取随机颜色数组
 * @param {number} count - 颜色数量
 * @param {number} index - 索引
 * @returns {Array} 随机颜色数组
 */
function getRandomColors(count, index) {
    const baseColor = getRandomColor(index);
    const colors = [];

    for (let i = 0; i < count; i++) {
        colors.push(baseColor);
    }

    return colors;
}

/**
 * 更新图表
 */
function updateChart() {
    if (currentResponse && currentResponse.results) {
        generateChart(currentResponse.results, currentResponse.columns);
    }
}

/**
 * 复制SQL查询
 */
function copySql() {
    const sqlText = document.getElementById('sqlDisplay').textContent;
    navigator.clipboard.writeText(sqlText).then(() => {
        alert('SQL已复制到剪贴板');
    }).catch(err => {
        console.error('复制失败:', err);
    });
}

/**
 * 执行编辑后的SQL
 */
function executeEditedSql() {
    const sql = document.getElementById('sqlEditor').value;
    if (!sql) return;

    // 显示加载状态
    document.getElementById('loadingIndicator').style.display = 'flex';

    // 构建请求
    const request = {
        sql: sql
    };

    // 发送请求
    fetch('/api/nl-query/execute-sql', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    })
    .then(response => response.json())
    .then(response => {
        console.log('Execute SQL response:', response);
        // 隐藏加载状态
        document.getElementById('loadingIndicator').style.display = 'none';

        // 处理响应数据
        let data;

        // 判断响应格式
        if (response && response.meta && response.data) {
            // 统一响应格式
            console.log('Using unified response format for SQL execution');
            data = response.data;
        } else {
            // 直接使用响应
            console.log('Using direct response format for SQL execution');
            data = response;
        }

        console.log('Processed SQL execution data:', data);

        // 检查是否有错误
        if (data && data.success === false) {
            document.getElementById('resultSection').style.display = 'block';
            document.getElementById('resultTable').innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> ${data.message || data.errorMessage || 'SQL执行失败'}
                </div>
            `;
            return;
        }

        // 保存响应
        currentResponse = data;

        // 不再显示单独的结果区域，因为结果已经在对话框中显示
        // displayResults(data);
        document.getElementById('resultSection').style.display = 'none';

        // 将查询和响应添加到对话显示
        addToConversationDisplay(`执行 SQL: ${sql}`, data);
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        alert('SQL执行失败，请重试');
    });
}

/**
 * 提交反馈
 * @param {string} queryId - 查询ID
 * @param {string} feedbackValue - 反馈值
 * @param {string} feedbackText - 反馈文本
 */
function submitFeedback(queryId, feedbackValue, feedbackText) {
    // 构建请求
    const request = {
        queryId: queryId,
        feedbackValue: feedbackValue,
        feedbackText: feedbackText
    };

    // 发送请求
    fetch('/api/nl-query/feedback', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    })
    .then(response => response.json())
    .then(response => {
        // 处理统一响应格式
        handleApiResponse(response,
            data => {
                alert('感谢您的反馈！');
                document.getElementById('feedbackForm').style.display = 'none';
                document.querySelectorAll('.feedback-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.getElementById('feedbackText').value = '';
            },
            error => {
                alert('提交反馈失败：' + error);
            }
        );
    })
    .catch(error => {
        console.error('Error:', error);
        alert('提交反馈失败，请重试');
    });
}



/**
 * 清除查询缓存
 */
function clearQueryCache() {
    fetch('/api/nl-query/clear-cache', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(response => {
        // 处理统一响应格式
        handleApiResponse(response,
            data => {
                alert('查询缓存已清除');
            },
            error => {
                alert('清除缓存失败: ' + error);
            }
        );
    })
    .catch(error => {
        console.error('Error clearing cache:', error);
        alert('清除缓存时发生错误');
    });
}







/**
 * 设置示例查询
 * @param {string} query - 查询
 */
function setExampleQuery(query) {
    document.getElementById('nlQuery').value = query;

    // 自动聚焦到输入框
    document.getElementById('nlQuery').focus();

    // 自动执行查询
    if (contextualModeEnabled) {
        executeContextualQuery(query);
    } else {
        executeQuery(query);
    }
}
