/**
 * API工具函数
 * 用于处理统一的API响应格式和显示消息
 */

/**
 * 从统一响应格式中提取数据
 * @param {Object} response - API响应对象
 * @param {Function} onSuccess - 成功回调函数，参数为提取的数据
 * @param {Function} onError - 错误回调函数，参数为错误消息
 * @returns {boolean} 是否成功处理
 */
function handleApiResponse(response, onSuccess, onError) {
    console.log('API Response:', response);
    console.log('Response type:', typeof response);
    if (response) {
        console.log('Response keys:', Object.keys(response));
    }

    try {
        // 检查是否为统一响应格式
        if (response && response.meta) {
            console.log('Processing response with meta:', response.meta);
            // 统一响应格式
            if (response.meta.code === '20000' || response.meta.code === 20000) {
                console.log('Success response with meta, data:', response.data);
                if (onSuccess) {
                    onSuccess(response.data);
                }
                return true;
            } else {
                console.log('Error response with meta:', response.meta.message);
                if (onError) {
                    onError(response.meta.message || '请求失败');
                }
                return false;
            }
        } else if (response && response.code !== undefined) {
            console.log('Processing response with code:', response.code);
            // 另一种统一响应格式
            if (response.code === 0 || response.code === '0' || response.code === 20000 || response.code === '20000') {
                console.log('Success response with code, data:', response.data);
                if (onSuccess) {
                    onSuccess(response.data);
                }
                return true;
            } else {
                console.log('Error response with code:', response.message);
                if (onError) {
                    onError(response.message || '请求失败');
                }
                return false;
            }
        } else if (response && (response.success !== undefined)) {
            console.log('Processing response with success:', response.success);
            // 旧格式 - 直接包含success字段
            if (response.success) {
                console.log('Success response with success flag, data:', response);
                if (onSuccess) {
                    onSuccess(response);
                }
                return true;
            } else {
                console.log('Error response with success flag:', response.message || response.errorMessage);
                if (onError) {
                    onError(response.message || response.errorMessage || '请求失败');
                }
                return false;
            }
        } else {
            console.log('Processing unknown response format');
            // 其他格式，直接返回
            if (onSuccess) {
                onSuccess(response);
            }
            return true;
        }
    } catch (error) {
        console.error('Error processing API response:', error);
        if (onError) {
            onError('处理响应数据时发生错误: ' + error.message);
        }
        return false;
    }
}

/**
 * 显示消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, error, info, warning)
 */
function showMessage(message, type = 'info') {
    console.log('显示消息:', message, type);

    try {
        // 检查是否已存在消息容器
        let messageContainer = document.getElementById('messageContainer');

        if (!messageContainer) {
            console.log('创建新的消息容器');
            // 创建消息容器
            messageContainer = document.createElement('div');
            messageContainer.id = 'messageContainer';
            messageContainer.style.position = 'fixed';
            messageContainer.style.top = '20px';
            messageContainer.style.right = '20px';
            messageContainer.style.zIndex = '9999';
            document.body.appendChild(messageContainer);
        }

        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = `alert alert-${type} alert-dismissible fade show`;
        messageElement.role = 'alert';
        messageElement.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // 添加到容器
        messageContainer.appendChild(messageElement);

        // 自动关闭
        setTimeout(() => {
            messageElement.classList.remove('show');
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.remove();
                }
            }, 300);
        }, 3000);
    } catch (error) {
        console.error('显示消息时发生错误:', error);
        // 使用原生alert作为备选方案
        alert(`${type.toUpperCase()}: ${message}`);
    }
}