/**
 * 对话上下文功能
 * 用于管理用户与系统之间的对话上下文
 */

// 全局变量
let contextualModeEnabled = true; // 默认启用对话模式
let userId = generateUserId(); // 生成唯一用户ID
let hasActiveContext = false;
let conversations = []; // 会话列表
let currentConversationId = 'new'; // 当前会话 ID

// 页面卸载时清理图表实例
window.addEventListener('beforeunload', function() {
    if (typeof cleanupAllCharts === 'function') {
        cleanupAllCharts();
    }
});

/**
 * 初始化对话模式
 */
function initConversationMode() {
    // 从本地存储中获取用户ID
    const storedUserId = localStorage.getItem('chatbi_user_id');
    if (storedUserId) {
        userId = storedUserId;
    } else {
        userId = generateUserId(); // 将返回'admin'
        localStorage.setItem('chatbi_user_id', userId);
    }

    // 确保userId为'admin'
    if (userId !== 'admin') {
        userId = 'admin';
        localStorage.setItem('chatbi_user_id', userId);
    }

    // 从本地存储中获取对话模式设置
    const storedContextMode = localStorage.getItem('chatbi_context_mode');
    if (storedContextMode) {
        contextualModeEnabled = storedContextMode === 'true';
    }

    // 从本地存储中获取会话列表
    const storedConversations = localStorage.getItem('chatbi_conversations');
    if (storedConversations) {
        try {
            conversations = JSON.parse(storedConversations);
        } catch (e) {
            console.error('解析会话列表失败:', e);
            conversations = [];
        }
    }

    // 从本地存储中获取当前会话 ID
    const storedCurrentConversationId = localStorage.getItem('chatbi_current_conversation_id');
    if (storedCurrentConversationId) {
        currentConversationId = storedCurrentConversationId;
    }

    // 无论如何，都尝试加载最近的会话
    console.log('首次访问页面，尝试加载最近的会话');
    // 先加载会话列表，然后在回调中切换到最近的会话
    // 或者创建新会话（如果没有会话）

    // 初始化对话模式切换
    const contextModeSwitch = document.getElementById('contextualMode');
    if (contextModeSwitch) {
        contextModeSwitch.checked = contextualModeEnabled;
        updateContextualModeUI();

        contextModeSwitch.addEventListener('change', function() {
            contextualModeEnabled = this.checked;
            localStorage.setItem('chatbi_context_mode', contextualModeEnabled);
            updateContextualModeUI();

            if (contextualModeEnabled) {
                // 加载对话上下文
                loadConversationContext();
            }
        });
    }

    // 清除上下文按钮
    const clearContextBtn = document.getElementById('clearContextBtn');
    if (clearContextBtn) {
        clearContextBtn.addEventListener('click', function() {
            clearConversationContext();
        });
    }

    // 初始化新建会话按钮
    const newChatBtn = document.getElementById('newChatBtn');
    if (newChatBtn) {
        newChatBtn.addEventListener('click', function() {
            createNewConversation();
        });
    }

    // 初始化示例查询按钮
    const showExamplesBtn = document.getElementById('showExamplesBtn');
    if (showExamplesBtn) {
        showExamplesBtn.addEventListener('click', function() {
            const examplesPanel = document.getElementById('examplesPanel');
            if (examplesPanel.style.display === 'none') {
                examplesPanel.style.display = 'block';
                showExamplesBtn.innerHTML = '<i class="bi bi-x-lg"></i> 隐藏示例';
            } else {
                examplesPanel.style.display = 'none';
                showExamplesBtn.innerHTML = '<i class="bi bi-lightbulb"></i> 示例查询';
            }
        });
    }

    // 加载会话列表，并指定为首次加载
    // 这样会自动加载最近的会话，或者创建新会话（如果没有会话）
    loadConversationList(true);
}

/**
 * 更新对话模式UI
 */
function updateContextualModeUI() {
    const clearContextBtn = document.getElementById('clearContextBtn');
    const conversationHistory = document.getElementById('conversationHistory');

    if (clearContextBtn) {
        clearContextBtn.style.display = contextualModeEnabled && hasActiveContext ? 'inline-block' : 'none';
    }

    if (conversationHistory) {
        conversationHistory.style.display = contextualModeEnabled && hasActiveContext ? 'block' : 'none';
    }
}

/**
 * 生成唯一用户ID
 * @returns {string} 用户ID
 */
function generateUserId() {
    // 默认返回admin作为用户ID
    return 'admin';
}

/**
 * 格式化列名
 * @param {string} columnName - 列名
 * @returns {string} 格式化后的列名
 */
function formatColumnName(columnName) {
    // 将下划线转换为空格，并将每个单词首字母大写
    return columnName.split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}

/**
 * 获取会话的时间戳
 * @param {Object} conversation - 会话对象
 * @returns {number} 时间戳
 */
function getConversationTimestamp(conversation) {
    if (!conversation) {
        return 0;
    }

    // 尝试不同的时间字段
    let timestamp = 0;

    // 尝试 lastUpdateTime
    if (conversation.lastUpdateTime) {
        if (typeof conversation.lastUpdateTime === 'string') {
            return new Date(conversation.lastUpdateTime).getTime();
        } else if (conversation.lastUpdateTime.year) {
            // 处理LocalDateTime格式
            return new Date(
                conversation.lastUpdateTime.year,
                conversation.lastUpdateTime.monthValue - 1,
                conversation.lastUpdateTime.dayOfMonth,
                conversation.lastUpdateTime.hour,
                conversation.lastUpdateTime.minute,
                conversation.lastUpdateTime.second
            ).getTime();
        }
    }

    // 尝试 lastUpdatedAt
    if (conversation.lastUpdatedAt) {
        if (typeof conversation.lastUpdatedAt === 'string') {
            return new Date(conversation.lastUpdatedAt).getTime();
        } else if (conversation.lastUpdatedAt.year) {
            // 处理LocalDateTime格式
            return new Date(
                conversation.lastUpdatedAt.year,
                conversation.lastUpdatedAt.monthValue - 1,
                conversation.lastUpdatedAt.dayOfMonth,
                conversation.lastUpdatedAt.hour,
                conversation.lastUpdatedAt.minute,
                conversation.lastUpdatedAt.second
            ).getTime();
        }
    }

    // 尝试 createdAt
    if (conversation.createdAt) {
        if (typeof conversation.createdAt === 'string') {
            return new Date(conversation.createdAt).getTime();
        } else if (conversation.createdAt.year) {
            // 处理LocalDateTime格式
            return new Date(
                conversation.createdAt.year,
                conversation.createdAt.monthValue - 1,
                conversation.createdAt.dayOfMonth,
                conversation.createdAt.hour,
                conversation.createdAt.minute,
                conversation.createdAt.second
            ).getTime();
        }
    }

    return timestamp;
}

/**
 * 格式化单元格值
 * @param {*} value - 单元格值
 * @returns {string} 格式化后的单元格值
 */
function formatCellValue(value) {
    if (value === null || value === undefined) {
        return '-';
    }

    if (typeof value === 'number') {
        // 格式化数字
        if (Number.isInteger(value)) {
            return value.toString();
        } else {
            return value.toFixed(2);
        }
    }

    if (typeof value === 'boolean') {
        return value ? '是' : '否';
    }

    return value.toString();
}

/**
 * 加载对话上下文
 */
function loadConversationContext() {
    if (!contextualModeEnabled || !userId) {
        return;
    }

    // 如果是新会话，不需要加载上下文
    if (currentConversationId === 'new') {
        hasActiveContext = false;
        updateContextualModeUI();
        return;
    }

    // 加载指定的会话
    loadConversationById(currentConversationId);
}

/**
 * 根据会话ID加载会话
 * @param {string} conversationId 会话ID
 */
function loadConversationById(conversationId) {
    if (!userId || !conversationId) {
        return;
    }

    fetch(`/api/nl-query/conversation?userId=${userId}&conversationId=${conversationId}`)
        .then(response => response.json())
        .then(response => {
            console.log('获取会话原始响应:', response);

            // 处理响应数据
            let data;

            // 判断响应格式
            if (response && response.meta && response.data) {
                // 统一响应格式
                console.log('使用统一响应格式处理会话');
                data = response.data;
            } else {
                // 直接使用响应
                console.log('使用直接响应格式处理会话');
                data = response;
            }

            console.log('处理后的会话数据:', data);

            if (data.hasContext) {
                hasActiveContext = true;
                updateContextualModeUI();

                // 更新对话历史显示
                if (data.exchanges && data.exchanges.length > 0) {
                    updateConversationHistoryDisplay(data.exchanges);
                }

                // 更新当前会话ID
                currentConversationId = data.conversationId;
                localStorage.setItem('chatbi_current_conversation_id', currentConversationId);

                // 更新会话列表选中状态
                updateConversationListSelection();
            } else {
                hasActiveContext = false;
                updateContextualModeUI();
            }
        })
        .catch(error => {
            console.error('获取会话失败:', error);
            hasActiveContext = false;
            updateContextualModeUI();
        });
}

/**
 * 更新对话历史显示
 * @param {Array} exchanges 对话交换记录
 */
function updateConversationHistoryDisplay(exchanges) {
    const conversationExchanges = document.getElementById('conversationExchanges');
    if (!conversationExchanges || !exchanges || exchanges.length === 0) {
        return;
    }

    console.log('更新对话历史显示:', exchanges);

    let html = '';
    exchanges.forEach(exchange => {
        // 处理时间戳
        let timestamp;
        if (exchange.timestamp) {
            if (typeof exchange.timestamp === 'string') {
                timestamp = new Date(exchange.timestamp).toLocaleString();
            } else if (exchange.timestamp.year) {
                // 处理LocalDateTime格式
                const date = new Date(
                    exchange.timestamp.year,
                    exchange.timestamp.monthValue - 1,
                    exchange.timestamp.dayOfMonth,
                    exchange.timestamp.hour,
                    exchange.timestamp.minute,
                    exchange.timestamp.second
                );
                timestamp = date.toLocaleString();
            } else {
                timestamp = new Date().toLocaleString();
            }
        } else {
            timestamp = new Date().toLocaleString();
        }

        // 处理结果信息
        let resultInfo = '';

        if (exchange.resultSummary) {
            resultInfo = `结果: ${exchange.resultSummary.rowCount || 0} 行数据`;
            if (exchange.resultSummary.columnNames && exchange.resultSummary.columnNames.length > 0) {
                resultInfo += `, 包含列: ${exchange.resultSummary.columnNames.join(', ')}`;
            }
        } else if (exchange.resultCount) {
            resultInfo = `结果: ${exchange.resultCount || 0} 行数据`;
        }

        // 生成结果表格HTML
        let resultTableHtml = '';
        let hasResults = false;
        if (exchange.results && Array.isArray(exchange.results) && exchange.results.length > 0) {
            resultTableHtml = generateConversationResultTable(exchange.results, exchange.columns);
            hasResults = true;
        } else if (exchange.resultData && Array.isArray(exchange.resultData) && exchange.resultData.length > 0) {
            // 使用保存的完整查询结果数据
            resultTableHtml = generateConversationResultTable(exchange.resultData, exchange.resultSummary ? exchange.resultSummary.columnNames : null);
            hasResults = true;
        } else if (exchange.resultSummary && exchange.resultSummary.results && Array.isArray(exchange.resultSummary.results)) {
            resultTableHtml = generateConversationResultTable(exchange.resultSummary.results, exchange.resultSummary.columns);
            hasResults = true;
        }

        // 生成图表容器HTML
        const chartContainerId = 'chart-history-' + Date.now() + '-' + Math.random().toString(36).substring(2, 8);
        const chartHtml = `
            <div class="conversation-chart-container">
                <canvas id="${chartContainerId}"></canvas>
            </div>
        `;

        // 生成SQL显示HTML
        const sqlId = `sql-history-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
        const sqlHtml = exchange.generatedSql ? `
            <div class="conversation-sql">
                <button class="copy-sql-btn" onclick="copySqlToClipboard('${sqlId}')"><i class="bi bi-clipboard"></i> 复制</button>
                <code id="${sqlId}">${exchange.generatedSql}</code>
            </div>` : '';

        // 生成唯一的tab ID
        const tabId = 'tab-history-' + Date.now() + '-' + Math.random().toString(36).substring(2, 8);
        const resultTabId = `${tabId}-results`;
        const sqlTabId = `${tabId}-sql`;
        const chartTabId = `${tabId}-chart`;

        // 准备结果数据和列名的JSON字符串，用于存储在data属性中
        let resultDataJson = '';
        let columnNamesJson = '';

        if (exchange.resultData && Array.isArray(exchange.resultData)) {
            try {
                resultDataJson = JSON.stringify(exchange.resultData);
                if (exchange.resultSummary && exchange.resultSummary.columnNames) {
                    columnNamesJson = JSON.stringify(exchange.resultSummary.columnNames);
                }
            } catch (e) {
                console.error('序列化结果数据时出错:', e);
            }
        }

        html += `
            <div class="conversation-exchange" ${resultDataJson ? `data-result-data='${resultDataJson}'` : ''} ${columnNamesJson ? `data-column-names='${columnNamesJson}'` : ''}>
                <div class="user-query">
                    <span class="query-label">问:</span> ${exchange.userQuery}
                </div>
                <div class="system-response">
                    <span class="response-label">答:</span>
                    <div class="response-meta">${resultInfo}</div>

                    ${hasResults || exchange.generatedSql ? `
                    <ul class="nav conversation-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link active" data-bs-toggle="tab" href="#${resultTabId}" role="tab" aria-selected="true">查询结果</a>
                        </li>
                        ${exchange.generatedSql ? `
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" data-bs-toggle="tab" href="#${sqlTabId}" role="tab" aria-selected="false">SQL查询</a>
                        </li>` : ''}
                        ${hasResults ? `
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" data-bs-toggle="tab" href="#${chartTabId}" role="tab" aria-selected="false">可视化</a>
                        </li>` : ''}
                    </ul>

                    <div class="conversation-tab-content">
                        <div class="conversation-tab-pane active" id="${resultTabId}" role="tabpanel">
                            ${resultTableHtml}
                        </div>
                        ${exchange.generatedSql ? `
                        <div class="conversation-tab-pane" id="${sqlTabId}" role="tabpanel">
                            ${sqlHtml}
                        </div>` : ''}
                        ${hasResults ? `
                        <div class="conversation-tab-pane" id="${chartTabId}" role="tabpanel">
                            ${chartHtml}
                        </div>` : ''}
                    </div>` : ''}
                </div>
                <div class="exchange-time">${timestamp}</div>
            </div>
        `;

        // 记录图表ID和数据，以便后续渲染
        if (exchange.results && Array.isArray(exchange.results) && exchange.results.length > 0) {
            setTimeout(() => {
                const chartElement = document.getElementById(chartContainerId);
                if (chartElement) {
                    generateConversationChart(chartContainerId, exchange.results, exchange.columns);
                }
            }, 100);
        } else if (exchange.resultData && Array.isArray(exchange.resultData) && exchange.resultData.length > 0) {
            // 使用保存的完整查询结果数据生成图表
            setTimeout(() => {
                const chartElement = document.getElementById(chartContainerId);
                if (chartElement) {
                    generateConversationChart(chartContainerId, exchange.resultData, exchange.resultSummary ? exchange.resultSummary.columnNames : null);
                }
            }, 100);
        } else if (exchange.resultSummary && exchange.resultSummary.results && Array.isArray(exchange.resultSummary.results) && exchange.resultSummary.results.length > 0) {
            setTimeout(() => {
                const chartElement = document.getElementById(chartContainerId);
                if (chartElement) {
                    generateConversationChart(chartContainerId, exchange.resultSummary.results, exchange.resultSummary.columns);
                }
            }, 100);
        }
    });

    conversationExchanges.innerHTML = html;

    // 初始化标签页切换
    initConversationTabs();
}

/**
 * 清除对话上下文
 */
function clearConversationContext() {
    if (!userId) {
        return;
    }

    fetch(`/api/nl-query/clear-context?userId=${userId}`, {
        method: 'POST'
    })
        .then(response => response.json())
        .then(response => {
            console.log('清除对话上下文原始响应:', response);

            // 处理响应数据
            let data;

            // 判断响应格式
            if (response && response.meta && response.data) {
                // 统一响应格式
                console.log('使用统一响应格式处理清除上下文');
                data = response.data;
                // 如果没有success字段，则默认为成功
                if (data.success === undefined) {
                    data.success = true;
                }
            } else {
                // 直接使用响应
                console.log('使用直接响应格式处理清除上下文');
                data = response;
                // 如果没有success字段，则默认为成功
                if (data.success === undefined) {
                    data.success = true;
                }
            }

            console.log('处理后的清除上下文数据:', data);

            if (data.success) {
                // 清理所有图表实例
                cleanupAllCharts();

                hasActiveContext = false;
                updateContextualModeUI();

                // 清空对话历史显示
                const conversationExchanges = document.getElementById('conversationExchanges');
                if (conversationExchanges) {
                    conversationExchanges.innerHTML = '';
                }

                showMessage('对话上下文已清除', 'success');
            } else {
                showMessage(data.message || '清除对话上下文失败', 'error');
            }
        })
        .catch(error => {
            console.error('清除对话上下文失败:', error);
            showMessage('清除对话上下文失败: ' + error, 'error');
        });
}

/**
 * 加载会话列表
 * @param {boolean} isInitialLoad - 是否是首次加载
 */
function loadConversationList(isInitialLoad = false) {
    console.log('开始加载会话列表', isInitialLoad ? '(首次加载)' : '');

    if (!userId) {
        console.warn('用户ID不存在，无法加载会话列表');
        return;
    }

    try {
        // 获取会话列表容器
        const conversationListElement = document.getElementById('conversationList');
        if (!conversationListElement) {
            console.error('会话列表容器不存在');
            return;
        }

        console.log('从服务器获取会话列表，用户ID:', userId);

        // 从服务器获取会话列表
        fetch(`/api/nl-query/conversations?userId=${userId}`)
            .then(response => {
                console.log('获取到原始响应:', response);
                return response.json();
            })
            .then(response => {
                console.log('获取会话列表原始响应:', response);

                // 处理响应数据
                let data;

                // 判断响应格式
                if (response && response.meta && response.data) {
                    // 统一响应格式
                    console.log('使用统一响应格式处理会话列表');
                    if (response.data && response.data.conversations) {
                        // 新的统一响应格式，data中包含conversations字段
                        console.log('从data中提取conversations字段');
                        data = response.data.conversations;
                    } else {
                        // 旧的统一响应格式，data直接是会话列表
                        data = response.data;
                    }
                } else if (response && response.conversations) {
                    // 直接使用响应中的conversations字段
                    console.log('使用响应中的conversations字段');
                    data = response.conversations;
                } else {
                    // 直接使用响应
                    console.log('使用直接响应格式处理会话列表');
                    data = response;
                }

                // 输出详细的响应信息以便调试
                console.log('响应完整内容:', JSON.stringify(response));
                console.log('处理后的数据:', JSON.stringify(data));

                console.log('处理后的会话列表数据:', data);

                // 更新全局会话列表
                if (Array.isArray(data)) {
                    conversations = data;
                    // 保存到本地存储
                    localStorage.setItem('chatbi_conversations', JSON.stringify(conversations));
                    console.log('已更新会话列表，数量:', conversations.length);

                    // 如果是首次加载，判断是否有会话
                    if (isInitialLoad) {
                        if (conversations.length > 0) {
                            // 有会话，按时间排序，找到最近的会话
                            const sortedConversations = [...conversations].sort((a, b) => {
                                const timeA = getConversationTimestamp(a);
                                const timeB = getConversationTimestamp(b);
                                return timeB - timeA; // 降序排列，最近的在前面
                            });

                            if (sortedConversations.length > 0) {
                                const mostRecentConversation = sortedConversations[0];
                                console.log('找到最近的会话:', mostRecentConversation.conversationId);
                                // 强制设置当前会话ID为最近的会话，即使本地存储中有其他值
                                currentConversationId = mostRecentConversation.conversationId;
                                localStorage.setItem('chatbi_current_conversation_id', currentConversationId);
                                console.log('强制设置当前会话ID为:', currentConversationId);
                            }
                        } else {
                            // 没有会话，设置为新会话
                            console.log('没有找到会话，创建新会话');
                            currentConversationId = 'new';
                            localStorage.setItem('chatbi_current_conversation_id', currentConversationId);
                        }
                    }
                } else {
                    console.warn('响应数据不是数组格式:', data);
                }

                // 更新UI
                updateConversationListUI();

                // 如果是首次加载
                if (isInitialLoad) {
                    if (currentConversationId !== 'new') {
                        // 如果有会话，加载该会话
                        console.log('首次加载，加载会话:', currentConversationId);
                        // 先清空对话历史显示，防止显示旧内容
                        const conversationExchanges = document.getElementById('conversationExchanges');
                        if (conversationExchanges) {
                            conversationExchanges.innerHTML = '';
                        }
                        // 加载会话
                        loadConversationById(currentConversationId);
                    } else {
                        // 如果没有会话，显示新会话界面
                        console.log('首次加载，没有会话，显示新会话界面');
                        // 清空对话历史显示
                        const conversationExchanges = document.getElementById('conversationExchanges');
                        if (conversationExchanges) {
                            conversationExchanges.innerHTML = '';
                        }
                    }
                    // 更新选中状态
                    updateConversationListSelection();
                }
            })
            .catch(error => {
                console.error('获取会话列表失败:', error);
                // 使用本地存储的会话列表
                updateConversationListUI();
            });
    } catch (error) {
        console.error('加载会话列表时发生错误:', error);
        // 尝试使用本地存储的会话列表
        try {
            updateConversationListUI();
        } catch (e) {
            console.error('更新会话列表UI时发生错误:', e);
        }
    }
}

/**
 * 更新会话列表UI
 */
function updateConversationListUI() {
    console.log('开始更新会话列表UI');

    try {
        const conversationListElement = document.getElementById('conversationList');
        if (!conversationListElement) {
            console.error('会话列表元素不存在');
            return;
        }

        // 初始化HTML
        let html = '';

        // 如果有会话且当前会话ID不是'new'，则不需要显示新会话项
        // 如果没有会话或者当前会话ID是'new'，则显示新会话项
        if (Array.isArray(conversations) && conversations.length === 0 || currentConversationId === 'new') {
            html = `
                <div class="conversation-item ${currentConversationId === 'new' ? 'active' : ''}" data-id="new" onclick="switchConversation('new')">
                    <div class="conversation-icon">
                        <i class="bi bi-chat-dots"></i>
                    </div>
                    <div class="conversation-info">
                        <div class="conversation-title">新对话</div>
                        <div class="conversation-preview">开始新的对话...</div>
                    </div>
                    <div class="conversation-time">Now</div>
                </div>
            `;
        }

        // 添加现有会话
        if (Array.isArray(conversations) && conversations.length > 0) {
            console.log('处理', conversations.length, '个会话');

            conversations.forEach((conversation, index) => {
                try {
                    if (!conversation || !conversation.conversationId) {
                        console.warn('跳过无效会话项:', conversation);
                        return;
                    }

                    console.log(`处理会话 ${index + 1}/${conversations.length}: ${conversation.conversationId}`);

                    // 处理时间戳
                    let timestamp;
                    if (conversation.lastUpdateTime) {
                        if (typeof conversation.lastUpdateTime === 'string') {
                            timestamp = new Date(conversation.lastUpdateTime).toLocaleString();
                        } else if (conversation.lastUpdateTime.year) {
                            // 处理LocalDateTime格式
                            const date = new Date(
                                conversation.lastUpdateTime.year,
                                conversation.lastUpdateTime.monthValue - 1,
                                conversation.lastUpdateTime.dayOfMonth,
                                conversation.lastUpdateTime.hour,
                                conversation.lastUpdateTime.minute,
                                conversation.lastUpdateTime.second
                            );
                            timestamp = date.toLocaleString();
                        } else {
                            timestamp = new Date().toLocaleString();
                        }
                    } else if (conversation.lastUpdatedAt) {
                        // 尝试使用lastUpdatedAt
                        if (typeof conversation.lastUpdatedAt === 'string') {
                            timestamp = new Date(conversation.lastUpdatedAt).toLocaleString();
                        } else if (conversation.lastUpdatedAt.year) {
                            // 处理LocalDateTime格式
                            const date = new Date(
                                conversation.lastUpdatedAt.year,
                                conversation.lastUpdatedAt.monthValue - 1,
                                conversation.lastUpdatedAt.dayOfMonth,
                                conversation.lastUpdatedAt.hour,
                                conversation.lastUpdatedAt.minute,
                                conversation.lastUpdatedAt.second
                            );
                            timestamp = date.toLocaleString();
                        } else {
                            timestamp = new Date().toLocaleString();
                        }
                    } else if (conversation.createdAt) {
                        // 尝试使用createdAt
                        if (typeof conversation.createdAt === 'string') {
                            timestamp = new Date(conversation.createdAt).toLocaleString();
                        } else {
                            timestamp = new Date().toLocaleString();
                        }
                    } else {
                        timestamp = new Date().toLocaleString();
                    }

                    console.log('会话时间戳:', timestamp);

                    // 获取预览文本
                    let previewText = '空对话';
                    let title = '对话 ' + conversation.conversationId.substring(0, 8);

                    // 如果有标题，优先使用标题
                    if (conversation.title) {
                        title = conversation.title;
                    }

                    if (conversation.exchanges && conversation.exchanges.length > 0) {
                        const lastExchange = conversation.exchanges[conversation.exchanges.length - 1];
                        previewText = lastExchange.userQuery || '空查询';
                        if (previewText.length > 30) {
                            previewText = previewText.substring(0, 30) + '...';
                        }

                        // 如果没有标题但有交互记录，使用第一个问题作为标题
                        if (!conversation.title && conversation.exchanges.length > 0) {
                            const firstExchange = conversation.exchanges[0];
                            if (firstExchange.userQuery) {
                                title = firstExchange.userQuery;
                                if (title.length > 20) {
                                    title = title.substring(0, 17) + '...';
                                }
                            }
                        }
                    } else if (conversation.lastQuery) {
                        // 尝试使用lastQuery
                        previewText = conversation.lastQuery;
                        if (previewText.length > 30) {
                            previewText = previewText.substring(0, 30) + '...';
                        }
                    }

                    console.log('会话标题:', title);
                    console.log('会话预览:', previewText);

                    // 确保会话ID是字符串
                    const conversationId = String(conversation.conversationId);
                    const shortId = conversationId.length > 8 ? conversationId.substring(0, 8) : conversationId;

                    html += `
                        <div class="conversation-item ${currentConversationId === conversationId ? 'active' : ''}"
                             data-id="${conversationId}"
                             onclick="switchConversation('${conversationId}')">
                            <div class="conversation-icon">
                                <i class="bi bi-chat-text"></i>
                            </div>
                            <div class="conversation-info">
                                <div class="conversation-title">${title}</div>
                                <div class="conversation-preview">${previewText}</div>
                            </div>
                            <div class="conversation-time">${timestamp}</div>
                        </div>
                    `;
                } catch (itemError) {
                    console.error('处理会话项时发生错误:', itemError, conversation);
                }
            });
        } else {
            console.log('没有现有会话或会话列表不是数组');
        }

        // 更新DOM
        console.log('更新会话列表DOM');
        conversationListElement.innerHTML = html;
        console.log('会话列表UI更新完成');
    } catch (error) {
        console.error('更新会话列表UI时发生错误:', error);
    }
}

/**
 * 更新会话列表选中状态
 */
function updateConversationListSelection() {
    const conversationItems = document.querySelectorAll('.conversation-item');
    conversationItems.forEach(item => {
        const id = item.getAttribute('data-id');
        if (id === currentConversationId) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });
}

/**
 * 切换会话
 * @param {string} conversationId 会话ID
 */
function switchConversation(conversationId) {
    if (conversationId === currentConversationId) {
        return;
    }

    // 清理所有图表实例，防止内存泄漏
    cleanupAllCharts();

    // 更新当前会话ID
    currentConversationId = conversationId;
    localStorage.setItem('chatbi_current_conversation_id', currentConversationId);

    // 更新选中状态
    updateConversationListSelection();

    // 加载会话内容
    loadConversationContext();

    // 清空查询输入框
    const queryInput = document.getElementById('nlQuery');
    if (queryInput) {
        queryInput.value = '';
    }

    // 隐藏结果区域
    const resultSection = document.getElementById('resultSection');
    if (resultSection) {
        resultSection.style.display = 'none';
    }
}

/**
 * 创建新会话
 * 不调用后端创建会话的API
 * 只在前端切换到新会话模式
 * 当用户进行第一次查询时，才会在后端创建并保存会话
 */
function createNewConversation() {
    if (!userId) {
        return;
    }

    // 直接切换到新会话模式，不调用后端接口
    switchConversation('new');

    // 清空对话历史显示
    const conversationExchanges = document.getElementById('conversationExchanges');
    if (conversationExchanges) {
        conversationExchanges.innerHTML = '';
    }

    // 显示提示消息
    showMessage('已切换到新对话模式，输入问题开始对话', 'info');

    // 聚焦到输入框
    const queryInput = document.getElementById('nlQuery');
    if (queryInput) {
        queryInput.focus();
    }
}

/**
 * 将当前查询和响应添加到对话显示
 * @param {string} query 用户查询
 * @param {Object} response 响应数据
 */
function addToConversationDisplay(query, response) {
    console.log('添加查询和响应到对话显示:', query, response);

    try {
        const conversationExchanges = document.getElementById('conversationExchanges');
        if (!conversationExchanges) {
            console.error('对话交换容器不存在');
            return;
        }

        // 处理结果信息
        let resultInfo = '';
        let rowCount = 0;
        let columnNames = [];

        if (response.results && Array.isArray(response.results)) {
            rowCount = response.results.length;
            if (response.columns && Array.isArray(response.columns)) {
                columnNames = response.columns.map(col => col.name || col);
            }
            resultInfo = `结果: ${rowCount} 行数据`;
            if (columnNames.length > 0) {
                resultInfo += `, 包含列: ${columnNames.join(', ')}`;
            }
        }

        // 生成结果表格HTML
        let resultTableHtml = '';
        if (response.results && Array.isArray(response.results) && response.results.length > 0) {
            resultTableHtml = generateConversationResultTable(response.results, response.columns);
        }

        // 生成图表容器HTML
        const chartContainerId = 'chart-' + Date.now();
        const chartHtml = `
            <div class="conversation-chart-container">
                <canvas id="${chartContainerId}"></canvas>
            </div>
        `;

        // 生成SQL显示HTML
        const sqlId = `sql-${Date.now()}`;
        const sqlHtml = response.generatedSql ? `
            <div class="conversation-sql">
                <button class="copy-sql-btn" onclick="copySqlToClipboard('${sqlId}')"><i class="bi bi-clipboard"></i> 复制</button>
                <code id="${sqlId}">${response.generatedSql}</code>
            </div>` : '';

        // 生成唯一的tab ID
        const tabId = 'tab-' + Date.now();
        const resultTabId = `${tabId}-results`;
        const sqlTabId = `${tabId}-sql`;
        const chartTabId = `${tabId}-chart`;

        // 准备结果数据和列名的JSON字符串，用于存储在data属性中
        let resultDataJson = '';
        let columnNamesJson = '';

        if (response.results && Array.isArray(response.results)) {
            try {
                resultDataJson = JSON.stringify(response.results);
                if (response.columns && Array.isArray(response.columns)) {
                    columnNamesJson = JSON.stringify(response.columns.map(col => col.name || col));
                }
            } catch (e) {
                console.error('序列化结果数据时出错:', e);
            }
        }

        // 创建新的对话交换HTML带有标签页
        const timestamp = new Date().toLocaleString();
        const newExchangeHtml = `
            <div class="conversation-exchange" ${resultDataJson ? `data-result-data='${resultDataJson}'` : ''} ${columnNamesJson ? `data-column-names='${columnNamesJson}'` : ''}>
                <div class="user-query">
                    <span class="query-label">问:</span> ${query}
                </div>
                <div class="system-response">
                    <span class="response-label">答:</span>
                    <div class="response-meta">${resultInfo}</div>

                    ${response.results && response.results.length > 0 || response.generatedSql ? `
                    <ul class="nav conversation-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link active" data-bs-toggle="tab" href="#${resultTabId}" role="tab" aria-selected="true">查询结果</a>
                        </li>
                        ${response.generatedSql ? `
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" data-bs-toggle="tab" href="#${sqlTabId}" role="tab" aria-selected="false">SQL查询</a>
                        </li>` : ''}
                        ${response.results && response.results.length > 0 ? `
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" data-bs-toggle="tab" href="#${chartTabId}" role="tab" aria-selected="false">可视化</a>
                        </li>` : ''}
                    </ul>

                    <div class="conversation-tab-content">
                        <div class="conversation-tab-pane active" id="${resultTabId}" role="tabpanel">
                            ${resultTableHtml}
                        </div>
                        ${response.generatedSql ? `
                        <div class="conversation-tab-pane" id="${sqlTabId}" role="tabpanel">
                            ${sqlHtml}
                        </div>` : ''}
                        ${response.results && response.results.length > 0 ? `
                        <div class="conversation-tab-pane" id="${chartTabId}" role="tabpanel">
                            ${chartHtml}
                        </div>` : ''}
                    </div>` : ''}
                </div>
                <div class="exchange-time">${timestamp}</div>
            </div>
        `;

        // 添加到对话历史
        const currentHtml = conversationExchanges.innerHTML;
        conversationExchanges.innerHTML = currentHtml + newExchangeHtml;

        // 初始化标签页切换
        initConversationTabs();

        // 如果有结果，生成图表
        if (response.results && Array.isArray(response.results) && response.results.length > 0) {
            setTimeout(() => {
                generateConversationChart(chartContainerId, response.results, response.columns);
            }, 100);
        }

        // 滚动到底部
        conversationExchanges.scrollTop = conversationExchanges.scrollHeight;
    } catch (error) {
        console.error('添加到对话显示时发生错误:', error);
    }
}

/**
 * 初始化对话框中的标签页
 */
function initConversationTabs() {
    // 为所有标签页添加点击事件
    document.querySelectorAll('.conversation-tabs .nav-link').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();

            // 获取目标标签页ID
            const target = this.getAttribute('href');

            // 找到当前标签组中的所有标签和标签页
            const tabGroup = this.closest('.conversation-tabs');
            const tabContent = tabGroup.nextElementSibling;

            // 移除所有标签的active类
            tabGroup.querySelectorAll('.nav-link').forEach(t => {
                t.classList.remove('active');
            });

            // 移除所有标签页的active类
            tabContent.querySelectorAll('.conversation-tab-pane').forEach(p => {
                p.classList.remove('active');
            });

            // 激活当前标签和标签页
            this.classList.add('active');
            document.querySelector(target).classList.add('active');
        });
    });
}

/**
 * 复制SQL到剪贴板
 * @param {string} sqlId SQL元素的ID
 */
function copySqlToClipboard(sqlId) {
    const sqlElement = document.getElementById(sqlId);
    if (!sqlElement) {
        console.error('SQL元素不存在:', sqlId);
        return;
    }

    const sqlText = sqlElement.textContent;

    // 使用剪贴板API复制文本
    navigator.clipboard.writeText(sqlText)
        .then(() => {
            // 显示复制成功的提示
            const button = sqlElement.previousElementSibling;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-check"></i> 已复制';
            button.style.backgroundColor = '#d4edda';
            button.style.color = '#155724';

            // 2秒后恢复按钮样式
            setTimeout(() => {
                button.innerHTML = originalText;
                button.style.backgroundColor = '';
                button.style.color = '';
            }, 2000);
        })
        .catch(err => {
            console.error('复制失败:', err);
            alert('复制失败: ' + err);
        });
}

/**
 * 为对话框生成结果表格HTML
 * @param {Array} results - 查询结果
 * @param {Array} columns - 列信息
 * @returns {string} 表格HTML
 */
function generateConversationResultTable(results, columns) {
    if (!results || results.length === 0) {
        return '<div class="alert alert-info">查询未返回任何结果</div>';
    }

    let tableHtml = '<div class="conversation-result-table"><table class="table table-striped"><thead><tr>';

    // 生成表头
    if (columns && columns.length > 0) {
        // 处理不同类型的列信息
        if (typeof columns[0] === 'string') {
            // 列名是字符串数组（从数据库恢复的情况）
            columns.forEach(columnName => {
                tableHtml += `<th>${formatColumnName(columnName)}</th>`;
            });
        } else {
            // 列信息是对象数组
            columns.forEach(column => {
                tableHtml += `<th>${column.label || column.name}</th>`;
            });
        }
    } else {
        // 如果没有提供列信息，使用第一行的键作为表头
        Object.keys(results[0]).forEach(key => {
            tableHtml += `<th>${formatColumnName(key)}</th>`;
        });
    }

    tableHtml += '</tr></thead><tbody>';

    // 生成表格内容
    results.forEach(row => {
        tableHtml += '<tr>';

        if (columns && columns.length > 0) {
            // 处理不同类型的列信息
            if (typeof columns[0] === 'string') {
                // 列名是字符串数组（从数据库恢复的情况）
                columns.forEach(columnName => {
                    tableHtml += `<td>${formatCellValue(row[columnName])}</td>`;
                });
            } else {
                // 列信息是对象数组
                columns.forEach(column => {
                    tableHtml += `<td>${formatCellValue(row[column.name])}</td>`;
                });
            }
        } else {
            Object.values(row).forEach(value => {
                tableHtml += `<td>${formatCellValue(value)}</td>`;
            });
        }

        tableHtml += '</tr>';
    });

    tableHtml += '</tbody></table></div>';
    return tableHtml;
}

/**
 * 为对话框生成图表
 * @param {string} chartId - 图表容器ID
 * @param {Array} results - 查询结果
 * @param {Array} columns - 列信息
 */
// 存储图表实例的对象，用于在重新创建图表前销毁旧图表
const chartInstances = {};

/**
 * 清理所有图表实例
 */
function cleanupAllCharts() {
    Object.keys(chartInstances).forEach(chartId => {
        try {
            if (chartInstances[chartId]) {
                console.log('清理图表:', chartId);
                chartInstances[chartId].destroy();
                delete chartInstances[chartId];
            }
        } catch (error) {
            console.error('清理图表时出错:', error);
        }
    });
}

function generateConversationChart(chartId, results, columns) {
    if (!results || results.length === 0) {
        return;
    }

    const chartElement = document.getElementById(chartId);
    if (!chartElement) {
        console.error('图表容器不存在:', chartId);
        return;
    }

    try {
        // 检查是否已存在图表实例，如果存在则销毁
        if (chartInstances[chartId]) {
            console.log('销毁已存在的图表:', chartId);
            chartInstances[chartId].destroy();
            delete chartInstances[chartId];
        }

        // 默认使用柱状图
        const chartType = 'bar';
        const ctx = chartElement.getContext('2d');

        // 准备图表数据
        const chartData = prepareChartData(results, columns, chartType);

        // 创建图表并保存实例
        chartInstances[chartId] = new Chart(ctx, {
            type: chartType,
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: '查询结果可视化'
                    }
                }
            }
        });
    } catch (error) {
        console.error('生成图表时出错:', error);
    }
}

/**
 * 准备图表数据
 * @param {Array} results - 查询结果
 * @param {Array} columns - 列信息
 * @param {string} chartType - 图表类型
 * @returns {Object} 图表数据
 */
function prepareChartData(results, columns, chartType) {
    // 确定标签列和数据列
    let labelColumn = null;
    let dataColumns = [];

    if (columns && columns.length > 0) {
        // 处理不同类型的列信息
        if (typeof columns[0] === 'string') {
            // 列名是字符串数组（从数据库恢复的情况）
            // 使用第一个列作为标签列，其余作为数据列
            labelColumn = columns[0];
            dataColumns = columns.slice(1);
        } else {
            // 列信息是对象数组
            // 尝试找到合适的标签列和数据列
            columns.forEach(column => {
                // 防止column为undefined或null
                if (!column) return;

                // 检查是否是字符串类型列
                const isStringType = column.type === 'String';

                // 安全地检查name属性是否存在并包含特定字符串
                const hasNameKeyword = column.name && typeof column.name === 'string' &&
                    (column.name.indexOf('name') !== -1 || column.name.indexOf('category') !== -1);

                if (isStringType || hasNameKeyword) {
                    if (!labelColumn && column.name) {
                        labelColumn = column.name;
                    }
                } else if (column.type === 'Integer' || column.type === 'Double' || column.type === 'Float' ||
                          column.type === 'Long' || column.type === 'BigDecimal') {
                    if (column.name) {
                        dataColumns.push(column.name);
                    }
                }
            });

            // 如果没有找到合适的标签列，使用第一列
            if (!labelColumn && columns.length > 0 && columns[0] && columns[0].name) {
                labelColumn = columns[0].name;
            }

            // 如果没有找到合适的数据列，使用除标签列外的所有列
            if (dataColumns.length === 0) {
                dataColumns = columns
                    .filter(column => column && column.name && column.name !== labelColumn)
                    .map(column => column.name)
                    .filter(name => name); // 过滤掉可能的undefined或null
            }
        }
    } else if (results && results.length > 0) {
        // 如果没有提供列信息，使用第一个键作为标签列，其余作为数据列
        const keys = Object.keys(results[0]);
        labelColumn = keys[0];
        dataColumns = keys.slice(1);
    } else {
        return { labels: [], datasets: [] };
    }

    // 安全地提取标签和数据
    const labels = results.map(row => {
        // 确保row存在且labelColumn有效
        if (!row || !labelColumn) return '';
        return row[labelColumn] !== undefined ? row[labelColumn] : '';
    });

    // 为每个数据列创建一个数据集
    const datasets = dataColumns.filter(column => column).map((column, index) => {
        // 安全地提取数据
        const data = results.map(row => {
            if (!row) return null;
            return row[column] !== undefined ? row[column] : null;
        });

        // 生成随机颜色
        const color = getRandomColor(index);

        return {
            label: column ? formatColumnName(column) : `数据列${index + 1}`,
            data: data,
            backgroundColor: chartType === 'line' ? 'rgba(0, 0, 0, 0.1)' : getRandomColors(data.length, index),
            borderColor: color,
            borderWidth: 1
        };
    });

    return {
        labels: labels,
        datasets: datasets
    };
}

/**
 * 获取随机颜色
 * @param {number} index - 索引
 * @returns {string} 随机颜色
 */
function getRandomColor(index) {
    const colors = [
        'rgba(255, 99, 132, 1)',
        'rgba(54, 162, 235, 1)',
        'rgba(255, 206, 86, 1)',
        'rgba(75, 192, 192, 1)',
        'rgba(153, 102, 255, 1)',
        'rgba(255, 159, 64, 1)',
        'rgba(199, 199, 199, 1)',
        'rgba(83, 102, 255, 1)',
        'rgba(40, 159, 64, 1)',
        'rgba(210, 199, 199, 1)'
    ];

    return colors[index % colors.length];
}

/**
 * 获取随机颜色数组
 * @param {number} count - 颜色数量
 * @param {number} index - 索引
 * @returns {Array} 随机颜色数组
 */
function getRandomColors(count, index) {
    const baseColor = getRandomColor(index);
    const colors = [];

    for (let i = 0; i < count; i++) {
        colors.push(baseColor);
    }

    return colors;
}

/**
 * 执行上下文查询
 * @param {string} query 查询文本
 */
function executeContextualQuery(query) {
    // 显示加载状态
    document.getElementById('loadingIndicator').style.display = 'flex';
    document.getElementById('resultSection').style.display = 'none';

    // 获取选中的数据源
    const dataSourceSelect = document.getElementById('dataSourceSelect');
    const dataSourceId = parseInt(dataSourceSelect.value);

    // 构建请求
    const request = {
        query: query,
        needExplanation: false, // 始终不请求SQL解释
        saveHistory: true,
        dataSourceId: dataSourceId,
        userId: userId
    };

    // 添加用户查询到对话框
    const conversationExchanges = document.getElementById('conversationExchanges');
    if (conversationExchanges) {
        const timestamp = new Date().toLocaleString();
        const userQueryHtml = `
            <div class="conversation-exchange">
                <div class="user-query">
                    <span class="query-label">问:</span> ${query}
                </div>
                <div class="system-response loading">
                    <span class="response-label">答:</span> <i class="bi bi-hourglass-split"></i> 正在处理查询...
                </div>
                <div class="exchange-time">${timestamp}</div>
            </div>
        `;
        const currentHtml = conversationExchanges.innerHTML;
        conversationExchanges.innerHTML = currentHtml + userQueryHtml;
        conversationExchanges.scrollTop = conversationExchanges.scrollHeight;
    }

    // 确定要使用的API端点
    let apiEndpoint;

    // 如果是新会话模式，直接使用普通查询接口
    // 这样后端会自动创建一个新会话并保存第一次问答交互
    if (currentConversationId === 'new') {
        console.log('新会话模式，直接使用普通查询接口');
        apiEndpoint = '/api/nl-query';
        return continueWithQuery(apiEndpoint, request);
    } else {
        console.log('使用会话查询接口，当前会话ID:', currentConversationId);
        // 使用正确的后端API端点，并添加conversationId参数
        apiEndpoint = `/api/nl-query/conversation-query?conversationId=${encodeURIComponent(currentConversationId)}`;
        return continueWithQuery(apiEndpoint, request);
    }

}

/**
 * 在指定会话中执行查询
 * @param {string} query 查询文本
 * @param {string} conversationId 会话ID
 * @returns {Promise} 查询响应的Promise
 */
function executeConversationQuery(query, conversationId) {
    console.log('在会话中执行查询:', query, '会话ID:', conversationId);

    // 获取选中的数据源
    const dataSourceSelect = document.getElementById('dataSourceSelect');
    const dataSourceId = parseInt(dataSourceSelect.value);

    // 构建请求
    const request = {
        query: query,
        needExplanation: false,
        saveHistory: true,
        dataSourceId: dataSourceId,
        userId: userId
    };

    // 发送请求
    return fetch(`/api/nl-query/conversation-query?conversationId=${conversationId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    })
    .then(response => response.json())
    .then(response => processQueryResponse(response))
    .catch(error => handleQueryError(error));
}

/**
 * 继续执行查询
 * @param {string} apiEndpoint API端点
 * @param {Object} request 请求对象
 * @returns {Promise} 查询响应的Promise
 */
function continueWithQuery(apiEndpoint, request) {
    // 发送请求
    return fetch(apiEndpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    })
    .then(response => response.json())
    .then(response => processQueryResponse(response))
    .catch(error => handleQueryError(error));
}

/**
 * 处理查询响应
 * @param {Object} response 响应对象
 */
function processQueryResponse(response) {
    // 隐藏加载状态
    document.getElementById('loadingIndicator').style.display = 'none';

    console.log('查询原始响应:', response);

    // 处理响应数据
    let data;

    // 判断响应格式
    if (response && response.meta && response.data) {
        // 统一响应格式
        console.log('使用统一响应格式处理查询');
        data = response.data;
    } else {
        // 直接使用响应
        console.log('使用直接响应格式处理查询');
        data = response;
    }

    console.log('处理后的查询数据:', data);

    // 检查是否有错误
    if (data && data.success === false) {
        // 移除加载中的消息
        const conversationExchanges = document.getElementById('conversationExchanges');
        if (conversationExchanges) {
            const loadingElements = conversationExchanges.querySelectorAll('.system-response.loading');
            if (loadingElements.length > 0) {
                const lastLoadingElement = loadingElements[loadingElements.length - 1];
                lastLoadingElement.innerHTML = `
                    <span class="response-label">答:</span>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> ${data.message || data.errorMessage || '查询处理失败'}
                    </div>
                `;
                lastLoadingElement.classList.remove('loading');
            }
        }

        document.getElementById('resultSection').style.display = 'block';
        document.getElementById('resultTable').innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> ${data.message || data.errorMessage || '查询处理失败'}
            </div>
        `;
        return;
    }

    // 保存响应
    currentResponse = data;

    // 更新对话框中的响应
    const conversationExchanges = document.getElementById('conversationExchanges');
    if (conversationExchanges) {
        const loadingElements = conversationExchanges.querySelectorAll('.system-response.loading');
        if (loadingElements.length > 0) {
            // 获取结果信息
            let resultInfo = '';
            let rowCount = 0;
            let columnNames = [];

            if (data.results && Array.isArray(data.results)) {
                rowCount = data.results.length;
                if (data.columns && Array.isArray(data.columns)) {
                    columnNames = data.columns.map(col => col.name || col);
                }
                resultInfo = `结果: ${rowCount} 行数据`;
                if (columnNames.length > 0) {
                    resultInfo += `, 包含列: ${columnNames.join(', ')}`;
                }
            }

            // 生成结果表格HTML
            let resultTableHtml = '';
            let hasResults = false;
            if (data.results && Array.isArray(data.results) && data.results.length > 0) {
                resultTableHtml = generateConversationResultTable(data.results, data.columns);
                hasResults = true;
            }

            // 生成图表容器HTML
            const chartContainerId = 'chart-' + Date.now();
            const chartHtml = `
                <div class="conversation-chart-container">
                    <canvas id="${chartContainerId}"></canvas>
                </div>
            `;

            // 生成SQL显示HTML
            const sqlId = `sql-contextual-${Date.now()}`;
            const sqlHtml = data.generatedSql ? `
                <div class="conversation-sql">
                    <button class="copy-sql-btn" onclick="copySqlToClipboard('${sqlId}')"><i class="bi bi-clipboard"></i> 复制</button>
                    <code id="${sqlId}">${data.generatedSql || '无SQL'}</code>
                </div>` : '';

                    // 生成唯一的tab ID
                    const tabId = 'tab-' + Date.now();
                    const resultTabId = `${tabId}-results`;
                    const sqlTabId = `${tabId}-sql`;
                    const chartTabId = `${tabId}-chart`;

                    // 更新最后一个加载中的元素
                    const lastLoadingElement = loadingElements[loadingElements.length - 1];
                    lastLoadingElement.innerHTML = `
                        <span class="response-label">答:</span>
                        <div class="response-meta">${resultInfo}</div>

                        ${hasResults || data.generatedSql ? `
                        <ul class="nav conversation-tabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <a class="nav-link active" data-bs-toggle="tab" href="#${resultTabId}" role="tab" aria-selected="true">查询结果</a>
                            </li>
                            ${data.generatedSql ? `
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" data-bs-toggle="tab" href="#${sqlTabId}" role="tab" aria-selected="false">SQL查询</a>
                            </li>` : ''}
                            ${hasResults ? `
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" data-bs-toggle="tab" href="#${chartTabId}" role="tab" aria-selected="false">可视化</a>
                            </li>` : ''}
                        </ul>

                        <div class="conversation-tab-content">
                            <div class="conversation-tab-pane active" id="${resultTabId}" role="tabpanel">
                                ${resultTableHtml}
                            </div>
                            ${data.generatedSql ? `
                            <div class="conversation-tab-pane" id="${sqlTabId}" role="tabpanel">
                                ${sqlHtml}
                            </div>` : ''}
                            ${hasResults ? `
                            <div class="conversation-tab-pane" id="${chartTabId}" role="tabpanel">
                                ${chartHtml}
                            </div>` : ''}
                        </div>` : ''}
                    `;
                    lastLoadingElement.classList.remove('loading');

                    // 初始化标签页切换
                    initConversationTabs();

                    // 如果有结果，生成图表
                    if (hasResults) {
                        setTimeout(() => {
                            generateConversationChart(chartContainerId, data.results, data.columns);
                        }, 100);
                    }
                }
            }

    // 不再显示单独的结果区域，因为结果已经在对话框中显示
    document.getElementById('resultSection').style.display = 'none';
    document.getElementById('loadingIndicator').style.display = 'none';

    // 显示数据源信息
    if (data.dataSource) {
        console.log('查询使用数据源: ' + data.dataSource);
    }

    // 更新上下文状态
    hasActiveContext = data.hasContext;
    updateContextualModeUI();

    // 如果是新会话模式且返回了会话ID，更新当前会话ID
    if (currentConversationId === 'new' && data.conversationId) {
        console.log('新会话创建成功，会话ID:', data.conversationId);
        currentConversationId = data.conversationId;
        localStorage.setItem('chatbi_current_conversation_id', currentConversationId);
        showMessage('新会话已创建并保存第一次问答交互', 'success');
    }

    // 刷新会话列表
    console.log('查询完成，开始刷新会话列表');

    // 等待一小段时间再加载会话列表，确保后端有足够时间完成保存
    setTimeout(() => {
        loadConversationList();
        // 更新选中状态
        updateConversationListSelection();
    }, 500);

    return data;
}

/**
 * 处理查询错误
 * @param {Error} error 错误对象
 */
function handleQueryError(error) {
    console.error('Error:', error);
    document.getElementById('loadingIndicator').style.display = 'none';

    // 更新对话框中的错误信息
    const conversationExchanges = document.getElementById('conversationExchanges');
    if (conversationExchanges) {
        const loadingElements = conversationExchanges.querySelectorAll('.system-response.loading');
        if (loadingElements.length > 0) {
            const lastLoadingElement = loadingElements[loadingElements.length - 1];
            lastLoadingElement.innerHTML = `
                <span class="response-label">答:</span>
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> 查询处理失败，请重试
                </div>
            `;
            lastLoadingElement.classList.remove('loading');
        }
    }

    showMessage('查询处理失败，请重试', 'error');
    return null;
}

/**
 * 页面加载完成后初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，初始化对话模式');

    // 初始化对话模式
    initConversationMode();

    // 页面刷新后，重新初始化图表
    setTimeout(() => {
        // 查找所有图表容器
        const chartContainers = document.querySelectorAll('.conversation-chart-container canvas');
        console.log('找到', chartContainers.length, '个图表容器');

        // 对每个图表容器，尝试重新初始化图表
        chartContainers.forEach(chartElement => {
            const chartId = chartElement.id;
            const exchangeDiv = chartElement.closest('.conversation-exchange');

            if (exchangeDiv) {
                // 尝试从交换记录中获取数据
                const userQueryDiv = exchangeDiv.querySelector('.user-query');
                const userQuery = userQueryDiv ? userQueryDiv.textContent.replace('问:', '').trim() : '';

                console.log('尝试重新初始化图表:', chartId, '用户查询:', userQuery);

                // 如果有结果数据属性，使用它重新生成图表
                if (exchangeDiv.dataset.resultData) {
                    try {
                        const resultData = JSON.parse(exchangeDiv.dataset.resultData);
                        const columnNames = exchangeDiv.dataset.columnNames ?
                            JSON.parse(exchangeDiv.dataset.columnNames) : null;

                        if (resultData && Array.isArray(resultData) && resultData.length > 0) {
                            generateConversationChart(chartId, resultData, columnNames);
                        }
                    } catch (e) {
                        console.error('解析结果数据时出错:', e);
                    }
                }
            }
        });
    }, 500); // 等待500毫秒，确保页面元素已完全加载
});
