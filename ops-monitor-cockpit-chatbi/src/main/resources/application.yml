spring:
  application:
    name: ops-monitor-cockpit-chatbi
  # 配置文件上传
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

  # 向量存储类型配置 (elasticsearch 或 milvus)
  vector-store:
    type: milvus  # 使用 Elasticsearch 作为向量存储

  # Spring AI 配置
  ai:
    # Ollama 配置
    ollama:
      chat:
        options:
          model: qwen3:4b  # 或其他支持的模型，如 mistral, llama2, qwen2.5-coder 等
        enabled: true
      base-url: http://localhost:11434  # Ollama 服务地址

    # Chat Memory 配置
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: always  # 自动初始化数据库表
            table-name: spring_ai_chat_memory  # 自定义表名
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  profiles:
    active: dev
server:
  port: 8080

# 日志配置
logging:
  level:
    root: INFO
    com.chinamobile.cmss.ops.fms.chatbi.mapper: DEBUG  # 增加Mapper日志级别以便调试

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.chinamobile.cmss.ops.fms.chatbi.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    jdbc-type-for-null: null

# ChatBI应用配置
app:
  chatbi:
    # 提示词模板文件
    prompt-template-file: classpath:prompts/text-to-sql-prompt.txt
    # 最大结果行数
    max-result-rows: 1000
    # 默认数据源类型
    default-datasource: doris
    # 会话配置 (现在使用Spring AI管理)
    conversation:
      # 会话过期时间（分钟）- 现在由Spring AI ChatMemory管理
      expiration-minutes: 30
      # 最大历史记录长度 - 现在由MessageWindowChatMemory管理
      max-history-length: 10
      # 启用上下文功能
      context-enabled: true

# 加密配置
encryption:
  # 加密密钥，生产环境应使用更复杂的密钥并通过环境变量或外部配置注入
  key: cmss_ops_monitor_secure_key_2025
