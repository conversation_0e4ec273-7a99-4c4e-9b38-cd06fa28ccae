spring:
  datasource:
    doris:
      url: ******************************************
      username: root
      password: <PERSON>@7831xsda
      driver-class-name: com.mysql.cj.jdbc.Driver
    postgres:
      url: *********************************************************
      username: fms
      password: 5G8EZHvW%Ix6
      driver-class-name: org.postgresql.Driver

# MyBatis-Plus 配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.chinamobile.cmss.ops.fms.chatbi.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    jdbc-type-for-null: null
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 加密配置
encryption:
  # 开发环境加密密钥
  key: cmss_ops_monitor_dev_key_2025

app:
  chatbi:
    prompt-template-file: classpath:prompts/text-to-sql-prompt.txt