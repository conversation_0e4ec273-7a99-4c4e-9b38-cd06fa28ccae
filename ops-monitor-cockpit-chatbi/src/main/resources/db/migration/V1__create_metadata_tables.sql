-- 创建元数据表
CREATE TABLE IF NOT EXISTS metadata_table (
    id SERIAL PRIMARY KEY,
    database_name VARCHAR(100) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    description VARCHAR(255),
    enable SMALLINT DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    create_time TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(50),
    update_by VARCHAR(50),
    CONSTRAINT uk_metadata_table UNIQUE (database_name, table_name)
);

-- 创建元数据列表
CREATE TABLE IF NOT EXISTS metadata_column (
    id SERIAL PRIMARY KEY,
    table_id INTEGER NOT NULL,
    column_name VARCHAR(100) NOT NULL,
    data_type VARCHAR(50) NOT NULL,
    primary_key SMALLINT DEFAULT 0,
    description VARCHAR(255),
    enable SMALLINT DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VA<PERSON><PERSON>R(50),
    update_by VARCHAR(50),
    CONSTRAINT fk_metadata_column_table FOREIGN KEY (table_id) REFERENCES metadata_table(id) ON DELETE CASCADE,
    CONSTRAINT uk_metadata_column UNIQUE (table_id, column_name)
);

-- 创建元数据关系表
CREATE TABLE IF NOT EXISTS metadata_relationship (
    id SERIAL PRIMARY KEY,
    source_table VARCHAR(100) NOT NULL,
    source_column VARCHAR(100) NOT NULL,
    target_table VARCHAR(100) NOT NULL,
    target_column VARCHAR(100) NOT NULL,
    relationship_type VARCHAR(50),
    enable SMALLINT DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(50),
    update_by VARCHAR(50),
    CONSTRAINT uk_metadata_relationship UNIQUE (source_table, source_column, target_table, target_column)
);

-- 创建索引
CREATE INDEX idx_metadata_table_database ON metadata_table(database_name);
CREATE INDEX idx_metadata_table_enable ON metadata_table(enable);
CREATE INDEX idx_metadata_column_table_id ON metadata_column(table_id);
CREATE INDEX idx_metadata_column_enable ON metadata_column(enable);
CREATE INDEX idx_metadata_relationship_source ON metadata_relationship(source_table, source_column);
CREATE INDEX idx_metadata_relationship_target ON metadata_relationship(target_table, target_column);
CREATE INDEX idx_metadata_relationship_enable ON metadata_relationship(enable);
