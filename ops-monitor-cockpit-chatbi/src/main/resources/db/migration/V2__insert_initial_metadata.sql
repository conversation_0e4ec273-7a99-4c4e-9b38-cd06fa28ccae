-- 插入初始元数据表
INSERT INTO metadata_table (database_name, table_name, description, enable, sort_order, create_by)
VALUES
('fms_alarm', 't_dwd_fms_alarm', '告警信息表', 1, 100, 'system')
ON CONFLICT (database_name, table_name) DO NOTHING;

-- 获取表ID
DO $$
DECLARE
    alarm_table_id INTEGER;
BEGIN
    -- 获取表ID
    SELECT id INTO alarm_table_id FROM metadata_table WHERE database_name = 'fms_alarm' AND table_name = 't_dwd_fms_alarm';

    -- 插入告警表列
    IF alarm_table_id IS NOT NULL THEN
        INSERT INTO metadata_column (table_id, column_name, data_type, primary_key, description, enable, sort_order, create_by)
        VALUES
        (alarm_table_id, 'alarm_id', 'VARCHAR(100)', 1, '告警唯一标识', 1, 100, 'system'),
        (alarm_table_id, 'dispatch_process_id', 'VARCHAR(100)', 0, '工单流程Id', 1, 99, 'system'),
        (alarm_table_id, 'alarm_title', 'VARCHAR(200)', 0, '告警标题', 1, 98, 'system'),
        (alarm_table_id, 'alarm_level_label', 'VARCHAR(50)', 0, '告警级别', 1, 97, 'system'),
        (alarm_table_id, 'source_ip', 'VARCHAR(50)', 0, '源告警设备的IP', 1, 96, 'system'),
        (alarm_table_id, 'first_alarm_time', 'VARCHAR(50)', 0, '首次发生时间', 1, 95, 'system'),
        (alarm_table_id, 'last_alarm_time', 'VARCHAR(50)', 0, '最后发生时间', 1, 94, 'system'),
        (alarm_table_id, 'count', 'INT', 0, '发生次数', 1, 93, 'system'),
        (alarm_table_id, 'pool_id', 'VARCHAR(100)', 0, '资源池ID', 1, 92, 'system'),
        (alarm_table_id, 'biz_system_name', 'VARCHAR(100)', 0, '原应用系统名称-业务名称', 1, 91, 'system'),
        (alarm_table_id, 'dispatch_status_label', 'VARCHAR(50)', 0, '派单状态', 1, 90, 'system'),
        (alarm_table_id, 'dispatch_status_id', 'INT', 0, '派单状态ID', 1, 89, 'system'),
        (alarm_table_id, 'parent_id', 'VARCHAR(100)', 0, '告警父对象编码', 1, 88, 'system'),
        (alarm_table_id, 'ticket_no', 'VARCHAR(100)', 0, '工单号', 1, 87, 'system'),
        (alarm_table_id, 'object_id', 'VARCHAR(100)', 0, '告警对象编码', 1, 86, 'system'),
        (alarm_table_id, 'alarm_type', 'VARCHAR(50)', 0, '告警类型', 1, 85, 'system'),
        (alarm_table_id, 'alarm_type_id', 'VARCHAR(50)', 0, '告警类型', 1, 84, 'system'),
        (alarm_table_id, 'device_type', 'VARCHAR(50)', 0, '设备类型', 1, 83, 'system'),
        (alarm_table_id, 'biz_group_name', 'VARCHAR(100)', 0, '原业务分组名称-模块名', 1, 82, 'system'),
        (alarm_table_id, 'alarm_content', 'TEXT', 0, '告警内容', 1, 81, 'system'),
        (alarm_table_id, 'project_flag_id', 'INT', 0, '是否是工程告警 0:否，1:是', 1, 80, 'system'),
        (alarm_table_id, 'project_flag_label', 'VARCHAR(50)', 0, '是否是工程告警 0:否，1:是', 1, 79, 'system'),
        (alarm_table_id, 'insert_time', 'VARCHAR(50)', 0, '告警入库时间', 1, 78, 'system'),
        (alarm_table_id, 'notify_type', 'INT', 0, '清除状态', 1, 77, 'system'),
        (alarm_table_id, 'region_code', 'VARCHAR(50)', 0, '资源池编码', 1, 76, 'system'),
        (alarm_table_id, 'ticket_status', 'VARCHAR(50)', 0, '工单状态', 1, 75, 'system'),
        (alarm_table_id, 'location', 'VARCHAR(200)', 0, '告警定位信息', 1, 74, 'system'),
        (alarm_table_id, 'parent_name', 'VARCHAR(100)', 0, '告警父对象名称', 1, 73, 'system'),
        (alarm_table_id, 'parent_device_type', 'VARCHAR(50)', 0, '告警父对象设备类型', 1, 72, 'system'),
        (alarm_table_id, 'ack_time', 'VARCHAR(50)', 0, '确认时间', 1, 71, 'system'),
        (alarm_table_id, 'ack_user', 'VARCHAR(50)', 0, '确认人', 1, 70, 'system'),
        (alarm_table_id, 'clear_time', 'VARCHAR(50)', 0, '清除时间', 1, 69, 'system'),
        (alarm_table_id, 'dispatch_time', 'VARCHAR(50)', 0, '派单时间', 1, 68, 'system'),
        (alarm_table_id, 'dispatching_time', 'DATETIME', 0, '实际派单时间', 1, 67, 'system'),
        (alarm_table_id, 'clear_user', 'VARCHAR(50)', 0, '清除人', 1, 66, 'system'),
        (alarm_table_id, 'ack_status_label', 'VARCHAR(50)', 0, '告警确认状态', 1, 65, 'system'),
        (alarm_table_id, 'notify_type_label', 'VARCHAR(50)', 0, '是否清除标识', 1, 64, 'system'),
        (alarm_table_id, 'object_name', 'VARCHAR(100)', 0, '资源名称', 1, 63, 'system'),
        (alarm_table_id, 'region_name', 'VARCHAR(100)', 0, '资源池名称', 1, 62, 'system'),
        (alarm_table_id, 'device_name', 'VARCHAR(100)', 0, '设备名称', 1, 61, 'system'),
        (alarm_table_id, 'device_code', 'VARCHAR(100)', 0, '设备编码', 1, 60, 'system'),
        (alarm_table_id, 'device_vendor', 'VARCHAR(100)', 0, '设备厂商', 1, 59, 'system'),
        (alarm_table_id, 'device_model', 'VARCHAR(100)', 0, '设备型号', 1, 58, 'system'),
        (alarm_table_id, 'alarm_impact', 'TEXT', 0, '告警的影响描述', 1, 57, 'system'),
        (alarm_table_id, 'major', 'VARCHAR(50)', 0, '所属专业', 1, 56, 'system'),
        (alarm_table_id, 'component', 'VARCHAR(50)', 0, '所属组件', 1, 55, 'system'),
        (alarm_table_id, 'monitor_index', 'VARCHAR(100)', 0, '监控指标', 1, 54, 'system'),
        (alarm_table_id, 'alarm_explain', 'TEXT', 0, '告警解释', 1, 53, 'system'),
        (alarm_table_id, 'alarm_influence', 'TEXT', 0, '告警对业务的影响', 1, 52, 'system')
        ON CONFLICT (table_id, column_name) DO NOTHING;
    END IF;
END $$;

-- 不需要插入表关系，因为只有一个表
