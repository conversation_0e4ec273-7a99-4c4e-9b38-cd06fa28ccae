-- 创建数据源配置表
CREATE TABLE IF NOT EXISTS data_source_config (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    jdbc_url VARCHAR(500) NOT NULL,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(100) NOT NULL,
    driver_class_name VARCHAR(200) NOT NULL,
    database_name VARCHAR(100) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    enabled BOOLEAN DEFAULT TRUE,
    description TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    max_pool_size INTEGER DEFAULT 10,
    min_idle INTEGER DEFAULT 5,
    connection_timeout BIGINT DEFAULT 30000,
    idle_timeout BIGINT DEFAULT 600000,
    max_lifetime BIGINT DEFAULT 1800000,
    properties TEXT,
    CONSTRAINT uk_data_source_name UNIQUE (name)
);

-- 创建索引
CREATE INDEX idx_data_source_type ON data_source_config (type);
CREATE INDEX idx_data_source_enabled ON data_source_config (enabled);

-- 插入默认数据源配置
INSERT INTO data_source_config (
    name, type, jdbc_url, username, password, 
    driver_class_name, database_name, is_default, enabled, description
) VALUES (
    'Doris数据源', 'doris', '******************************************', 
    'root', 'Doris@7831xsda', 'com.mysql.cj.jdbc.Driver', 
    'fms_alarm', TRUE, TRUE, 'Doris默认数据源'
);

-- 插入PostgreSQL数据源配置
INSERT INTO data_source_config (
    name, type, jdbc_url, username, password, 
    driver_class_name, database_name, is_default, enabled, description
) VALUES (
    'PostgreSQL数据源', 'postgresql', '*********************************************************', 
    'fms', '5G8EZHvW%Ix6', 'org.postgresql.Driver', 
    'ops_monitor_cockpit', FALSE, TRUE, 'PostgreSQL数据源'
);
