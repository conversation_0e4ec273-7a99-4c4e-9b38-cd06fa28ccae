-- 添加数据源ID字段到元数据表
ALTER TABLE metadata_table
    ADD COLUMN data_source_id BIGINT;

-- 添加数据源ID字段到元数据关系表
ALTER TABLE metadata_relationship
    ADD COLUMN data_source_id BIGINT;

-- 创建索引
CREATE INDEX idx_metadata_table_data_source_id ON metadata_table (data_source_id);
CREATE INDEX idx_metadata_relationship_data_source_id ON metadata_relationship (data_source_id);

-- 添加外键约束
ALTER TABLE metadata_table
    ADD CONSTRAINT fk_metadata_table_data_source
    FOREIGN KEY (data_source_id)
    REFERENCES data_source_config(id)
    ON DELETE SET NULL;

ALTER TABLE metadata_relationship
    ADD CONSTRAINT fk_metadata_relationship_data_source
    FOREIGN KEY (data_source_id)
    REFERENCES data_source_config(id)
    ON DELETE SET NULL;

-- 更新现有数据，将默认数据源ID设置到现有记录中
DO $$
DECLARE
    default_data_source_id BIGINT;
BEGIN
    -- 获取默认数据源ID
    SELECT id INTO default_data_source_id FROM data_source_config WHERE is_default = TRUE LIMIT 1;
    
    -- 如果没有默认数据源，则获取第一个数据源
    IF default_data_source_id IS NULL THEN
        SELECT id INTO default_data_source_id FROM data_source_config LIMIT 1;
    END IF;
    
    -- 更新元数据表
    IF default_data_source_id IS NOT NULL THEN
        UPDATE metadata_table SET data_source_id = default_data_source_id WHERE data_source_id IS NULL;
        UPDATE metadata_relationship SET data_source_id = default_data_source_id WHERE data_source_id IS NULL;
    END IF;
END $$;

-- 修改元数据表的唯一约束
ALTER TABLE metadata_table DROP CONSTRAINT uk_metadata_table;
ALTER TABLE metadata_table ADD CONSTRAINT uk_metadata_table UNIQUE (data_source_id, database_name, table_name);
