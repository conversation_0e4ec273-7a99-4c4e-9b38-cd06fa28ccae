-- Spring AI 1.0.0 集成架构的完整数据库schema
-- 本文件整合了ChatBI模块所需的所有表结构，移除了已废弃的自定义会话管理表

-- ============================================================================
-- 元数据管理表 (Metadata Management Tables)
-- ============================================================================

-- 确保元数据表结构是最新的
-- metadata_table 表已在 V1 中创建，这里确保包含 data_source_id 字段
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'metadata_table' AND column_name = 'data_source_id') THEN
        ALTER TABLE metadata_table ADD COLUMN data_source_id BIGINT;
        CREATE INDEX IF NOT EXISTS idx_metadata_table_data_source_id ON metadata_table(data_source_id);
    END IF;
END $$;

-- 确保元数据列表包含中文名字段
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'metadata_column' AND column_name = 'chinese_name') THEN
        ALTER TABLE metadata_column ADD COLUMN chinese_name VARCHAR(255);
    END IF;
END $$;

-- 确保数据源配置表存在
CREATE TABLE IF NOT EXISTS data_source_config (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    type VARCHAR(50) NOT NULL,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL,
    database_name VARCHAR(100) NOT NULL,
    username VARCHAR(100) NOT NULL,
    password_encrypted TEXT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    enable SMALLINT DEFAULT 1,
    description VARCHAR(500),
    connection_params TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(50),
    update_by VARCHAR(50)
);

-- 创建数据源配置表的索引
CREATE INDEX IF NOT EXISTS idx_data_source_config_type ON data_source_config(type);
CREATE INDEX IF NOT EXISTS idx_data_source_config_enable ON data_source_config(enable);
CREATE INDEX IF NOT EXISTS idx_data_source_config_default ON data_source_config(is_default);

-- ============================================================================
-- Spring AI 会话管理说明
-- ============================================================================

-- Spring AI 1.0.0 会自动创建以下表用于会话管理：
-- - spring_ai_chat_memory: 存储会话消息和上下文
-- 
-- 不再需要以下自定义表：
-- - conversation (已删除)
-- - conversation_exchange (已删除)
-- - query_history (已删除)

-- ============================================================================
-- 表注释和约束
-- ============================================================================

-- 添加表和字段注释
COMMENT ON TABLE metadata_table IS '元数据表信息';
COMMENT ON COLUMN metadata_table.data_source_id IS '关联的数据源ID';

COMMENT ON TABLE metadata_column IS '元数据列信息';
COMMENT ON COLUMN metadata_column.chinese_name IS '列的中文名称';

COMMENT ON TABLE data_source_config IS '数据源配置表';
COMMENT ON COLUMN data_source_config.password_encrypted IS '加密后的密码';
COMMENT ON COLUMN data_source_config.is_default IS '是否为默认数据源';
COMMENT ON COLUMN data_source_config.connection_params IS '额外的连接参数(JSON格式)';

-- 确保只有一个默认数据源
CREATE OR REPLACE FUNCTION ensure_single_default_datasource()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_default = TRUE THEN
        UPDATE data_source_config SET is_default = FALSE WHERE id != NEW.id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_ensure_single_default_datasource ON data_source_config;
CREATE TRIGGER trigger_ensure_single_default_datasource
    BEFORE INSERT OR UPDATE ON data_source_config
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_default_datasource();
