/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.util;

import com.chinamobile.cmss.ops.fms.chatbi.entity.DataSourceConfigEntity;
import com.chinamobile.cmss.ops.fms.chatbi.mapper.DataSourceConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数据源凭证加密工具
 * 用于在应用启动时加密数据库中已存在的明文凭证
 */
@Component
@Slf4j
public class DataSourceCredentialEncryptor implements CommandLineRunner {
    
    private final DataSourceConfigMapper dataSourceConfigMapper;
    
    private final EncryptionUtil encryptionUtil;
    
    @Autowired
    public DataSourceCredentialEncryptor(DataSourceConfigMapper dataSourceConfigMapper, EncryptionUtil encryptionUtil) {
        this.dataSourceConfigMapper = dataSourceConfigMapper;
        this.encryptionUtil = encryptionUtil;
    }
    
    @Override
    public void run(String... args) {
        log.info("开始检查数据源凭证加密状态...");
        encryptExistingCredentials();
        log.info("数据源凭证加密检查完成");
    }
    
    /**
     * 加密已存在的数据源凭证
     */
    @Transactional(rollbackFor = Exception.class)
    public void encryptExistingCredentials() {
        try {
            // 获取所有数据源配置
            List<DataSourceConfigEntity> allConfigs = dataSourceConfigMapper.selectList(null);
            if (allConfigs == null) {
                log.warn("No data source configurations found in database");
                return;
            }
            int encryptedCount = 0;
            
            for (DataSourceConfigEntity config : allConfigs) {
                boolean updated = false;
                
                // 检查并加密用户名
                if (config.getUsername() != null && !encryptionUtil.isEncrypted(config.getUsername())) {
                    config.setUsername(encryptionUtil.encrypt(config.getUsername()));
                    updated = true;
                }
                
                // 检查并加密密码
                if (config.getPassword() != null && !encryptionUtil.isEncrypted(config.getPassword())) {
                    config.setPassword(encryptionUtil.encrypt(config.getPassword()));
                    updated = true;
                }
                
                // 如果有更新，保存到数据库
                if (updated) {
                    dataSourceConfigMapper.updateDataSourceConfig(config);
                    encryptedCount++;
                }
            }
            
            log.info("已加密 {} 个数据源的凭证", encryptedCount);
        } catch (Exception e) {
            log.error("加密数据源凭证时发生错误", e);
            throw e;
        }
    }
}
