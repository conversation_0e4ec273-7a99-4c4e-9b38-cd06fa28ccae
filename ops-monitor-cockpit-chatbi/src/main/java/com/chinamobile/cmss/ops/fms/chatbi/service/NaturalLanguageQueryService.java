/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.cmss.ops.fms.chatbi.model.DatabaseMetadata;
import com.chinamobile.cmss.ops.fms.chatbi.model.NlQueryRequest;
import com.chinamobile.cmss.ops.fms.chatbi.model.NlQueryResponse;

/**
 * 自然语言查询服务接口
 */
public interface NaturalLanguageQueryService {
    
    /**
     * 处理自然语言查询
     *
     * @param request 查询请求
     * @return 查询响应
     */
    NlQueryResponse processQuery(NlQueryRequest request);
    
    /**
     * 清除查询缓存
     */
    void clearCache();
    
    /**
     * 获取数据库元数据
     *
     * @return 数据库元数据
     */
    DatabaseMetadata getDatabaseMetadata();
    
    /**
     * 获取指定数据源ID的数据库元数据
     *
     * @param dataSourceId 数据源ID
     * @return 数据库元数据
     */
    DatabaseMetadata getDatabaseMetadata(Long dataSourceId);
    
    /**
     * 分页获取数据库元数据
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 分页数据库元数据
     */
    Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(int pageNum, int pageSize);
    
    /**
     * 分页获取指定数据源ID的数据库元数据
     *
     * @param dataSourceId 数据源ID
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @return 分页数据库元数据
     */
    Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(Long dataSourceId, int pageNum, int pageSize);
    
    /**
     * 处理用户反馈
     *
     * @param queryId       查询ID
     * @param feedbackValue 反馈值
     * @param feedbackText  反馈文本
     * @return 是否成功处理反馈
     */
    boolean submitFeedback(String queryId, String feedbackValue, String feedbackText);
    
    /**
     * 在指定会话中进行查询（使用Spring AI会话管理）
     *
     * @param request 查询请求
     * @param conversationId 会话ID
     * @return 查询响应
     */
    NlQueryResponse processConversationQuery(NlQueryRequest request, String conversationId);
}
