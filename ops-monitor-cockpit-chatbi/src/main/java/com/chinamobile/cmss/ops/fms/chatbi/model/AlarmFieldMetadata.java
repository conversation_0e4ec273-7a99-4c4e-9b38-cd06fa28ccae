/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警字段元数据
 * 用于从配置文件中加载字段信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmFieldMetadata {
    
    /**
     * 字段名
     */
    private String fieldName;
    
    /**
     * 数据类型
     */
    private String dataType;
    
    /**
     * 是否为主键
     */
    private boolean primaryKey;
    
    /**
     * 字段描述（中文）
     */
    private String description;
}
