/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.controller;

import com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataColumn;
import com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataRelationship;
import com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataTable;
import com.chinamobile.cmss.ops.fms.chatbi.service.MetadataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 元数据管理控制器
 */
@RestController
@RequestMapping("/api/metadata-management")
@Slf4j
public class MetadataManagementController {
    
    @Autowired
    private MetadataService metadataService;
    
    /**
     * 获取元数据表列表
     *
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @param databaseName 数据库名
     * @param dataSourceId 数据源ID
     * @return 元数据表列表
     */
    @GetMapping("/tables")
    public Map<String, Object> getMetadataTables(
                                                 @RequestParam(defaultValue = "static") int pageNum,
                                                 @RequestParam(defaultValue = "10") int pageSize,
                                                 @RequestParam(required = false) String databaseName,
                                                 @RequestParam(required = false) Long dataSourceId) {
        if (dataSourceId != null) {
            return metadataService.getMetadataTablesByDataSource(dataSourceId, pageNum, pageSize, databaseName);
        } else {
            return metadataService.getMetadataTables(pageNum, pageSize, databaseName);
        }
    }
    
    /**
     * 获取元数据表详情
     *
     * @param id 元数据表ID
     * @return 元数据表详情
     */
    @GetMapping("/tables/{id}")
    public Map<String, Object> getMetadataTable(@PathVariable Long id) {
        return metadataService.getMetadataTable(id);
    }
    
    /**
     * 创建元数据表
     *
     * @param table 元数据表
     * @return 创建结果
     */
    @PostMapping("/tables")
    public MetadataTable createMetadataTable(@RequestBody MetadataTable table) {
        return metadataService.createMetadataTable(table);
    }
    
    /**
     * 更新元数据表
     *
     * @param id    元数据表ID
     * @param table 元数据表
     * @return 更新结果
     */
    @PutMapping("/tables/{id}")
    public MetadataTable updateMetadataTable(@PathVariable Long id, @RequestBody MetadataTable table) {
        return metadataService.updateMetadataTable(id, table);
    }
    
    /**
     * 删除元数据表
     *
     * @param id 元数据表ID
     * @return 删除结果
     */
    @DeleteMapping("/tables/{id}")
    public String deleteMetadataTable(@PathVariable Long id) {
        return metadataService.deleteMetadataTable(id);
    }
    
    /**
     * 获取元数据列列表
     *
     * @param tableId 表ID
     * @return 元数据列列表
     */
    @GetMapping("/columns")
    public Map<String, Object> getMetadataColumns(@RequestParam Long tableId) {
        return metadataService.getMetadataColumns(tableId);
    }
    
    /**
     * 创建元数据列
     *
     * @param column 元数据列
     * @return 创建结果
     */
    @PostMapping("/columns")
    public MetadataColumn createMetadataColumn(@RequestBody MetadataColumn column) {
        return metadataService.createMetadataColumn(column);
    }
    
    /**
     * 更新元数据列
     *
     * @param id     元数据列ID
     * @param column 元数据列
     * @return 更新结果
     */
    @PutMapping("/columns/{id}")
    public MetadataColumn updateMetadataColumn(@PathVariable Long id, @RequestBody MetadataColumn column) {
        return metadataService.updateMetadataColumn(id, column);
    }
    
    /**
     * 删除元数据列
     *
     * @param id 元数据列ID
     * @return 删除结果
     */
    @DeleteMapping("/columns/{id}")
    public String deleteMetadataColumn(@PathVariable Long id) {
        return metadataService.deleteMetadataColumn(id);
    }
    
    /**
     * 获取元数据关系列表
     *
     * @return 元数据关系列表
     */
    @GetMapping("/relationships")
    public List<MetadataRelationship> getMetadataRelationships() {
        return metadataService.getMetadataRelationships();
    }
    
    /**
     * 创建元数据关系
     *
     * @param relationship 元数据关系
     * @return 创建结果
     */
    @PostMapping("/relationships")
    public MetadataRelationship createMetadataRelationship(@RequestBody MetadataRelationship relationship) {
        return metadataService.createMetadataRelationship(relationship);
    }
    
    /**
     * 更新元数据关系
     *
     * @param id           元数据关系ID
     * @param relationship 元数据关系
     * @return 更新结果
     */
    @PutMapping("/relationships/{id}")
    public MetadataRelationship updateMetadataRelationship(@PathVariable Long id, @RequestBody MetadataRelationship relationship) {
        return metadataService.updateMetadataRelationship(id, relationship);
    }
    
    /**
     * 删除元数据关系
     *
     * @param id 元数据关系ID
     * @return 删除结果
     */
    @DeleteMapping("/relationships/{id}")
    public String deleteMetadataRelationship(@PathVariable Long id) {
        return metadataService.deleteMetadataRelationship(id);
    }
    
    /**
     * 刷新元数据缓存
     *
     * @return 刷新结果
     */
    @PostMapping("/refresh-cache")
    public String refreshMetadataCache() {
        return metadataService.refreshMetadataCache();
    }
}
