/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.cmss.ops.fms.chatbi.dto.DataSourceConfigDTO;
import com.chinamobile.cmss.ops.fms.chatbi.entity.DataSourceConfigEntity;
import com.chinamobile.cmss.ops.fms.chatbi.model.DatabaseMetadata;

import java.util.List;
import java.util.Map;

/**
 * 统一的数据源服务接口
 * 合并了原来的DatabaseConnectionService、JdbcDataSourceService、DynamicDatabaseConnectionServiceFactory和DataSourceConfigService的功能
 * 提供数据源配置管理、连接池管理、SQL执行和元数据获取的完整统一接口
 */
public interface DataSourceService {
    
    // ============================================================================
    // 数据源基本信息
    // ============================================================================
    
    /**
     * 获取数据源名称
     *
     * @param dataSourceId 数据源ID
     * @return 数据源名称
     */
    String getDataSourceName(Long dataSourceId);
    
    /**
     * 获取数据源类型
     *
     * @param dataSourceId 数据源ID
     * @return 数据源类型
     */
    String getDataSourceType(Long dataSourceId);
    
    /**
     * 获取所有数据源信息（用于下拉选择）
     *
     * @return 数据源信息列表
     */
    List<Map<String, String>> getAllDataSources();
    
    // ============================================================================
    // SQL执行
    // ============================================================================
    
    /**
     * 使用默认数据源执行SQL查询
     *
     * @param sql SQL查询语句
     * @return 查询结果
     */
    List<Map<String, Object>> executeQuery(String sql);
    
    /**
     * 使用指定数据源执行SQL查询
     *
     * @param dataSourceId 数据源ID
     * @param sql          SQL查询语句
     * @return 查询结果
     */
    List<Map<String, Object>> executeQuery(Long dataSourceId, String sql);
    
    /**
     * 使用指定类型的数据源执行SQL查询
     *
     * @param dataSourceType 数据源类型
     * @param sql            SQL查询语句
     * @return 查询结果
     */
    List<Map<String, Object>> executeQueryByType(String dataSourceType, String sql);
    
    // ============================================================================
    // 元数据管理
    // ============================================================================
    
    /**
     * 获取默认数据源的数据库元数据
     *
     * @return 数据库元数据
     */
    DatabaseMetadata getDatabaseMetadata();
    
    /**
     * 获取指定数据源的数据库元数据
     *
     * @param dataSourceId 数据源ID
     * @return 数据库元数据
     */
    DatabaseMetadata getDatabaseMetadata(Long dataSourceId);
    
    /**
     * 分页获取默认数据源的数据库元数据
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 分页数据库元数据
     */
    Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(int pageNum, int pageSize);
    
    /**
     * 分页获取指定数据源的数据库元数据
     *
     * @param dataSourceId 数据源ID
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @return 分页数据库元数据
     */
    Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(Long dataSourceId, int pageNum, int pageSize);
    
    // ============================================================================
    // 连接测试
    // ============================================================================
    
    /**
     * 测试数据源连接
     *
     * @param config 数据源配置
     * @return 是否连接成功
     */
    boolean testConnection(DataSourceConfigEntity config);
    
    /**
     * 测试数据源连接
     *
     * @param jdbcUrl         JDBC URL
     * @param username        用户名
     * @param password        密码
     * @param driverClassName 驱动类名
     * @return 是否连接成功
     */
    boolean testConnection(String jdbcUrl, String username, String password, String driverClassName);
    
    // ============================================================================
    // 数据源配置管理 (合并自DataSourceConfigService)
    // ============================================================================

    /**
     * 获取所有启用的数据源配置
     *
     * @return 数据源配置列表
     */
    List<DataSourceConfigEntity> getAllEnabledConfigs();

    /**
     * 获取默认数据源配置
     *
     * @return 默认数据源配置
     */
    DataSourceConfigEntity getDefaultConfig();

    /**
     * 根据类型获取数据源配置
     *
     * @param type 数据源类型
     * @return 数据源配置
     */
    DataSourceConfigEntity getConfigByType(String type);

    /**
     * 根据ID获取数据源配置
     *
     * @param id 数据源ID
     * @return 数据源配置
     */
    DataSourceConfigEntity getConfigById(Long id);

    /**
     * 根据名称获取数据源配置
     *
     * @param name 数据源名称
     * @return 数据源配置
     */
    DataSourceConfigEntity getConfigByName(String name);

    /**
     * 分页查询数据源配置
     *
     * @param page     页码
     * @param pageSize 每页大小
     * @return 分页数据源配置
     */
    Page<DataSourceConfigEntity> getConfigPage(int page, int pageSize);

    /**
     * 创建数据源配置
     *
     * @param config 数据源配置
     * @return 创建的数据源配置
     */
    DataSourceConfigEntity createConfig(DataSourceConfigEntity config);

    /**
     * 更新数据源配置
     *
     * @param config 数据源配置
     * @return 更新的数据源配置
     */
    DataSourceConfigEntity updateConfig(DataSourceConfigEntity config);

    /**
     * 删除数据源配置
     *
     * @param id 数据源ID
     * @return 是否成功删除
     */
    boolean deleteConfig(Long id);

    /**
     * 设置默认数据源
     *
     * @param id 数据源ID
     * @return 是否成功设置
     */
    boolean setDefaultConfig(Long id);

    /**
     * 启用或禁用数据源
     *
     * @param id      数据源ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean setConfigEnabled(Long id, boolean enabled);

    /**
     * 获取数据源配置 DTO（敏感字段保持加密状态）
     *
     * @param id 数据源ID
     * @return 数据源配置 DTO
     */
    DataSourceConfigDTO getEncryptedConfigById(Long id);

    /**
     * 分页查询数据源配置 DTO（敏感字段保持加密状态）
     *
     * @param page     页码
     * @param pageSize 每页大小
     * @return 分页数据源配置 DTO
     */
    Page<DataSourceConfigDTO> getEncryptedConfigPage(int page, int pageSize);

    /**
     * 获取所有启用的数据源配置 DTO（敏感字段保持加密状态）
     *
     * @return 数据源配置 DTO 列表
     */
    List<DataSourceConfigDTO> getAllEnabledEncryptedConfigs();

    // ============================================================================
    // 缓存管理
    // ============================================================================

    /**
     * 清除指定数据源的缓存（包括连接池和元数据缓存）
     *
     * @param dataSourceId 数据源ID
     */
    void clearCache(Long dataSourceId);

    /**
     * 清除所有数据源的缓存
     */
    void clearAllCache();
}
