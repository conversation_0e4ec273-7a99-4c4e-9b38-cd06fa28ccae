/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chinamobile.cmss.ops.fms.chatbi.entity.DataSourceConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据源配置Mapper
 */
@Mapper
public interface DataSourceConfigMapper extends BaseMapper<DataSourceConfigEntity> {
    
    /**
     * 查询所有启用的数据源配置
     *
     * @return 数据源配置列表
     */
    List<DataSourceConfigEntity> selectAllEnabled();
    
    /**
     * 查询默认数据源配置
     *
     * @return 默认数据源配置
     */
    DataSourceConfigEntity selectDefault();
    
    /**
     * 根据类型查询数据源配置
     *
     * @param type 数据源类型
     * @return 数据源配置
     */
    DataSourceConfigEntity selectByType(@Param("type") String type);
    
    /**
     * 根据名称查询数据源配置
     *
     * @param name 数据源名称
     * @return 数据源配置
     */
    DataSourceConfigEntity selectByName(@Param("name") String name);
    
    /**
     * 清除所有默认标记
     *
     * @return 影响的行数
     */
    int clearAllDefault();
    
    /**
     * 设置默认数据源
     *
     * @param id 数据源ID
     * @return 影响的行数
     */
    int setDefault(@Param("id") Long id);

    /**
     * 设置数据源启用状态
     *
     * @param id 数据源ID
     * @param enabled 是否启用
     * @return 影响的行数
     */
    int setEnabled(@Param("id") Long id, @Param("enabled") boolean enabled);

    /**
     * 更新数据源配置
     *
     * @param config 数据源配置
     * @return 影响的行数
     */
    int updateDataSourceConfig(@Param("config") DataSourceConfigEntity config);
}
