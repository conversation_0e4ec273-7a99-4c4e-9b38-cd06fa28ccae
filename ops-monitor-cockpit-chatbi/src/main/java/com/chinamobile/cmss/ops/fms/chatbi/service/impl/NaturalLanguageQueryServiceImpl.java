/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.cmss.ops.fms.chatbi.model.DatabaseMetadata;
import com.chinamobile.cmss.ops.fms.chatbi.model.NlQueryRequest;
import com.chinamobile.cmss.ops.fms.chatbi.model.NlQueryResponse;
import com.chinamobile.cmss.ops.fms.chatbi.model.TextToSqlResult;
import com.chinamobile.cmss.ops.fms.chatbi.service.DataSourceService;
import com.chinamobile.cmss.ops.fms.chatbi.service.NaturalLanguageQueryService;
import com.chinamobile.cmss.ops.fms.chatbi.service.TextToSqlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 自然语言查询服务实现类
 */
@Service
@Slf4j
public class NaturalLanguageQueryServiceImpl implements NaturalLanguageQueryService {
    
    private final TextToSqlService textToSqlService;
    
    private final DataSourceService dataSourceService;
    
    @Value("${app.chatbi.max-result-rows:1000}")
    private int maxResultRows;
    
    @Autowired
    public NaturalLanguageQueryServiceImpl(TextToSqlService textToSqlService,
                                           DataSourceService dataSourceService) {
        this.textToSqlService = textToSqlService;
        this.dataSourceService = dataSourceService;
    }
    
    /**
     * 处理自然语言查询
     *
     * @param request 查询请求
     * @return 查询响应
     */
    @Override
    public NlQueryResponse processQuery(NlQueryRequest request) {
        return processQueryInternal(request, null);
    }

    /**
     * 通用的查询处理方法
     *
     * @param request 查询请求
     * @param conversationId 会话ID，如果为null则表示非会话查询
     * @return 查询响应
     */
    private NlQueryResponse processQueryInternal(NlQueryRequest request, String conversationId) {
        long startTime = System.currentTimeMillis();
        boolean isConversationQuery = conversationId != null;

        try {
            // 获取数据源信息
            String dataSourceName;
            String dataSourceType;
            Long actualDataSourceId;

            if (request.getDataSourceId() != null) {
                // 优先使用数据源ID
                actualDataSourceId = request.getDataSourceId();
                dataSourceName = dataSourceService.getDataSourceName(actualDataSourceId);
                dataSourceType = dataSourceService.getDataSourceType(actualDataSourceId);
            } else {
                // 使用默认数据源
                var defaultConfig = dataSourceService.getDefaultConfig();
                if (defaultConfig == null) {
                    throw new RuntimeException("未找到默认数据源配置");
                }
                actualDataSourceId = defaultConfig.getId();
                dataSourceName = defaultConfig.getName();
                dataSourceType = defaultConfig.getType();
            }

            if (isConversationQuery) {
                log.info("在会话 {} 中使用数据源: {}, 类型: {}", conversationId, dataSourceName, dataSourceType);
            } else {
                log.info("使用数据源: {}, 类型: {}", dataSourceName, dataSourceType);
            }

            // 获取数据库元数据用于SQL生成
            DatabaseMetadata metadata = dataSourceService.getDatabaseMetadata(actualDataSourceId);

            // 生成SQL查询 - 直接传递查询文本和元数据
            TextToSqlResult sqlResult = textToSqlService.generateSql(request.getQuery(), metadata);

            // 如果SQL生成失败，返回错误
            if (!sqlResult.isSuccess()) {
                NlQueryResponse.NlQueryResponseBuilder responseBuilder = NlQueryResponse.builder()
                        .id(UUID.randomUUID().toString())
                        .query(request.getQuery())
                        .success(false)
                        .errorMessage(sqlResult.getErrorMessage())
                        .executionTimeMs(System.currentTimeMillis() - startTime);

                if (isConversationQuery) {
                    responseBuilder.conversationId(conversationId);
                }

                return responseBuilder.build();
            }

            if (isConversationQuery) {
                log.info("生成的SQL: {}", sqlResult.getSql());
            }

            // 执行SQL查询
            List<Map<String, Object>> results = dataSourceService.executeQuery(actualDataSourceId, sqlResult.getSql());

            if (isConversationQuery) {
                log.info("查询结果行数: {}", results.size());
            }

            // 限制结果行数
            if (results.size() > maxResultRows) {
                results = results.subList(0, maxResultRows);
            }

            // 提取列信息
            List<NlQueryResponse.ColumnInfo> columns = extractColumnInfo(results);

            // 推荐可视化类型
            String suggestedVisualization = suggestVisualization(request.getQuery(), sqlResult.getSql(), results);

            // 构建响应
            NlQueryResponse.NlQueryResponseBuilder responseBuilder = NlQueryResponse.builder()
                    .id(UUID.randomUUID().toString())
                    .query(request.getQuery())
                    .generatedSql(sqlResult.getSql())
                    .results(results)
                    .columns(columns)
                    .suggestedVisualization(suggestedVisualization)
                    .success(true)
                    .executionTimeMs(System.currentTimeMillis() - startTime)
                    .dataSource(dataSourceName)
                    .hasContext(isConversationQuery);

            if (isConversationQuery) {
                responseBuilder.conversationId(conversationId);
                log.debug("Spring AI会话 {} 上下文已自动更新", conversationId);
            }

            return responseBuilder.build();

        } catch (Exception e) {
            String errorMsg = isConversationQuery ?
                    String.format("在会话 %s 中处理查询时发生错误", conversationId) :
                    "处理自然语言查询时发生错误";
            log.error(errorMsg, e);

            NlQueryResponse.NlQueryResponseBuilder responseBuilder = NlQueryResponse.builder()
                    .id(UUID.randomUUID().toString())
                    .query(request.getQuery())
                    .success(false)
                    .errorMessage("处理查询时发生错误: " + e.getMessage())
                    .executionTimeMs(System.currentTimeMillis() - startTime);

            if (isConversationQuery) {
                responseBuilder.conversationId(conversationId);
            }

            return responseBuilder.build();
        }
    }

    /**
     * 提取列信息
     *
     * @param results 查询结果
     * @return 列信息列表
     */
    private List<NlQueryResponse.ColumnInfo> extractColumnInfo(List<Map<String, Object>> results) {
        if (results == null || results.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 获取数据库元数据，用于查找字段的中文描述
        DatabaseMetadata metadata = dataSourceService.getDatabaseMetadata();
        Map<String, String> columnChineseNames = new HashMap<>();
        
        // 将所有列的描述收集到一个映射中
        if (metadata != null && metadata.getTables() != null) {
            for (DatabaseMetadata.TableMetadata table : metadata.getTables()) {
                if (table.getColumns() != null) {
                    for (DatabaseMetadata.ColumnMetadata column : table.getColumns()) {
                        columnChineseNames.put(column.getColumnName().toLowerCase(), column.getChineseName());
                    }
                }
            }
        }
        
        // 从第一行提取列信息
        Map<String, Object> firstRow = results.get(0);
        
        return firstRow.keySet().stream()
                .map(key -> {
                    Object value = firstRow.get(key);
                    String type = (value != null) ? value.getClass().getSimpleName() : "String";
                    
                    // 使用中文描述作为标签，如果没有则使用格式化的列名
                    String chineseNames = columnChineseNames.get(key.toLowerCase());
                    String label = StringUtils.hasText(chineseNames) ? chineseNames : formatColumnLabel(key);
                    
                    return NlQueryResponse.ColumnInfo.builder()
                            .name(key)
                            .label(label)
                            .type(type)
                            .build();
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 格式化列标签
     *
     * @param columnName 列名
     * @return 格式化后的列标签
     */
    private String formatColumnLabel(String columnName) {
        // 将下划线转换为空格，并将首字母大写
        String[] parts = columnName.split("_");
        StringBuilder label = new StringBuilder();
        
        for (String part : parts) {
            if (!part.isEmpty()) {
                label.append(Character.toUpperCase(part.charAt(0)))
                        .append(part.substring(1))
                        .append(" ");
            }
        }
        
        return label.toString().trim();
    }
    
    /**
     * 推荐可视化类型
     *
     * @param query   自然语言查询
     * @param sql     SQL查询
     * @param results 查询结果
     * @return 推荐的可视化类型
     */
    private String suggestVisualization(String query, String sql, List<Map<String, Object>> results) {
        if (results == null || results.isEmpty()) {
            return "table";
        }
        
        // 检查是否包含时间/日期列
        boolean hasDateColumn = false;
        boolean hasNumericColumn = false;
        boolean hasCategoryColumn = false;
        
        Map<String, Object> firstRow = results.get(0);
        for (Map.Entry<String, Object> entry : firstRow.entrySet()) {
            String columnName = entry.getKey().toLowerCase();
            Object value = entry.getValue();
            
            // 检查列名是否包含日期相关词
            if (columnName.contains("date") || columnName.contains("time") || columnName.contains("year") ||
                    columnName.contains("month") || columnName.contains("day")) {
                hasDateColumn = true;
            }
            
            // 检查是否有数值列
            if (value instanceof Number) {
                hasNumericColumn = true;
            }
            
            // 检查是否有分类列
            if (value instanceof String &&
                    (columnName.contains("category") || columnName.contains("type") ||
                            columnName.contains("name") || columnName.contains("status"))) {
                hasCategoryColumn = true;
            }
        }
        
        // 根据查询和结果特征推荐可视化类型
        String queryLower = query.toLowerCase();
        String sqlLower = sql.toLowerCase();
        
        // 趋势分析通常使用折线图
        if ((queryLower.contains("趋势") || queryLower.contains("变化") || queryLower.contains("增长") ||
                sqlLower.contains("order by") && hasDateColumn) && hasNumericColumn) {
            return "line";
        }
        
        // 比较分析通常使用柱状图
        if ((queryLower.contains("比较") || queryLower.contains("对比") || queryLower.contains("排名") ||
                queryLower.contains("最高") || queryLower.contains("最低")) && hasNumericColumn) {
            return "bar";
        }
        // 检查结果集大小
        int rowCount = results.size();
        // 分布分析通常使用饼图
        if ((queryLower.contains("占比") || queryLower.contains("分布") || queryLower.contains("构成") ||
                queryLower.contains("百分比")) && hasNumericColumn && hasCategoryColumn && rowCount <= 10) {
            return "pie";
        }
        
        // 默认使用表格
        return "table";
    }
    
    /**
     * 清除查询缓存
     */
    @Override
    public void clearCache() {
        textToSqlService.clearCache();
        log.info("清除所有缓存完成");
    }
    
    // ============================================================================
    // 元数据获取方法 - 直接委托给DataSourceService
    // ============================================================================

    @Override
    public DatabaseMetadata getDatabaseMetadata() {
        return dataSourceService.getDatabaseMetadata();
    }

    @Override
    public DatabaseMetadata getDatabaseMetadata(Long dataSourceId) {
        return dataSourceId != null ?
                dataSourceService.getDatabaseMetadata(dataSourceId) :
                dataSourceService.getDatabaseMetadata();
    }

    @Override
    public Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(int pageNum, int pageSize) {
        return dataSourceService.getDatabaseMetadataPage(pageNum, pageSize);
    }

    @Override
    public Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(Long dataSourceId, int pageNum, int pageSize) {
        return dataSourceId != null ?
                dataSourceService.getDatabaseMetadataPage(dataSourceId, pageNum, pageSize) :
                dataSourceService.getDatabaseMetadataPage(pageNum, pageSize);
    }
    
    /**
     * 处理用户反馈
     *
     * @param queryId       查询ID
     * @param feedbackValue 反馈值
     * @param feedbackText  反馈文本
     * @return 是否成功处理反馈
     */
    @Override
    public boolean submitFeedback(String queryId, String feedbackValue, String feedbackText) {
        log.info("收到用户反馈 - 查询ID: {}, 反馈值: {}, 反馈文本: {}", queryId, feedbackValue, feedbackText);
        // 这里可以添加实际的反馈处理逻辑，例如保存到数据库等
        return true;
    }
    
    /**
     * 在指定会话中进行查询
     *
     * @param request 查询请求
     * @param conversationId 会话ID
     * @return 查询响应
     */
    @Override
    public NlQueryResponse processConversationQuery(NlQueryRequest request, String conversationId) {
        if (request.getUserId() == null || request.getUserId().isEmpty() || conversationId == null || conversationId.isEmpty()) {
            log.warn("在指定会话中查询失败：用户ID或会话ID为空");
            return NlQueryResponse.builder()
                    .id(UUID.randomUUID().toString())
                    .query(request.getQuery())
                    .success(false)
                    .errorMessage("用户ID或会话ID不能为空")
                    .build();
        }

        log.info("使用Spring AI原生会话管理，会话ID: {}, 用户ID: {}", conversationId, request.getUserId());
        return processQueryInternal(request, conversationId);
    }
    
}
