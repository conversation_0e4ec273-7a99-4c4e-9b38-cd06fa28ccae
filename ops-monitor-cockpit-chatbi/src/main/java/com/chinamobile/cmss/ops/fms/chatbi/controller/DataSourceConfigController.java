/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.cmss.ops.fms.chatbi.dto.DataSourceConfigDTO;
import com.chinamobile.cmss.ops.fms.chatbi.entity.DataSourceConfigEntity;

import com.chinamobile.cmss.ops.fms.chatbi.service.DataSourceService;
import com.chinamobile.cmss.ops.monitor.cockpit.common.enums.ResponseCode;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.RootException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 数据源配置控制器
 * 提供数据源配置的CRUD操作API
 */
@RestController
@RequestMapping("/api/datasource-config")
@Slf4j
public class DataSourceConfigController {



    @Autowired
    private DataSourceService dataSourceService;

    /**
     * 获取所有启用的数据源配置
     *
     * @return 数据源配置列表
     */
    @GetMapping("/enabled")
    public List<DataSourceConfigDTO> getAllEnabled() {
        try {
            log.info("获取所有启用的数据源配置");
            return dataSourceService.getAllEnabledEncryptedConfigs();
        } catch (Exception e) {
            log.error("获取启用数据源配置失败", e);
            throw new RootException("获取启用数据源配置失败: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }

    /**
     * 获取默认数据源配置
     *
     * @return 默认数据源配置
     */
    @GetMapping("/default")
    public DataSourceConfigDTO getDefault() {
        try {
            log.info("获取默认数据源配置");
            DataSourceConfigEntity defaultConfig = dataSourceService.getDefaultConfig();
            if (defaultConfig == null) {
                throw new RootException("未找到默认数据源配置",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            return dataSourceService.getEncryptedConfigById(defaultConfig.getId());
        } catch (Exception e) {
            log.error("获取默认数据源配置失败", e);
            throw new RootException("获取默认数据源配置失败: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }

    /**
     * 根据ID获取数据源配置
     *
     * @param id 数据源ID
     * @return 数据源配置
     */
    @GetMapping("/{id}")
    public DataSourceConfigDTO getById(@PathVariable Long id) {
        try {
            log.info("根据ID获取数据源配置: {}", id);
            DataSourceConfigDTO config = dataSourceService.getEncryptedConfigById(id);
            if (config == null) {
                throw new RootException("数据源配置不存在",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            return config;
        } catch (Exception e) {
            log.error("根据ID获取数据源配置失败: {}", id, e);
            throw new RootException("获取数据源配置失败: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }

    /**
     * 分页查询数据源配置
     *
     * @param page     页码
     * @param pageSize 每页大小
     * @return 分页数据源配置
     */
    @GetMapping
    public Page<DataSourceConfigDTO> getPage(@RequestParam(defaultValue = "1") int page,
                                             @RequestParam(defaultValue = "10") int pageSize) {
        try {
            log.info("分页查询数据源配置 - 页码: {}, 每页大小: {}", page, pageSize);
            return dataSourceService.getEncryptedConfigPage(page, pageSize);
        } catch (Exception e) {
            log.error("分页查询数据源配置失败", e);
            throw new RootException("分页查询数据源配置失败: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }

    /**
     * 获取所有数据源信息（简化版，用于下拉选择）
     *
     * @return 数据源信息列表
     */
    @GetMapping("/list")
    public List<Map<String, String>> getAllDataSources() {
        try {
            log.info("获取所有数据源信息列表");
            return dataSourceService.getAllDataSources();
        } catch (Exception e) {
            log.error("获取数据源信息列表失败", e);
            throw new RootException("获取数据源信息列表失败: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }

    /**
     * 创建数据源配置
     *
     * @param config 数据源配置
     * @return 创建的数据源配置
     */
    @PostMapping
    public DataSourceConfigEntity create(@RequestBody DataSourceConfigEntity config) {
        try {
            log.info("创建数据源配置: {}", config.getName());
            return dataSourceService.createConfig(config);
        } catch (Exception e) {
            log.error("创建数据源配置失败", e);
            throw new RootException("创建数据源配置失败: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }

    /**
     * 更新数据源配置
     *
     * @param id     数据源ID
     * @param config 数据源配置
     * @return 更新的数据源配置
     */
    @PutMapping("/{id}")
    public DataSourceConfigEntity update(@PathVariable Long id, @RequestBody DataSourceConfigEntity config) {
        try {
            log.info("更新数据源配置: {}", id);
            config.setId(id);
            return dataSourceService.updateConfig(config);
        } catch (Exception e) {
            log.error("更新数据源配置失败: {}", id, e);
            throw new RootException("更新数据源配置失败: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }

    /**
     * 删除数据源配置
     *
     * @param id 数据源ID
     * @return 是否成功删除
     */
    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable Long id) {
        try {
            log.info("删除数据源配置: {}", id);
            return dataSourceService.deleteConfig(id);
        } catch (Exception e) {
            log.error("删除数据源配置失败: {}", id, e);
            throw new RootException("删除数据源配置失败: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }

    /**
     * 测试数据源连接
     *
     * @param config 数据源配置
     * @return 是否连接成功
     */
    @PostMapping("/test-connection")
    public boolean testConnection(@RequestBody DataSourceConfigEntity config) {
        try {
            log.info("测试数据源连接: {}", config.getName());
            return dataSourceService.testConnection(config);
        } catch (Exception e) {
            log.error("测试数据源连接失败", e);
            throw new RootException("测试数据源连接失败: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }

    /**
     * 设置默认数据源
     *
     * @param id 数据源ID
     * @return 是否成功设置
     */
    @PutMapping("/{id}/set-default")
    public boolean setDefault(@PathVariable Long id) {
        try {
            log.info("设置默认数据源: {}", id);
            return dataSourceService.setDefaultConfig(id);
        } catch (Exception e) {
            log.error("设置默认数据源失败: {}", id, e);
            throw new RootException("设置默认数据源失败: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }

    /**
     * 启用或禁用数据源
     *
     * @param id      数据源ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    @PutMapping("/{id}/enabled")
    public boolean setEnabled(@PathVariable Long id, @RequestParam boolean enabled) {
        try {
            log.info("设置数据源启用状态: {} -> {}", id, enabled);
            return dataSourceService.setConfigEnabled(id, enabled);
        } catch (Exception e) {
            log.error("设置数据源启用状态失败: {}", id, e);
            throw new RootException("设置数据源启用状态失败: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
}
