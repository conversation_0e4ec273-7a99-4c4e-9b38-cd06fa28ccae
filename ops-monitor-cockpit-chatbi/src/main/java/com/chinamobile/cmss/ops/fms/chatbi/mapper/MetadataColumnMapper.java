/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataColumn;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 元数据列Mapper接口
 */
@Mapper
public interface MetadataColumnMapper extends BaseMapper<MetadataColumn> {
    
    /**
     * 根据表ID查询启用的列
     *
     * @param tableId 表ID
     * @return 列列表
     */
    List<MetadataColumn> selectEnabledColumnsByTableId(@Param("tableId") Long tableId);
    
    /**
     * 根据表名查询启用的列
     *
     * @param tableName 表名
     * @return 列列表
     */
    List<MetadataColumn> selectEnabledColumnsByTableName(@Param("tableName") String tableName);
}
