/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataColumn;
import com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataRelationship;
import com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataTable;
import com.chinamobile.cmss.ops.fms.chatbi.model.DatabaseMetadata;

import java.util.List;
import java.util.Map;

/**
 * 元数据服务接口
 */
public interface MetadataService {
    
    /**
     * 获取指定数据源的数据库元数据
     *
     * @param dataSourceId 数据源ID
     * @return 数据库元数据
     */
    DatabaseMetadata getDatabaseMetadata(Long dataSourceId);
    
    /**
     * 分页获取数据库元数据
     *
     * @param databaseName 数据库名称
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @return 分页数据库元数据
     */
    Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(String databaseName, int pageNum, int pageSize);
    
    /**
     * 分页获取指定数据源的数据库元数据
     *
     * @param dataSourceId 数据源ID
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @return 分页数据库元数据
     */
    Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(Long dataSourceId, int pageNum, int pageSize);
    
    /**
     * 清除指定数据源的数据库元数据缓存
     *
     * @param dataSourceId 数据源ID
     */
    void clearDatabaseMetadataCache(Long dataSourceId);
    
    /**
     * 清除所有数据库元数据缓存
     */
    void clearAllDatabaseMetadataCache();
    
    /**
     * 获取元数据表列表
     *
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @param databaseName 数据库名称
     * @return 元数据表列表与分页信息
     */
    Map<String, Object> getMetadataTables(int pageNum, int pageSize, String databaseName);
    
    /**
     * 获取指定数据源的元数据表列表
     *
     * @param dataSourceId 数据源ID
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @param databaseName 数据库名称
     * @return 元数据表列表与分页信息
     */
    Map<String, Object> getMetadataTablesByDataSource(Long dataSourceId, int pageNum, int pageSize, String databaseName);
    
    /**
     * 获取元数据表详情
     *
     * @param id 元数据表ID
     * @return 元数据表详情与列信息
     */
    Map<String, Object> getMetadataTable(Long id);
    
    /**
     * 创建元数据表
     *
     * @param table 元数据表
     * @return 创建的元数据表
     */
    MetadataTable createMetadataTable(MetadataTable table);
    
    /**
     * 更新元数据表
     *
     * @param id    元数据表ID
     * @param table 元数据表
     * @return 更新后的元数据表
     */
    MetadataTable updateMetadataTable(Long id, MetadataTable table);
    
    /**
     * 删除元数据表
     *
     * @param id 元数据表ID
     * @return 删除结果消息
     */
    String deleteMetadataTable(Long id);
    
    /**
     * 获取元数据列列表
     *
     * @param tableId 表ID
     * @return 元数据列列表与表信息
     */
    Map<String, Object> getMetadataColumns(Long tableId);
    
    /**
     * 创建元数据列
     *
     * @param column 元数据列
     * @return 创建的元数据列
     */
    MetadataColumn createMetadataColumn(MetadataColumn column);
    
    /**
     * 更新元数据列
     *
     * @param id     元数据列ID
     * @param column 元数据列
     * @return 更新后的元数据列
     */
    MetadataColumn updateMetadataColumn(Long id, MetadataColumn column);
    
    /**
     * 删除元数据列
     *
     * @param id 元数据列ID
     * @return 删除结果消息
     */
    String deleteMetadataColumn(Long id);
    
    /**
     * 获取元数据关系列表
     *
     * @return 元数据关系列表
     */
    List<MetadataRelationship> getMetadataRelationships();
    
    /**
     * 创建元数据关系
     *
     * @param relationship 元数据关系
     * @return 创建的元数据关系
     */
    MetadataRelationship createMetadataRelationship(MetadataRelationship relationship);
    
    /**
     * 更新元数据关系
     *
     * @param id           元数据关系ID
     * @param relationship 元数据关系
     * @return 更新后的元数据关系
     */
    MetadataRelationship updateMetadataRelationship(Long id, MetadataRelationship relationship);
    
    /**
     * 删除元数据关系
     *
     * @param id 元数据关系ID
     * @return 删除结果消息
     */
    String deleteMetadataRelationship(Long id);
    
    /**
     * 刷新元数据缓存
     *
     * @return 刷新结果消息
     */
    String refreshMetadataCache();
    
    /**
     * 执行SQL查询
     *
     * @param dataSourceId 数据源ID
     * @param sql          SQL查询语句
     * @return 查询结果
     */
    List<Map<String, Object>> executeQuery(Long dataSourceId, String sql);
}
