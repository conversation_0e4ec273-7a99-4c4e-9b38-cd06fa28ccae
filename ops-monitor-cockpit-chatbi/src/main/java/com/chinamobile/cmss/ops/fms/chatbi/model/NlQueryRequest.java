/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自然语言查询请求模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NlQueryRequest {
    
    /**
     * 自然语言查询
     */
    private String query;
    
    /**
     * 是否需要SQL解释
     */
    private boolean needExplanation;
    
    /**
     * 是否保存查询历史
     */
    private boolean saveHistory;
    
    /**
     * 用户ID（可选）
     */
    private String userId;
    
    /**
     * 数据源ID（可选，默认使用配置的默认数据源）
     */
    private Long dataSourceId;

}
