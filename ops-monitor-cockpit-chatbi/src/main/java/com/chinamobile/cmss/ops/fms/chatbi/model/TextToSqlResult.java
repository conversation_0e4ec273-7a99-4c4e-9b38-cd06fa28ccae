/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文本到SQL转换结果模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextToSqlResult {
    
    /**
     * 原始自然语言查询
     */
    private String naturalLanguageQuery;
    
    /**
     * 生成的SQL查询
     */
    private String sql;
    
    /**
     * SQL解释
     */
    private String explanation;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
    
    /**
     * 原始LLM响应（用于调试）
     */
    private String rawResponse;
    
    /**
     * 生成时间戳
     */
    private long timestamp = System.currentTimeMillis();
}
