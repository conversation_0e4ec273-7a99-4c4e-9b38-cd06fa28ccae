/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会话操作响应模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationResponse {
    
    /**
     * 操作消息
     */
    private String message;
    
    /**
     * 会话ID
     */
    private String conversationId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdatedAt;
    
    /**
     * 会话数量
     */
    private int count;
    
    /**
     * 会话列表
     */
    private List<ConversationInfo> conversations;
    
    /**
     * 会话信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConversationInfo {
        
        /**
         * 会话ID
         */
        private String conversationId;
        
        /**
         * 创建时间
         */
        private LocalDateTime createdAt;
        
        /**
         * 最后更新时间
         */
        private LocalDateTime lastUpdatedAt;
        
        /**
         * 交互次数
         */
        private int exchangeCount;
        
        /**
         * 最近一次查询
         */
        private String lastQuery;
    }
}
