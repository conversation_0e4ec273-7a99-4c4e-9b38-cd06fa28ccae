/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 自然语言查询响应模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NlQueryResponse {
    
    /**
     * 查询ID
     */
    private String id;
    
    /**
     * 原始自然语言查询
     */
    private String query;
    
    /**
     * 生成的SQL查询
     */
    private String generatedSql;
    
    // SQL解释功能已移除
    
    /**
     * 查询结果
     */
    private List<Map<String, Object>> results;
    
    /**
     * 结果列信息
     */
    private List<ColumnInfo> columns;
    
    /**
     * 建议的可视化类型
     */
    private String suggestedVisualization;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 执行时间（毫秒）
     */
    private long executionTimeMs;
    
    /**
     * 数据源名称
     */
    private String dataSource;
    
    /**
     * 是否有上下文
     */
    private boolean hasContext;
    
    /**
     * 会话ID
     */
    private String conversationId;
    
    /**
     * 列信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ColumnInfo {
        
        /**
         * 列名
         */
        private String name;
        
        /**
         * 列标签（显示名称）
         */
        private String label;
        
        /**
         * 数据类型
         */
        private String type;
    }
}
