/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.dto;

import com.chinamobile.cmss.ops.fms.chatbi.entity.DataSourceConfigEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据源配置DTO
 * 用于API接口返回，敏感字段保持加密状态
 */
@Data
public class DataSourceConfigDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 数据源名称
     */
    private String name;
    
    /**
     * 数据源类型（mysql, postgresql, doris等）
     */
    private String type;
    
    /**
     * 数据库连接URL
     */
    private String jdbcUrl;
    
    /**
     * 数据库用户名（加密）
     */
    private String username;
    
    /**
     * 数据库密码（加密）
     */
    private String password;
    
    /**
     * 数据库驱动类名
     */
    private String driverClassName;
    
    /**
     * 数据库名称
     */
    private String databaseName;
    
    /**
     * 是否为默认数据源
     */
    private Boolean isDefault;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 最大连接数
     */
    private Integer maxPoolSize;
    
    /**
     * 最小空闲连接数
     */
    private Integer minIdle;
    
    /**
     * 连接超时时间（毫秒）
     */
    private Long connectionTimeout;
    
    /**
     * 空闲超时时间（毫秒）
     */
    private Long idleTimeout;
    
    /**
     * 最大生命周期（毫秒）
     */
    private Long maxLifetime;
    
    /**
     * 其他配置（JSON格式）
     */
    private String properties;
    
    /**
     * 从实体转换为DTO
     *
     * @param entity 数据源配置实体
     * @return 数据源配置DTO
     */
    public static DataSourceConfigDTO fromEntity(DataSourceConfigEntity entity) {
        if (entity == null) {
            return null;
        }
        
        DataSourceConfigDTO dto = new DataSourceConfigDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setType(entity.getType());
        dto.setJdbcUrl(entity.getJdbcUrl());
        // 保持加密状态
        dto.setUsername(entity.getUsername());
        // 保持加密状态
        dto.setPassword(entity.getPassword());
        dto.setDriverClassName(entity.getDriverClassName());
        dto.setDatabaseName(entity.getDatabaseName());
        dto.setIsDefault(entity.getIsDefault());
        dto.setEnabled(entity.getEnabled());
        dto.setDescription(entity.getDescription());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setMaxPoolSize(entity.getMaxPoolSize());
        dto.setMinIdle(entity.getMinIdle());
        dto.setConnectionTimeout(entity.getConnectionTimeout());
        dto.setIdleTimeout(entity.getIdleTimeout());
        dto.setMaxLifetime(entity.getMaxLifetime());
        dto.setProperties(entity.getProperties());
        
        return dto;
    }
    
    /**
     * 转换为实体
     *
     * @return 数据源配置实体
     */
    public DataSourceConfigEntity toEntity() {
        DataSourceConfigEntity entity = new DataSourceConfigEntity();
        entity.setId(this.getId());
        entity.setName(this.getName());
        entity.setType(this.getType());
        entity.setJdbcUrl(this.getJdbcUrl());
        entity.setUsername(this.getUsername());
        entity.setPassword(this.getPassword());
        entity.setDriverClassName(this.getDriverClassName());
        entity.setDatabaseName(this.getDatabaseName());
        entity.setIsDefault(this.getIsDefault());
        entity.setEnabled(this.getEnabled());
        entity.setDescription(this.getDescription());
        entity.setCreateTime(this.getCreateTime());
        entity.setUpdateTime(this.getUpdateTime());
        entity.setMaxPoolSize(this.getMaxPoolSize());
        entity.setMinIdle(this.getMinIdle());
        entity.setConnectionTimeout(this.getConnectionTimeout());
        entity.setIdleTimeout(this.getIdleTimeout());
        entity.setMaxLifetime(this.getMaxLifetime());
        entity.setProperties(this.getProperties());
        
        return entity;
    }
}
