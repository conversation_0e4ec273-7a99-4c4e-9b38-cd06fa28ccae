/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.cmss.ops.fms.chatbi.entity.DataSourceConfigEntity;
import com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataColumn;
import com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataRelationship;
import com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataTable;
import com.chinamobile.cmss.ops.fms.chatbi.mapper.MetadataColumnMapper;
import com.chinamobile.cmss.ops.fms.chatbi.mapper.MetadataRelationshipMapper;
import com.chinamobile.cmss.ops.fms.chatbi.mapper.MetadataTableMapper;
import com.chinamobile.cmss.ops.fms.chatbi.model.DatabaseMetadata;
import com.chinamobile.cmss.ops.fms.chatbi.service.DataSourceService;
import com.chinamobile.cmss.ops.fms.chatbi.service.DataSourceService;
import com.chinamobile.cmss.ops.fms.chatbi.service.MetadataService;
import com.chinamobile.cmss.ops.monitor.cockpit.common.enums.ResponseCode;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.RootException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 元数据服务实现类
 */
@Service
@Slf4j
public class MetadataServiceImpl implements MetadataService {
    
    @Autowired
    private MetadataTableMapper metadataTableMapper;
    
    @Autowired
    private MetadataColumnMapper metadataColumnMapper;
    
    @Autowired
    private MetadataRelationshipMapper metadataRelationshipMapper;
    
    @Autowired
    private DataSourceService dataSourceService;

    
    /**
     * 获取数据库元数据
     *
     * @param dataSourceId 数据源ID
     * @return 数据库元数据
     */
    @Override
    @Cacheable(value = "databaseMetadata", key = "#dataSourceId")
    public DatabaseMetadata getDatabaseMetadata(Long dataSourceId) {
        log.info("从数据库中获取元数据（未命中缓存）, 数据源ID: {}", dataSourceId);
        
        // 获取数据源配置
        DataSourceConfigEntity dataSourceConfig = dataSourceService.getConfigById(dataSourceId);
        if (dataSourceConfig == null) {
            throw new RootException("数据源不存在",
                    String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
        }
        
        // 构建数据库元数据对象
        DatabaseMetadata metadata = new DatabaseMetadata();
        metadata.setDatabaseName(dataSourceConfig.getDatabaseName());
        metadata.setDatabaseType(dataSourceConfig.getType());
        
        // 查询表元数据
        List<MetadataTable> tables = metadataTableMapper.selectEnabledTablesByDatabase(dataSourceConfig.getDatabaseName());
        
        // 转换表元数据
        List<DatabaseMetadata.TableMetadata> tableMetadataList = new ArrayList<>();
        for (MetadataTable table : tables) {
            DatabaseMetadata.TableMetadata tableMetadata = new DatabaseMetadata.TableMetadata();
            tableMetadata.setTableName(table.getTableName());
            tableMetadata.setDescription(table.getDescription());
            
            // 查询列元数据
            List<MetadataColumn> columns = metadataColumnMapper.selectEnabledColumnsByTableId(table.getId());
            
            // 转换列元数据
            List<DatabaseMetadata.ColumnMetadata> columnMetadataList = columns.stream()
                    .map(column -> DatabaseMetadata.ColumnMetadata.builder()
                            .columnName(column.getColumnName())
                            .dataType(column.getDataType())
                            .primaryKey(column.getPrimaryKey() == 1)
                            .description(column.getDescription())
                            .chineseName(column.getChineseName())
                            .build())
                    .collect(Collectors.toList());
            
            tableMetadata.setColumns(columnMetadataList);
            tableMetadataList.add(tableMetadata);
        }
        
        metadata.setTables(tableMetadataList);
        
        // 查询表关系
        List<MetadataRelationship> relationships = metadataRelationshipMapper.selectEnabledRelationships();
        
        // 转换表关系
        List<DatabaseMetadata.TableRelationship> relationshipList = relationships.stream()
                .map(relationship -> DatabaseMetadata.TableRelationship.builder()
                        .sourceTable(relationship.getSourceTable())
                        .sourceColumn(relationship.getSourceColumn())
                        .targetTable(relationship.getTargetTable())
                        .targetColumn(relationship.getTargetColumn())
                        .relationshipType(relationship.getRelationshipType())
                        .build())
                .collect(Collectors.toList());
        
        metadata.setRelationships(relationshipList);
        
        return metadata;
    }
    
    /**
     * 分页获取数据库元数据
     *
     * @param databaseName 数据库名称
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @return 分页数据库元数据
     */
    @Override
    public Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(String databaseName, int pageNum, int pageSize) {
        log.info("分页获取数据库元数据, 数据库名称: {}, 页码: {}, 每页大小: {}", databaseName, pageNum, pageSize);
        
        try {
            // 创建分页对象
            Page<MetadataTable> page = new Page<>(pageNum, pageSize);
            
            // 分页查询表元数据
            Page<MetadataTable> tablePage = metadataTableMapper.selectEnabledTablesPage(page);
            
            // 创建结果分页对象
            Page<DatabaseMetadata.TableMetadata> resultPage = new Page<>(pageNum, pageSize);
            resultPage.setTotal(tablePage.getTotal());
            
            // 转换表元数据
            List<DatabaseMetadata.TableMetadata> tableMetadataList = new ArrayList<>();
            for (MetadataTable table : tablePage.getRecords()) {
                DatabaseMetadata.TableMetadata tableMetadata = new DatabaseMetadata.TableMetadata();
                tableMetadata.setTableName(table.getTableName());
                tableMetadata.setDescription(table.getDescription());
                
                // 查询列元数据
                List<MetadataColumn> columns = metadataColumnMapper.selectEnabledColumnsByTableId(table.getId());
                
                // 转换列元数据
                List<DatabaseMetadata.ColumnMetadata> columnMetadataList = columns.stream()
                        .map(column -> DatabaseMetadata.ColumnMetadata.builder()
                                .columnName(column.getColumnName())
                                .dataType(column.getDataType())
                                .primaryKey(column.getPrimaryKey() == 1)
                                .description(column.getDescription())
                                .chineseName(column.getChineseName())
                                .build())
                        .collect(Collectors.toList());
                
                tableMetadata.setColumns(columnMetadataList);
                tableMetadataList.add(tableMetadata);
            }
            
            resultPage.setRecords(tableMetadataList);
            
            return resultPage;
        } catch (Exception e) {
            log.error("分页获取数据库元数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("分页获取数据库元数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 分页获取数据库元数据
     *
     * @param dataSourceId 数据源ID
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @return 分页数据库元数据
     */
    @Override
    public Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(Long dataSourceId, int pageNum, int pageSize) {
        log.info("分页获取数据库元数据, 数据源ID: {}, 页码: {}, 每页大小: {}", dataSourceId, pageNum, pageSize);
        
        try {
            // 获取数据源配置
            DataSourceConfigEntity dataSourceConfig = dataSourceService.getConfigById(dataSourceId);
            if (dataSourceConfig == null) {
                throw new RootException("数据源不存在",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            // 创建分页对象和查询条件
            final Page<MetadataTable> page = new Page<>(pageNum, pageSize);
            final QueryWrapper<MetadataTable> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("database_name", dataSourceConfig.getDatabaseName());
            queryWrapper.eq("enable", 1);
            queryWrapper.orderByDesc("sort_order").orderByAsc("id");
            
            Page<MetadataTable> tablePage = metadataTableMapper.selectPage(page, queryWrapper);
            
            // 创建结果分页对象
            Page<DatabaseMetadata.TableMetadata> resultPage = new Page<>(pageNum, pageSize);
            resultPage.setTotal(tablePage.getTotal());
            
            // 转换表元数据
            List<DatabaseMetadata.TableMetadata> tableMetadataList = new ArrayList<>();
            for (MetadataTable table : tablePage.getRecords()) {
                DatabaseMetadata.TableMetadata tableMetadata = new DatabaseMetadata.TableMetadata();
                tableMetadata.setTableName(table.getTableName());
                tableMetadata.setDescription(table.getDescription());
                
                // 查询列元数据
                List<MetadataColumn> columns = metadataColumnMapper.selectEnabledColumnsByTableId(table.getId());
                
                // 转换列元数据
                List<DatabaseMetadata.ColumnMetadata> columnMetadataList = columns.stream()
                        .map(column -> DatabaseMetadata.ColumnMetadata.builder()
                                .columnName(column.getColumnName())
                                .dataType(column.getDataType())
                                .primaryKey(column.getPrimaryKey() == 1)
                                .description(column.getDescription())
                                .chineseName(column.getChineseName())
                                .build())
                        .collect(Collectors.toList());
                
                tableMetadata.setColumns(columnMetadataList);
                tableMetadataList.add(tableMetadata);
            }
            
            resultPage.setRecords(tableMetadataList);
            
            return resultPage;
        } catch (RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("分页获取数据库元数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("分页获取数据库元数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 清除数据库元数据缓存
     *
     * @param dataSourceId 数据源ID
     */
    @Override
    @CacheEvict(value = "databaseMetadata", key = "#dataSourceId")
    public void clearDatabaseMetadataCache(Long dataSourceId) {
        log.info("清除数据源ID: {} 的元数据缓存", dataSourceId);
    }
    
    /**
     * 清除所有数据库元数据缓存
     */
    @Override
    @CacheEvict(value = "databaseMetadata", allEntries = true)
    public void clearAllDatabaseMetadataCache() {
        log.info("清除所有数据库元数据缓存");
    }
    
    /**
     * 获取元数据表列表
     *
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @param databaseName 数据库名称
     * @return 元数据表列表与分页信息
     */
    @Override
    public Map<String, Object> getMetadataTables(int pageNum, int pageSize, String databaseName) {
        log.info("获取元数据表列表, 页码: {}, 每页大小: {}, 数据库名称: {}", pageNum, pageSize, databaseName);
        
        try {
            Page<MetadataTable> page = new Page<>(pageNum, pageSize);
            QueryWrapper<MetadataTable> queryWrapper = new QueryWrapper<>();
            
            if (databaseName != null && !databaseName.isEmpty()) {
                queryWrapper.eq("database_name", databaseName);
            }
            
            queryWrapper.orderByDesc("sort_order").orderByAsc("id");
            Page<MetadataTable> result = metadataTableMapper.selectPage(page, queryWrapper);
            
            Map<String, Object> response = new HashMap<>();
            response.put("data", result.getRecords());
            response.put("total", result.getTotal());
            response.put("pageNum", result.getCurrent());
            response.put("pageSize", result.getSize());
            return response;
        } catch (Exception e) {
            log.error("获取元数据表列表时发生错误", e);
            throw new RootException("获取元数据表列表时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 获取指定数据源的元数据表列表
     *
     * @param dataSourceId 数据源ID
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @param databaseName 数据库名称
     * @return 元数据表列表与分页信息
     */
    @Override
    public Map<String, Object> getMetadataTablesByDataSource(Long dataSourceId, int pageNum, int pageSize, String databaseName) {
        log.info("获取指定数据源的元数据表列表, 数据源ID: {}, 页码: {}, 每页大小: {}, 数据库名称: {}",
                dataSourceId, pageNum, pageSize, databaseName);
        
        try {
            // 验证数据源是否存在
            if (dataSourceId != null) {
                DataSourceConfigEntity dataSource = dataSourceService.getConfigById(dataSourceId);
                if (dataSource == null) {
                    throw new RootException("数据源不存在",
                            String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
                }
            }
            
            QueryWrapper<MetadataTable> queryWrapper = new QueryWrapper<>();
            
            // 添加数据源ID条件
            queryWrapper.eq("data_source_id", dataSourceId);
            
            if (databaseName != null && !databaseName.isEmpty()) {
                queryWrapper.eq("database_name", databaseName);
            }
            Page<MetadataTable> page = new Page<>(pageNum, pageSize);
            queryWrapper.orderByDesc("sort_order").orderByAsc("id");
            Page<MetadataTable> result = metadataTableMapper.selectPage(page, queryWrapper);
            
            Map<String, Object> response = new HashMap<>();
            response.put("data", result.getRecords());
            response.put("total", result.getTotal());
            response.put("pageNum", result.getCurrent());
            response.put("pageSize", result.getSize());
            return response;
        } catch (RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取指定数据源的元数据表列表时发生错误: {}", e.getMessage(), e);
            throw new RootException("获取指定数据源的元数据表列表时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 获取元数据表详情
     *
     * @param id 元数据表ID
     * @return 元数据表详情与列信息
     */
    @Override
    public Map<String, Object> getMetadataTable(Long id) {
        log.info("获取元数据表详情, ID: {}", id);
        
        try {
            MetadataTable table = metadataTableMapper.selectById(id);
            if (table == null) {
                throw new RootException("元数据表不存在",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            // 查询表的列
            QueryWrapper<MetadataColumn> columnQueryWrapper = new QueryWrapper<>();
            columnQueryWrapper.eq("table_id", id);
            columnQueryWrapper.orderByDesc("sort_order").orderByAsc("id");
            List<MetadataColumn> columns = metadataColumnMapper.selectList(columnQueryWrapper);
            
            Map<String, Object> response = new HashMap<>();
            response.put("data", table);
            response.put("columns", columns);
            return response;
        } catch (RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取元数据表详情时发生错误", e);
            throw new RootException("获取元数据表详情时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 创建元数据表
     *
     * @param table 元数据表
     * @return 创建的元数据表
     */
    @Override
    public MetadataTable createMetadataTable(MetadataTable table) {
        log.info("创建元数据表: {}, 数据源ID: {}", table.getTableName(), table.getDataSourceId());
        
        try {
            // 设置创建时间和更新时间
            LocalDateTime now = LocalDateTime.now();
            table.setCreateTime(now);
            table.setUpdateTime(now);
            
            // 如果未设置启用状态，默认启用
            if (table.getEnable() == null) {
                table.setEnable((short) 1);
            }
            
            // 如果未设置排序值，默认为0
            if (table.getSortOrder() == null) {
                table.setSortOrder(0);
            }
            
            // 如果未设置数据源ID，使用默认数据源
            if (table.getDataSourceId() == null) {
                DataSourceConfigEntity defaultDataSource = dataSourceService.getDefaultConfig();
                if (defaultDataSource != null) {
                    table.setDataSourceId(defaultDataSource.getId());
                    log.info("使用默认数据源ID: {}", defaultDataSource.getId());
                } else {
                    log.warn("未找到默认数据源");
                }
            }
            
            // 插入表
            metadataTableMapper.insert(table);
            
            // 清除缓存
            if (table.getDataSourceId() != null) {
                clearDatabaseMetadataCache(table.getDataSourceId());
            } else {
                clearAllDatabaseMetadataCache();
            }
            
            return table;
        } catch (Exception e) {
            log.error("创建元数据表时发生错误", e);
            throw new RootException("创建元数据表时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 更新元数据表
     *
     * @param id    元数据表ID
     * @param table 元数据表
     * @return 更新后的元数据表
     */
    @Override
    public MetadataTable updateMetadataTable(Long id, MetadataTable table) {
        log.info("更新元数据表, ID: {}", id);
        
        try {
            // 检查表是否存在
            MetadataTable existingTable = metadataTableMapper.selectById(id);
            if (existingTable == null) {
                throw new RootException("元数据表不存在",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            // 设置ID和更新时间
            table.setId(id);
            table.setUpdateTime(LocalDateTime.now());
            
            // 更新表
            metadataTableMapper.updateById(table);
            
            return table;
        } catch (RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新元数据表时发生错误", e);
            throw new RootException("更新元数据表时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 删除元数据表
     *
     * @param id 元数据表ID
     * @return 删除结果消息
     */
    @Override
    public String deleteMetadataTable(Long id) {
        log.info("删除元数据表, ID: {}", id);
        
        try {
            // 检查表是否存在
            MetadataTable existingTable = metadataTableMapper.selectById(id);
            if (existingTable == null) {
                throw new RootException("元数据表不存在",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            // 删除表（会级联删除列）
            metadataTableMapper.deleteById(id);
            
            return "元数据表删除成功";
        } catch (RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除元数据表时发生错误", e);
            throw new RootException("删除元数据表时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 获取元数据列列表
     *
     * @param tableId 表ID
     * @return 元数据列列表与表信息
     */
    @Override
    public Map<String, Object> getMetadataColumns(Long tableId) {
        log.info("获取元数据列列表, 表ID: {}", tableId);
        
        try {
            // 检查表是否存在
            MetadataTable existingTable = metadataTableMapper.selectById(tableId);
            if (existingTable == null) {
                throw new RootException("元数据表不存在",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            // 查询列
            QueryWrapper<MetadataColumn> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("table_id", tableId);
            queryWrapper.orderByDesc("sort_order").orderByAsc("id");
            List<MetadataColumn> columns = metadataColumnMapper.selectList(queryWrapper);
            
            Map<String, Object> response = new HashMap<>();
            response.put("data", columns);
            response.put("table", existingTable);
            return response;
        } catch (RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取元数据列列表时发生错误", e);
            throw new RootException("获取元数据列列表时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 创建元数据列
     *
     * @param column 元数据列
     * @return 创建的元数据列
     */
    @Override
    public MetadataColumn createMetadataColumn(MetadataColumn column) {
        log.info("创建元数据列: {}", column.getColumnName());
        
        try {
            // 检查表是否存在
            MetadataTable existingTable = metadataTableMapper.selectById(column.getTableId());
            if (existingTable == null) {
                throw new RootException("元数据表不存在",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            // 设置创建时间和更新时间
            LocalDateTime now = LocalDateTime.now();
            column.setCreateTime(now);
            column.setUpdateTime(now);
            
            // 如果未设置启用状态，默认启用
            if (column.getEnable() == null) {
                column.setEnable((short) 1);
            }
            
            // 如果未设置排序值，默认为0
            if (column.getSortOrder() == null) {
                column.setSortOrder(0);
            }
            
            // 如果未设置主键标志，默认为非主键
            if (column.getPrimaryKey() == null) {
                column.setPrimaryKey((short) 0);
            }
            
            // 如果未设置中文名，默认与描述保持一致
            if (column.getChineseName() == null) {
                column.setChineseName(column.getDescription());
            }
            
            // 插入列
            metadataColumnMapper.insert(column);
            
            return column;
        } catch (RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建元数据列时发生错误", e);
            throw new RootException("创建元数据列时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 更新元数据列
     *
     * @param id     元数据列ID
     * @param column 元数据列
     * @return 更新后的元数据列
     */
    @Override
    public MetadataColumn updateMetadataColumn(Long id, MetadataColumn column) {
        log.info("更新元数据列, ID: {}", id);
        
        try {
            // 检查列是否存在
            MetadataColumn existingColumn = metadataColumnMapper.selectById(id);
            if (existingColumn == null) {
                throw new RootException("元数据列不存在",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            // 设置ID和更新时间
            column.setId(id);
            column.setUpdateTime(LocalDateTime.now());
            
            // 如果未设置中文名，默认与描述保持一致
            if (column.getChineseName() == null) {
                column.setChineseName(column.getDescription());
            }
            
            // 更新列
            metadataColumnMapper.updateById(column);
            
            return column;
        } catch (RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新元数据列时发生错误", e);
            throw new RootException("更新元数据列时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 删除元数据列
     *
     * @param id 元数据列ID
     * @return 删除结果消息
     */
    @Override
    public String deleteMetadataColumn(Long id) {
        log.info("删除元数据列, ID: {}", id);
        
        try {
            // 检查列是否存在
            MetadataColumn existingColumn = metadataColumnMapper.selectById(id);
            if (existingColumn == null) {
                throw new RootException("元数据列不存在",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            // 删除列
            metadataColumnMapper.deleteById(id);
            
            return "元数据列删除成功";
        } catch (RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除元数据列时发生错误", e);
            throw new RootException("删除元数据列时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 获取元数据关系列表
     *
     * @return 元数据关系列表
     */
    @Override
    public List<MetadataRelationship> getMetadataRelationships() {
        log.info("获取元数据关系列表");
        
        try {
            // 查询关系
            QueryWrapper<MetadataRelationship> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("sort_order").orderByAsc("id");
            return metadataRelationshipMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("获取元数据关系列表时发生错误", e);
            throw new RootException("获取元数据关系列表时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 创建元数据关系
     *
     * @param relationship 元数据关系
     * @return 创建的元数据关系
     */
    @Override
    public MetadataRelationship createMetadataRelationship(MetadataRelationship relationship) {
        log.info("创建元数据关系: {} -> {}",
                relationship.getSourceTable(), relationship.getTargetTable());
        
        try {
            // 验证源表和目标表是否存在
            MetadataTable sourceTable = findTableByName(relationship.getSourceTable());
            MetadataTable targetTable = findTableByName(relationship.getTargetTable());
            
            if (sourceTable == null) {
                throw new RootException("源表不存在: " + relationship.getSourceTable(),
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            if (targetTable == null) {
                throw new RootException("目标表不存在: " + relationship.getTargetTable(),
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            // 验证源表和目标表是否属于同一数据源
            if (!Objects.equals(sourceTable.getDataSourceId(), targetTable.getDataSourceId())) {
                throw new RootException("源表和目标表必须属于同一数据源",
                        String.valueOf(ResponseCode.BAD_DATASOURCE_REQUEST.getCode()));
            }
            
            // 设置创建时间和更新时间
            LocalDateTime now = LocalDateTime.now();
            relationship.setCreateTime(now);
            relationship.setUpdateTime(now);
            
            // 如果未设置启用状态，默认启用
            if (relationship.getEnable() == null) {
                relationship.setEnable((short) 1);
            }
            
            // 如果未设置排序值，默认为0
            if (relationship.getSortOrder() == null) {
                relationship.setSortOrder(0);
            }
            
            // 插入关系
            metadataRelationshipMapper.insert(relationship);
            
            // 清除缓存
            clearDatabaseMetadataCache(sourceTable.getDataSourceId());
            
            return relationship;
        } catch (RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建元数据关系时发生错误", e);
            throw new RootException("创建元数据关系时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 根据表名查找表
     *
     * @param tableName 表名
     * @return 表元数据
     */
    private MetadataTable findTableByName(String tableName) {
        QueryWrapper<MetadataTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("table_name", tableName);
        queryWrapper.eq("enable", 1);
        return metadataTableMapper.selectOne(queryWrapper);
    }
    
    /**
     * 更新元数据关系
     *
     * @param id           元数据关系ID
     * @param relationship 元数据关系
     * @return 更新后的元数据关系
     */
    @Override
    public MetadataRelationship updateMetadataRelationship(Long id, MetadataRelationship relationship) {
        log.info("更新元数据关系, ID: {}", id);
        
        try {
            // 检查关系是否存在
            MetadataRelationship existingRelationship = metadataRelationshipMapper.selectById(id);
            if (existingRelationship == null) {
                throw new RootException("元数据关系不存在",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            // 设置ID和更新时间
            relationship.setId(id);
            relationship.setUpdateTime(LocalDateTime.now());
            
            // 更新关系
            metadataRelationshipMapper.updateById(relationship);
            
            return relationship;
        } catch (RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新元数据关系时发生错误", e);
            throw new RootException("更新元数据关系时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 删除元数据关系
     *
     * @param id 元数据关系ID
     * @return 删除结果消息
     */
    @Override
    public String deleteMetadataRelationship(Long id) {
        log.info("删除元数据关系, ID: {}", id);
        
        try {
            // 检查关系是否存在
            MetadataRelationship existingRelationship = metadataRelationshipMapper.selectById(id);
            if (existingRelationship == null) {
                throw new RootException("元数据关系不存在",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            // 删除关系
            metadataRelationshipMapper.deleteById(id);
            
            return "元数据关系删除成功";
        } catch (RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除元数据关系时发生错误", e);
            throw new RootException("删除元数据关系时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 刷新元数据缓存
     *
     * @return 刷新结果消息
     */
    @Override
    public String refreshMetadataCache() {
        log.info("刷新元数据缓存");
        
        try {
            // 清除缓存
            clearAllDatabaseMetadataCache();
            return "元数据缓存刷新成功";
        } catch (Exception e) {
            log.error("刷新元数据缓存时发生错误", e);
            throw new RootException("刷新元数据缓存时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 执行SQL查询
     *
     * @param dataSourceId 数据源ID
     * @param sql          SQL查询语句
     * @return 查询结果
     */
    @Override
    public List<Map<String, Object>> executeQuery(Long dataSourceId, String sql) {
        log.info("执行SQL查询, 数据源ID: {}, SQL: {}", dataSourceId, sql);
        
        try {
            // 基本的SQL验证，只允许SELECT语句
            if (sql == null || sql.trim().isEmpty()) {
                throw new IllegalArgumentException("查询SQL不能为空");
            }
            
            String trimmedSql = sql.trim().toLowerCase();
            if (!trimmedSql.startsWith("select")) {
                throw new IllegalArgumentException("只允许执行SELECT查询");
            }
            
            // 获取数据源配置
            DataSourceConfigEntity dataSourceConfig = dataSourceService.getConfigById(dataSourceId);
            if (dataSourceConfig == null) {
                throw new RootException("数据源不存在",
                        String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
            }
            
            // 执行查询
            return dataSourceService.executeQuery(dataSourceConfig.getId(), sql);
        } catch (IllegalArgumentException | RootException e) {
            throw e;
        } catch (Exception e) {
            log.error("执行SQL查询失败: {}", e.getMessage(), e);
            throw new RuntimeException("执行SQL查询失败: " + e.getMessage(), e);
        }
    }
}
