/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.chinamobile.cmss.ops.fms.chatbi.model.ConversationResponse;

import com.chinamobile.cmss.ops.fms.chatbi.model.DatabaseMetadata;
import com.chinamobile.cmss.ops.fms.chatbi.model.NlQueryRequest;
import com.chinamobile.cmss.ops.fms.chatbi.model.NlQueryResponse;
import com.chinamobile.cmss.ops.fms.chatbi.service.NaturalLanguageQueryService;
import com.chinamobile.cmss.ops.monitor.cockpit.common.enums.ResponseCode;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.RootException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 自然语言查询控制器
 */
@RestController
@RequestMapping("/api/nl-query")
@Slf4j
public class NaturalLanguageQueryController {
    
    private final NaturalLanguageQueryService naturalLanguageQueryService;
    
    @Autowired
    public NaturalLanguageQueryController(NaturalLanguageQueryService naturalLanguageQueryService) {
        this.naturalLanguageQueryService = naturalLanguageQueryService;
    }
    
    /**
     * 处理自然语言查询
     *
     * @param request 查询请求
     * @return 查询响应
     */
    @PostMapping
    public NlQueryResponse query(@RequestBody NlQueryRequest request) {
        log.info("收到自然语言查询请求: {}", request.getQuery());
        return naturalLanguageQueryService.processQuery(request);
    }
    
    /**
     * 获取数据库元数据
     *
     * @param dataSourceType 数据源类型（可选）
     * @param dataSourceId   数据源ID（可选）
     * @return 数据库元数据
     * @throws RootException 如果发生错误
     */
    @GetMapping("/metadata")
    public DatabaseMetadata getDatabaseMetadata(
                                                @RequestParam(required = false) String dataSourceType,
                                                @RequestParam(required = false) Long dataSourceId) {
        try {
            log.info("获取数据库元数据 - 数据源ID: {}, 数据源类型: {}", dataSourceId, dataSourceType);
            
            if (dataSourceId != null) {
                // 优先使用数据源ID
                return naturalLanguageQueryService.getDatabaseMetadata(dataSourceId);
            } else {
                // 使用默认数据源
                return naturalLanguageQueryService.getDatabaseMetadata();
            }
        } catch (Exception e) {
            log.error("获取数据库元数据时发生错误", e);
            throw new RootException("获取数据库元数据时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 分页获取数据库元数据
     *
     * @param dataSourceType 数据源类型（可选）
     * @param dataSourceId   数据源ID（可选）
     * @param pageNum        页码
     * @param pageSize       每页大小
     * @return 分页数据库元数据
     * @throws RootException 如果发生错误
     */
    @GetMapping("/metadata/page")
    public Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(
                                                                        @RequestParam(required = false) String dataSourceType,
                                                                        @RequestParam(required = false) Long dataSourceId,
                                                                        @RequestParam(defaultValue = "1") int pageNum, @RequestParam(defaultValue = "10") int pageSize) {
        try {
            log.info("分页获取数据库元数据 - 数据源ID: {}, 数据源类型: {}, 页码: {}, 每页大小: {}",
                    dataSourceId, dataSourceType, pageNum, pageSize);
            
            if (dataSourceId != null) {
                // 优先使用数据源ID
                return naturalLanguageQueryService.getDatabaseMetadataPage(dataSourceId, pageNum, pageSize);
            } else {
                // 使用默认数据源
                return naturalLanguageQueryService.getDatabaseMetadataPage(pageNum, pageSize);
            }
        } catch (Exception e) {
            log.error("分页获取数据库元数据时发生错误", e);
            throw new RootException("分页获取数据库元数据时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 清除查询缓存
     *
     * @return 处理结果
     * @throws RootException 如果发生错误
     */
    @PostMapping("/clear-cache")
    public ConversationResponse clearCache() {
        try {
            naturalLanguageQueryService.clearCache();
            return ConversationResponse.builder()
                    .message("查询缓存已清除")
                    .build();
        } catch (Exception e) {
            log.error("清除查询缓存时发生错误", e);
            throw new RootException("清除查询缓存时发生错误: " + e.getMessage(),
                    String.valueOf(ResponseCode.INTERNAL_ERROR.getCode()));
        }
    }
    
    /**
     * 在指定会话中进行查询
     *
     * @param request        查询请求
     * @param conversationId 会话ID
     * @return 查询响应
     */
    @PostMapping("/conversation-query")
    public NlQueryResponse conversationQuery(
                                             @RequestBody NlQueryRequest request,
                                             @RequestParam String conversationId) {
        log.info("在会话 {} 中进行查询: {}, 用户ID: {}", conversationId, request.getQuery(), request.getUserId());
        return naturalLanguageQueryService.processConversationQuery(request, conversationId);
    }

}