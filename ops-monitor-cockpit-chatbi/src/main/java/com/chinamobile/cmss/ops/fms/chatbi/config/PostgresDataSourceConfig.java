/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.config;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * PostgreSQL数据源配置
 * 配置PostgreSQL数据库连接和MyBatis相关设置
 */
@Configuration
@MapperScan(basePackages = "com.chinamobile.cmss.ops.fms.chatbi.mapper",
        sqlSessionTemplateRef = "postgresSqlSessionTemplate")
public class PostgresDataSourceConfig {
    
    @Value("${spring.datasource.postgres.url}")
    private String url;
    
    @Value("${spring.datasource.postgres.username}")
    private String username;
    
    @Value("${spring.datasource.postgres.password}")
    private String password;
    
    @Value("${spring.datasource.postgres.driver-class-name}")
    private String driverClassName;
    
    /**
     * 创建PostgreSQL数据源
     *
     * @return PostgreSQL数据源
     */
    @Bean(name = "postgresDataSource")
    @Primary
    public DataSource postgresDataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setDriverClassName(driverClassName);
        return dataSource;
    }
    
    /**
     * 创建PostgreSQL SqlSessionFactory
     *
     * @param dataSource PostgreSQL数据源
     * @return SqlSessionFactory
     * @throws Exception 异常
     */
    @Bean(name = "postgresSqlSessionFactory")
    @Primary
    public SqlSessionFactory postgresSqlSessionFactory(@Qualifier("postgresDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        // 设置Mapper XML文件位置
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/chatbi/*.xml"));
        
        // 确保MyBatis-Plus能够找到XML文件
        
        // 配置MyBatis全局配置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setCacheEnabled(false);
        configuration.setLogImpl(org.apache.ibatis.logging.stdout.StdOutImpl.class);
        configuration.setDefaultEnumTypeHandler(org.apache.ibatis.type.EnumOrdinalTypeHandler.class);
        configuration.setJdbcTypeForNull(org.apache.ibatis.type.JdbcType.NULL);
        
        // 添加插件
        bean.setPlugins(mybatisPlusInterceptor());
        bean.setConfiguration(configuration);
        
        // 设置别名包
        bean.setTypeAliasesPackage("com.chinamobile.cmss.ops.fms.chatbi.entity");
        
        return bean.getObject();
    }
    
    /**
     * 创建MyBatis-Plus拦截器
     *
     * @return MybatisPlusInterceptor
     */
    private com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor mybatisPlusInterceptor() {
        com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor interceptor = new com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor();
        // 添加分页插件
        interceptor.addInnerInterceptor(new com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor(com.baomidou.mybatisplus.annotation.DbType.POSTGRE_SQL));
        return interceptor;
    }
    
    /**
     * 创建PostgreSQL SqlSessionTemplate
     *
     * @param sqlSessionFactory PostgreSQL SqlSessionFactory
     * @return SqlSessionTemplate
     */
    @Bean(name = "postgresSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate postgresSqlSessionTemplate(@Qualifier("postgresSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
