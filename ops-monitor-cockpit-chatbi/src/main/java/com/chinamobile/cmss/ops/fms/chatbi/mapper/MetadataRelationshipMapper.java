/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataRelationship;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 元数据关系Mapper接口
 */
@Mapper
public interface MetadataRelationshipMapper extends BaseMapper<MetadataRelationship> {
    
    /**
     * 查询所有启用的表关系
     *
     * @return 表关系列表
     */
    List<MetadataRelationship> selectEnabledRelationships();
}
