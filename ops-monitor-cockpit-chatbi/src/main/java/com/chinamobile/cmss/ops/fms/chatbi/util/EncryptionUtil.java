/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Base64;

/**
 * 加密工具类
 * 用于敏感数据的加密和解密
 */
@Component
@Slf4j
public class EncryptionUtil {
    
    /**
     * 加密算法
     */
    private static final String ALGORITHM = "AES";
    
    /**
     * 加密模式
     */
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    
    /**
     * 加密密钥
     */
    private String encryptionKey;
    
    /**
     * 设置加密密钥
     *
     * @param key 密钥
     */
    @Value("${encryption.key:cmss_ops_monitor_default_key}")
    public void setEncryptionKey(String key) {
        // 确保密钥长度为16位（AES-128）
        String processedKey = key;
        if (processedKey.length() < 16) {
            processedKey = String.format("%-16s", processedKey).replace(' ', '0');
        } else if (processedKey.length() > 16) {
            processedKey = processedKey.substring(0, 16);
        }
        encryptionKey = processedKey;
    }
    
    /**
     * 加密字符串
     *
     * @param plainText 明文
     * @return 密文
     */
    public String encrypt(String plainText) {
        if (plainText == null || plainText.isEmpty()) {
            return plainText;
        }
        
        try {
            Key key = new SecretKeySpec(encryptionKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("加密失败", e);
            return plainText;
        }
    }
    
    /**
     * 解密字符串
     *
     * @param encryptedText 密文
     * @return 明文
     */
    public String decrypt(String encryptedText) {
        if (encryptedText == null || encryptedText.isEmpty()) {
            return encryptedText;
        }
        
        try {
            Key key = new SecretKeySpec(encryptionKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] decodedBytes = Base64.getDecoder().decode(encryptedText);
            byte[] decryptedBytes = cipher.doFinal(decodedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("解密失败", e);
            return encryptedText;
        }
    }
    
    /**
     * 判断字符串是否已加密
     *
     * @param text 待检查的字符串
     * @return 是否已加密
     */
    public boolean isEncrypted(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        try {
            // 尝试解码Base64
            byte[] decoded = Base64.getDecoder().decode(text);
            
            // 检查解码后的数据长度，加密后的数据应该有一定的长度
            if (decoded.length < 8) {
                return false;
            }
            
            // 尝试解密，如果能解密则认为是加密过的
            try {
                Key key = new SecretKeySpec(encryptionKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
                Cipher cipher = Cipher.getInstance(TRANSFORMATION);
                cipher.init(Cipher.DECRYPT_MODE, key);
                cipher.doFinal(decoded);
                return true;
            } catch (Exception e) {
                // 解密失败，说明不是用当前密钥加密的
                return false;
            }
        } catch (IllegalArgumentException e) {
            // 解码失败，说明不是有效的Base64编码，即未加密
            return false;
        }
    }
}
