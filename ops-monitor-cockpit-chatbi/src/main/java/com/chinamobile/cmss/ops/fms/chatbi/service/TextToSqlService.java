/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.service;

import com.chinamobile.cmss.ops.fms.chatbi.model.DatabaseMetadata;
import com.chinamobile.cmss.ops.fms.chatbi.model.TextToSqlResult;

/**
 * 文本到SQL转换服务接口
 */
public interface TextToSqlService {

    /**
     * 生成SQL查询
     *
     * @param naturalLanguageQuery 自然语言查询
     * @param databaseMetadata 数据库元数据
     * @return 文本到SQL转换结果
     */
    TextToSqlResult generateSql(String naturalLanguageQuery, DatabaseMetadata databaseMetadata);

    /**
     * 生成SQL查询（使用Spring AI会话上下文）
     *
     * @param naturalLanguageQuery 自然语言查询
     * @param databaseMetadata 数据库元数据
     * @param conversationId 会话ID
     * @return 文本到SQL转换结果
     */
    TextToSqlResult generateSqlWithContext(String naturalLanguageQuery,
                                           DatabaseMetadata databaseMetadata,
                                           String conversationId);

    /**
     * 清除查询缓存
     */
    void clearCache();
}
