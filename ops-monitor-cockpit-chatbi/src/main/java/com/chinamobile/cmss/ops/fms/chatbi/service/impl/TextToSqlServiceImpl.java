/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.service.impl;

import com.chinamobile.cmss.ops.fms.chatbi.model.DatabaseMetadata;
import com.chinamobile.cmss.ops.fms.chatbi.model.TextToSqlResult;

import com.chinamobile.cmss.ops.fms.chatbi.service.TextToSqlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文本到SQL转换服务实现类
 */
@Service
@Slf4j
public class TextToSqlServiceImpl implements TextToSqlService, CommandLineRunner {
    
    private final ChatClient chatClient;

    private final ResourceLoader resourceLoader;
    
    private String promptTemplate;
    
    // 查询缓存，用于存储相同或类似查询的结果
    private final Map<String, TextToSqlResult> queryCache = new ConcurrentHashMap<>();
    
    @Value("${app.chatbi.prompt-template-file:classpath:prompts/text-to-sql-prompt.txt}")
    private String promptTemplateFile;
    
    public TextToSqlServiceImpl(ChatClient chatClient, ResourceLoader resourceLoader) {
        this.chatClient = chatClient;
        this.resourceLoader = resourceLoader;
    }
    
    @Override
    public void run(String... args) throws Exception {
        loadPromptTemplates();
    }
    
    /**
     * 加载提示词模板
     */
    private void loadPromptTemplates() {
        try {
            // 加载SQL生成提示词模板
            Resource promptResource = resourceLoader.getResource(promptTemplateFile);
            Reader promptReader = new InputStreamReader(promptResource.getInputStream(), StandardCharsets.UTF_8);
            promptTemplate = FileCopyUtils.copyToString(promptReader);
            
            log.info("提示词模板加载成功");
        } catch (IOException e) {
            log.error("加载提示词模板失败", e);
        }
    }
    
    /**
     * 生成SQL查询
     *
     * @param naturalLanguageQuery 自然语言查询
     * @param databaseMetadata 数据库元数据
     * @return 文本到SQL转换结果
     */
    @Override
    public TextToSqlResult generateSql(String naturalLanguageQuery, DatabaseMetadata databaseMetadata) {
        if (naturalLanguageQuery == null || naturalLanguageQuery.trim().isEmpty()) {
            return TextToSqlResult.builder()
                    .success(false)
                    .errorMessage("查询不能为空")
                    .build();
        }
        
        // 检查缓存
        String cacheKey = normalizeQuery(naturalLanguageQuery) + "_" + databaseMetadata.getDatabaseType();
        if (queryCache.containsKey(cacheKey)) {
            log.info("从缓存中获取SQL查询结果: {}", naturalLanguageQuery);
            return queryCache.get(cacheKey);
        }

        try {
            // 使用ChatClient的fluent API生成SQL
            String content = chatClient.prompt()
                    .user(u -> u.text(promptTemplate)
                            .param("database_schema", databaseMetadata.toString())
                            .param("user_query", naturalLanguageQuery)
                            .param("database_type", databaseMetadata.getDatabaseType().toUpperCase()))
                    .call()
                    .content();
            
            // 从响应中提取SQL查询
            String generatedSql = extractSqlFromResponse(content);
            
            if (generatedSql == null || generatedSql.isEmpty()) {
                TextToSqlResult result = TextToSqlResult.builder()
                        .naturalLanguageQuery(naturalLanguageQuery)
                        .success(false)
                        .errorMessage("无法从LLM响应中提取SQL查询")
                        .build();
                return result;
            }
            
            // 验证SQL语法
            String validatedSql = validateAndFormatSql(generatedSql);
            
            TextToSqlResult result = TextToSqlResult.builder()
                    .naturalLanguageQuery(naturalLanguageQuery)
                    .sql(validatedSql)
                    .rawResponse(content)
                    .success(true)
                    .build();
            
            // 将结果存入缓存
            queryCache.put(cacheKey, result);
            
            return result;
        } catch (Exception e) {
            log.error("生成SQL查询时发生错误", e);
            return TextToSqlResult.builder()
                    .naturalLanguageQuery(naturalLanguageQuery)
                    .success(false)
                    .errorMessage("生成SQL查询时发生错误: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 从LLM响应中提取SQL查询
     *
     * @param response LLM响应
     * @return 提取的SQL查询
     */
    private String extractSqlFromResponse(String response) {
        // 尝试从代码块中提取SQL
        Pattern pattern = Pattern.compile("```(?:sql)?\\s*(.*?)\\s*```", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(response);
        
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        
        // 如果没有找到代码块，尝试提取各种SQL语句
        // 支持SELECT、INSERT、UPDATE、DELETE、CREATE、ALTER等常见SQL语句
        String sqlPatterns = "(?:SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP|TRUNCATE|SHOW|DESCRIBE|USE|EXPLAIN)\\s+.*?;";
        pattern = Pattern.compile(sqlPatterns, Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        matcher = pattern.matcher(response);
        
        if (matcher.find()) {
            return matcher.group(0).trim();
        }
        
        // 如果仍然没有找到，返回整个响应
        return response.trim();
    }
    
    /**
     * 验证并格式化SQL查询
     *
     * @param sql 原始SQL查询
     * @return 格式化后的SQL查询
     */
    private String validateAndFormatSql(String sql) {
        // 移除多余的空白字符
        String formattedSql = sql.replaceAll("\\s+", " ").trim();
        
        // 确保SQL以分号结尾
        if (!formattedSql.endsWith(";")) {
            formattedSql = formattedSql + ";";
        }
        
        // 实现更复杂的SQL验证逻辑
        // 可以使用SQL解析器或者尝试在测试环境中执行SQL来验证
        
        return formattedSql;
    }
    
    /**
     * 规范化查询文本，用于缓存键
     *
     * @param query 原始查询
     * @return 规范化后的查询
     */
    private String normalizeQuery(String query) {
        // 移除多余的空白字符
        String normalizedQuery = query.replaceAll("\\s+", " ").trim().toLowerCase();
        
        // 移除标点符号
        normalizedQuery = normalizedQuery.replaceAll("[,.?!;:]", "");
        
        return normalizedQuery;
    }
    
    /**
     * 生成SQL查询（使用Spring AI会话上下文）
     *
     * @param naturalLanguageQuery 自然语言查询
     * @param databaseMetadata 数据库元数据
     * @param conversationId 会话ID
     * @return 文本到SQL转换结果
     */
    @Override
    public TextToSqlResult generateSqlWithContext(String naturalLanguageQuery,
                                                  DatabaseMetadata databaseMetadata,
                                                  String conversationId) {
        if (naturalLanguageQuery == null || naturalLanguageQuery.trim().isEmpty()) {
            return TextToSqlResult.builder()
                    .success(false)
                    .errorMessage("查询不能为空")
                    .build();
        }
        
        // 检查缓存
        String cacheKey = normalizeQuery(naturalLanguageQuery) + "_" + databaseMetadata.getDatabaseType() + "_context_" + conversationId;
        if (queryCache.containsKey(cacheKey)) {
            log.info("从缓存中获取上下文SQL查询结果: {}, 会话: {}", naturalLanguageQuery, conversationId);
            return queryCache.get(cacheKey);
        }

        try {
            // 使用Spring AI 1.0.0的ChatClient，会话上下文由MessageChatMemoryAdvisor自动管理
            String content = chatClient.prompt()
                    .user(u -> u.text(promptTemplate)
                            .param("database_schema", databaseMetadata.toString())
                            .param("user_query", naturalLanguageQuery)
                            .param("database_type", databaseMetadata.getDatabaseType().toUpperCase()))
                    .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
                    .call()
                    .content();

            log.info("使用Spring AI 1.0.0原生会话管理生成SQL查询, 会话ID: {}", conversationId);
            
            // 从响应中提取SQL查询
            String generatedSql = extractSqlFromResponse(content);
            
            if (generatedSql == null || generatedSql.isEmpty()) {
                TextToSqlResult result = TextToSqlResult.builder()
                        .naturalLanguageQuery(naturalLanguageQuery)
                        .success(false)
                        .errorMessage("无法从LLM响应中提取SQL查询")
                        .build();
                return result;
            }
            
            // 验证SQL语法
            String validatedSql = validateAndFormatSql(generatedSql);

            // Spring AI会自动管理会话上下文，无需手动添加消息

            TextToSqlResult result = TextToSqlResult.builder()
                    .naturalLanguageQuery(naturalLanguageQuery)
                    .sql(validatedSql)
                    .rawResponse(content)
                    .success(true)
                    .build();
            
            // 将结果存入缓存
            queryCache.put(cacheKey, result);
            
            return result;
        } catch (Exception e) {
            log.error("生成上下文SQL查询时发生错误", e);
            return TextToSqlResult.builder()
                    .naturalLanguageQuery(naturalLanguageQuery)
                    .success(false)
                    .errorMessage("生成SQL查询时发生错误: " + e.getMessage())
                    .build();
        }
    }
    
    @Override
    public void clearCache() {
        queryCache.clear();
        log.info("查询缓存已清除");
    }
}
