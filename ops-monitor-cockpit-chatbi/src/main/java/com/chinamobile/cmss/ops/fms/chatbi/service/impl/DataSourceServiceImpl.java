/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.cmss.ops.fms.chatbi.dto.DataSourceConfigDTO;
import com.chinamobile.cmss.ops.fms.chatbi.entity.DataSourceConfigEntity;
import com.chinamobile.cmss.ops.fms.chatbi.mapper.DataSourceConfigMapper;
import com.chinamobile.cmss.ops.fms.chatbi.model.DatabaseMetadata;
import com.chinamobile.cmss.ops.fms.chatbi.service.DataSourceService;
import com.chinamobile.cmss.ops.fms.chatbi.service.MetadataService;
import com.chinamobile.cmss.ops.fms.chatbi.util.EncryptionUtil;
import com.chinamobile.cmss.ops.monitor.cockpit.common.enums.ResponseCode;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.RootException;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一的数据源服务实现
 * 合并了原来的DatabaseConnectionService、JdbcDataSourceService、DynamicDatabaseConnectionServiceFactory和DataSourceConfigService的功能
 */
@Service
@Slf4j
public class DataSourceServiceImpl implements DataSourceService {

    @Autowired
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Autowired
    private MetadataService metadataService;

    @Autowired
    private EncryptionUtil encryptionUtil;

    // 数据源连接池缓存
    private final Map<Long, HikariDataSource> dataSourceCache = new ConcurrentHashMap<>();
    
    // ============================================================================
    // 数据源基本信息
    // ============================================================================
    
    @Override
    public String getDataSourceName(Long dataSourceId) {
        DataSourceConfigEntity config = getDataSourceConfig(dataSourceId);
        return config.getName();
    }

    @Override
    public String getDataSourceType(Long dataSourceId) {
        DataSourceConfigEntity config = getDataSourceConfig(dataSourceId);
        return config.getType();
    }

    @Override
    public List<Map<String, String>> getAllDataSources() {
        List<Map<String, String>> result = new ArrayList<>();
        List<DataSourceConfigEntity> configEntities = getAllEnabledConfigs();
        DataSourceConfigEntity defaultConfig = getDefaultConfig();

        for (DataSourceConfigEntity config : configEntities) {
            Map<String, String> map = new HashMap<>();
            map.put("id", String.valueOf(config.getId()));
            map.put("name", config.getName());
            map.put("type", config.getType());
            map.put("isDefault", String.valueOf(defaultConfig != null && config.getId().equals(defaultConfig.getId())));
            result.add(map);
        }
        return result;
    }
    
    // ============================================================================
    // SQL执行
    // ============================================================================
    
    @Override
    public List<Map<String, Object>> executeQuery(String sql) {
        DataSourceConfigEntity defaultConfig = getDefaultConfig();
        if (defaultConfig == null) {
            throw new RootException("未找到默认数据源", String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
        }
        return executeQuery(defaultConfig.getId(), sql);
    }
    
    @Override
    public List<Map<String, Object>> executeQuery(Long dataSourceId, String sql) {
        // 基本的SQL验证，只允许SELECT语句
        validateSql(sql);
        
        try {
            DataSourceConfigEntity config = getDataSourceConfig(dataSourceId);
            log.info("使用数据源 [{}] 执行SQL查询: {}", config.getName(), sql);
            
            DataSource dataSource = getDataSource(config);
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            
            return jdbcTemplate.queryForList(sql);
        } catch (Exception e) {
            log.error("执行SQL查询失败: {}", e.getMessage(), e);
            throw new RuntimeException("执行SQL查询失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<Map<String, Object>> executeQueryByType(String dataSourceType, String sql) {
        DataSourceConfigEntity config = getConfigByType(dataSourceType);
        if (config == null) {
            throw new RootException("未找到类型为 " + dataSourceType + " 的数据源",
                    String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
        }
        return executeQuery(config.getId(), sql);
    }
    
    // ============================================================================
    // 元数据管理
    // ============================================================================
    
    @Override
    public DatabaseMetadata getDatabaseMetadata() {
        DataSourceConfigEntity defaultConfig = getDefaultConfig();
        if (defaultConfig == null) {
            throw new RootException("未找到默认数据源", String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
        }
        return getDatabaseMetadata(defaultConfig.getId());
    }
    
    @Override
    public DatabaseMetadata getDatabaseMetadata(Long dataSourceId) {
        try {
            log.info("获取数据源 [{}] 的数据库元数据", getDataSourceName(dataSourceId));
            return metadataService.getDatabaseMetadata(dataSourceId);
        } catch (Exception e) {
            log.error("获取数据库元数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取数据库元数据失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(int pageNum, int pageSize) {
        DataSourceConfigEntity defaultConfig = getDefaultConfig();
        if (defaultConfig == null) {
            throw new RootException("未找到默认数据源", String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
        }
        return getDatabaseMetadataPage(defaultConfig.getId(), pageNum, pageSize);
    }
    
    @Override
    public Page<DatabaseMetadata.TableMetadata> getDatabaseMetadataPage(Long dataSourceId, int pageNum, int pageSize) {
        try {
            log.info("分页获取数据源 [{}] 的数据库元数据, 页码: {}, 每页大小: {}", 
                    getDataSourceName(dataSourceId), pageNum, pageSize);
            return metadataService.getDatabaseMetadataPage(dataSourceId, pageNum, pageSize);
        } catch (Exception e) {
            log.error("分页获取数据库元数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("分页获取数据库元数据失败: " + e.getMessage(), e);
        }
    }
    
    // ============================================================================
    // 连接测试
    // ============================================================================
    
    @Override
    public boolean testConnection(DataSourceConfigEntity config) {
        return testConnection(config.getJdbcUrl(), config.getUsername(), 
                config.getPassword(), config.getDriverClassName());
    }
    
    @Override
    public boolean testConnection(String jdbcUrl, String username, String password, String driverClassName) {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(jdbcUrl);
        
        // 解密用户名和密码
        String decryptedUsername = username;
        if (decryptedUsername != null && encryptionUtil != null && encryptionUtil.isEncrypted(decryptedUsername)) {
            decryptedUsername = encryptionUtil.decrypt(decryptedUsername);
        }
        
        String decryptedPassword = password;
        if (decryptedPassword != null && encryptionUtil != null && encryptionUtil.isEncrypted(decryptedPassword)) {
            decryptedPassword = encryptionUtil.decrypt(decryptedPassword);
        }
        
        config.setUsername(decryptedUsername);
        config.setPassword(decryptedPassword);
        config.setDriverClassName(driverClassName);
        config.setMaximumPoolSize(1);
        config.setMinimumIdle(1);
        config.setConnectionTimeout(5000);
        config.setIdleTimeout(10000);
        config.setMaxLifetime(20000);
        
        try (HikariDataSource dataSource = new HikariDataSource(config);
             Connection conn = dataSource.getConnection()) {
            return true;
        } catch (Exception e) {
            log.error("测试数据源连接失败", e);
            return false;
        }
    }
    
    // ============================================================================
    // 数据源配置管理 (合并自DataSourceConfigService)
    // ============================================================================

    @Override
    public List<DataSourceConfigEntity> getAllEnabledConfigs() {
        List<DataSourceConfigEntity> configs = dataSourceConfigMapper.selectAllEnabled();
        configs.forEach(this::decryptSensitiveFields);
        return configs;
    }

    @Override
    public DataSourceConfigEntity getDefaultConfig() {
        DataSourceConfigEntity config = dataSourceConfigMapper.selectDefault();
        if (config != null) {
            decryptSensitiveFields(config);
        }
        return config;
    }

    @Override
    public DataSourceConfigEntity getConfigByType(String type) {
        DataSourceConfigEntity config = dataSourceConfigMapper.selectByType(type);
        if (config != null) {
            decryptSensitiveFields(config);
        }
        return config;
    }

    @Override
    public DataSourceConfigEntity getConfigById(Long id) {
        DataSourceConfigEntity config = dataSourceConfigMapper.selectById(id);
        if (config != null) {
            decryptSensitiveFields(config);
        }
        return config;
    }

    @Override
    public DataSourceConfigEntity getConfigByName(String name) {
        DataSourceConfigEntity config = dataSourceConfigMapper.selectByName(name);
        if (config != null) {
            decryptSensitiveFields(config);
        }
        return config;
    }

    @Override
    public Page<DataSourceConfigEntity> getConfigPage(int page, int pageSize) {
        Page<DataSourceConfigEntity> pageParam = new Page<>(page, pageSize);
        Page<DataSourceConfigEntity> resultPage = dataSourceConfigMapper.selectPage(pageParam, null);
        resultPage.getRecords().forEach(this::decryptSensitiveFields);
        return resultPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataSourceConfigEntity createConfig(DataSourceConfigEntity config) {
        // 检查名称是否已存在
        DataSourceConfigEntity existingConfig = dataSourceConfigMapper.selectByName(config.getName());
        if (existingConfig != null) {
            throw new RootException("数据源名称已存在", String.valueOf(ResponseCode.PARAM_ERROR.getCode()));
        }

        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        config.setCreateTime(now);
        config.setUpdateTime(now);

        // 如果设置为默认数据源，先清除其他默认标记
        if (Boolean.TRUE.equals(config.getIsDefault())) {
            dataSourceConfigMapper.clearAllDefault();
        }

        // 加密敏感字段
        encryptSensitiveFields(config);

        // 插入数据库
        dataSourceConfigMapper.insert(config);

        // 清除缓存
        clearAllCache();

        // 返回解密后的配置
        decryptSensitiveFields(config);
        return config;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataSourceConfigEntity updateConfig(DataSourceConfigEntity config) {
        // 检查数据源是否存在
        DataSourceConfigEntity existingConfig = dataSourceConfigMapper.selectById(config.getId());
        if (existingConfig == null) {
            throw new RootException("数据源不存在", String.valueOf(ResponseCode.PARAM_ERROR.getCode()));
        }

        // 检查名称是否已被其他数据源使用
        DataSourceConfigEntity nameConfig = dataSourceConfigMapper.selectByName(config.getName());
        if (nameConfig != null && !nameConfig.getId().equals(config.getId())) {
            throw new RootException("数据源名称已被其他数据源使用", String.valueOf(ResponseCode.PARAM_ERROR.getCode()));
        }

        // 设置更新时间
        config.setUpdateTime(LocalDateTime.now());

        // 如果设置为默认数据源，先清除其他默认标记
        if (Boolean.TRUE.equals(config.getIsDefault())) {
            dataSourceConfigMapper.clearAllDefault();
        }

        // 加密敏感字段
        encryptSensitiveFields(config);

        // 更新数据库
        dataSourceConfigMapper.updateById(config);

        // 清除缓存
        clearAllCache();

        // 返回解密后的配置
        decryptSensitiveFields(config);
        return config;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteConfig(Long id) {
        DataSourceConfigEntity config = dataSourceConfigMapper.selectById(id);
        if (config == null) {
            return false;
        }

        // 不允许删除默认数据源
        if (Boolean.TRUE.equals(config.getIsDefault())) {
            throw new RootException("不能删除默认数据源", String.valueOf(ResponseCode.PARAM_ERROR.getCode()));
        }

        int result = dataSourceConfigMapper.deleteById(id);
        if (result > 0) {
            clearAllCache();
        }
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDefaultConfig(Long id) {
        DataSourceConfigEntity config = dataSourceConfigMapper.selectById(id);
        if (config == null) {
            throw new RootException("数据源不存在", String.valueOf(ResponseCode.PARAM_ERROR.getCode()));
        }

        if (!Boolean.TRUE.equals(config.getEnabled())) {
            throw new RootException("不能将禁用的数据源设置为默认", String.valueOf(ResponseCode.PARAM_ERROR.getCode()));
        }

        // 清除所有默认标记
        dataSourceConfigMapper.clearAllDefault();

        // 设置新的默认数据源
        int result = dataSourceConfigMapper.setDefault(id);
        if (result > 0) {
            clearAllCache();
        }
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setConfigEnabled(Long id, boolean enabled) {
        DataSourceConfigEntity config = dataSourceConfigMapper.selectById(id);
        if (config == null) {
            throw new RootException("数据源不存在", String.valueOf(ResponseCode.PARAM_ERROR.getCode()));
        }

        // 不允许禁用默认数据源
        if (!enabled && Boolean.TRUE.equals(config.getIsDefault())) {
            throw new RootException("不能禁用默认数据源", String.valueOf(ResponseCode.PARAM_ERROR.getCode()));
        }

        int result = dataSourceConfigMapper.setEnabled(id, enabled);
        if (result > 0) {
            clearAllCache();
        }
        return result > 0;
    }

    @Override
    public DataSourceConfigDTO getEncryptedConfigById(Long id) {
        DataSourceConfigEntity config = dataSourceConfigMapper.selectById(id);
        if (config == null) {
            return null;
        }

        // 确保敏感字段已加密
        encryptSensitiveFields(config);
        return DataSourceConfigDTO.fromEntity(config);
    }

    @Override
    public Page<DataSourceConfigDTO> getEncryptedConfigPage(int page, int pageSize) {
        Page<DataSourceConfigEntity> pageParam = new Page<>(page, pageSize);
        Page<DataSourceConfigEntity> resultPage = dataSourceConfigMapper.selectPage(pageParam, null);

        Page<DataSourceConfigDTO> dtoPage = new Page<>(page, pageSize);
        dtoPage.setTotal(resultPage.getTotal());
        dtoPage.setPages(resultPage.getPages());

        List<DataSourceConfigDTO> dtoList = resultPage.getRecords().stream()
                .map(entity -> {
                    // 确保敏感字段已加密
                    encryptSensitiveFields(entity);
                    return DataSourceConfigDTO.fromEntity(entity);
                })
                .toList();

        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public List<DataSourceConfigDTO> getAllEnabledEncryptedConfigs() {
        List<DataSourceConfigEntity> entities = dataSourceConfigMapper.selectAllEnabled();

        return entities.stream()
                .map(entity -> {
                    // 确保敏感字段已加密
                    encryptSensitiveFields(entity);
                    return DataSourceConfigDTO.fromEntity(entity);
                })
                .toList();
    }

    // ============================================================================
    // 缓存管理
    // ============================================================================
    
    @Override
    public void clearCache(Long dataSourceId) {
        log.info("清除数据源 [{}] 的缓存", dataSourceId);
        
        // 清除连接池缓存
        HikariDataSource dataSource = dataSourceCache.remove(dataSourceId);
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
        }
        
        // 清除元数据缓存
        metadataService.clearDatabaseMetadataCache(dataSourceId);
    }
    
    @Override
    public void clearAllCache() {
        log.info("清除所有数据源缓存");
        
        // 清除所有连接池缓存
        for (Map.Entry<Long, HikariDataSource> entry : dataSourceCache.entrySet()) {
            HikariDataSource dataSource = entry.getValue();
            if (dataSource != null && !dataSource.isClosed()) {
                dataSource.close();
            }
        }
        dataSourceCache.clear();
        
        // 清除所有元数据缓存
        metadataService.clearAllDatabaseMetadataCache();
    }
    
    // ============================================================================
    // 私有辅助方法
    // ============================================================================
    
    /**
     * 获取数据源配置
     */
    private DataSourceConfigEntity getDataSourceConfig(Long dataSourceId) {
        DataSourceConfigEntity config = getConfigById(dataSourceId);
        if (config == null) {
            throw new RootException("数据源不存在: " + dataSourceId,
                    String.valueOf(ResponseCode.RESOURCE_NOT_FOUND.getCode()));
        }

        if (!Boolean.TRUE.equals(config.getEnabled())) {
            throw new RootException("数据源已禁用: " + config.getName(),
                    String.valueOf(ResponseCode.FORBIDDEN.getCode()));
        }

        return config;
    }

    /**
     * 加密敏感字段
     */
    private void encryptSensitiveFields(DataSourceConfigEntity config) {
        if (config == null) {
            return;
        }

        try {
            // 加密用户名
            if (config.getUsername() != null && !encryptionUtil.isEncrypted(config.getUsername())) {
                config.setUsername(encryptionUtil.encrypt(config.getUsername()));
            }

            // 加密密码
            if (config.getPassword() != null && !encryptionUtil.isEncrypted(config.getPassword())) {
                config.setPassword(encryptionUtil.encrypt(config.getPassword()));
            }
        } catch (Exception e) {
            log.error("加密敏感字段失败", e);
            throw new RuntimeException("加密敏感字段失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解密敏感字段
     */
    private void decryptSensitiveFields(DataSourceConfigEntity config) {
        if (config == null) {
            return;
        }

        try {
            // 解密用户名
            if (config.getUsername() != null && encryptionUtil.isEncrypted(config.getUsername())) {
                config.setUsername(encryptionUtil.decrypt(config.getUsername()));
            }

            // 解密密码
            if (config.getPassword() != null && encryptionUtil.isEncrypted(config.getPassword())) {
                config.setPassword(encryptionUtil.decrypt(config.getPassword()));
            }
        } catch (Exception e) {
            log.error("解密敏感字段失败", e);
            throw new RuntimeException("解密敏感字段失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取数据源连接池
     */
    private DataSource getDataSource(DataSourceConfigEntity config) {
        // 如果缓存中存在，直接返回
        if (dataSourceCache.containsKey(config.getId())) {
            HikariDataSource dataSource = dataSourceCache.get(config.getId());
            // 检查连接池是否关闭
            if (!dataSource.isClosed()) {
                return dataSource;
            } else {
                // 如果连接池已关闭，从缓存中移除
                dataSourceCache.remove(config.getId());
            }
        }
        
        // 创建新的连接池
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(config.getJdbcUrl());
        
        // 解密用户名和密码
        String username = config.getUsername();
        String password = config.getPassword();
        
        if (username != null && encryptionUtil != null && encryptionUtil.isEncrypted(username)) {
            username = encryptionUtil.decrypt(username);
        }
        
        if (password != null && encryptionUtil != null && encryptionUtil.isEncrypted(password)) {
            password = encryptionUtil.decrypt(password);
        }
        
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(password);
        hikariConfig.setDriverClassName(config.getDriverClassName());
        
        // 设置连接池参数
        hikariConfig.setMaximumPoolSize(config.getMaxPoolSize() != null ? config.getMaxPoolSize() : 10);
        hikariConfig.setMinimumIdle(config.getMinIdle() != null ? config.getMinIdle() : 2);
        hikariConfig.setConnectionTimeout(30000);
        hikariConfig.setIdleTimeout(600000);
        hikariConfig.setMaxLifetime(1800000);
        
        // 创建连接池
        HikariDataSource dataSource = new HikariDataSource(hikariConfig);
        
        // 缓存连接池
        dataSourceCache.put(config.getId(), dataSource);
        
        return dataSource;
    }
    
    /**
     * 验证SQL语句
     */
    private void validateSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new IllegalArgumentException("查询SQL不能为空");
        }
        
        String trimmedSql = sql.trim().toLowerCase();
        if (!trimmedSql.startsWith("select")) {
            throw new IllegalArgumentException("只允许执行SELECT查询");
        }
    }
}
