/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.cmss.ops.fms.chatbi.entity.MetadataTable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 元数据表Mapper接口
 */
@Mapper
public interface MetadataTableMapper extends BaseMapper<MetadataTable> {
    
    /**
     * 分页查询启用的元数据表
     *
     * @param page 分页参数
     * @return 分页结果
     */
    Page<MetadataTable> selectEnabledTablesPage(Page<MetadataTable> page);
    
    /**
     * 查询所有启用的元数据表
     *
     * @return 元数据表列表
     */
    List<MetadataTable> selectEnabledTables();
    
    /**
     * 根据数据库名称查询启用的元数据表
     *
     * @param databaseName 数据库名称
     * @return 元数据表列表
     */
    List<MetadataTable> selectEnabledTablesByDatabase(@Param("databaseName") String databaseName);
    
    /**
     * 根据数据源ID和数据库名称查询启用的元数据表
     *
     * @param dataSourceId 数据源ID
     * @param databaseName 数据库名称
     * @return 元数据表列表
     */
    List<MetadataTable> selectEnabledTablesByDataSourceAndDatabase(
                                                                   @Param("dataSourceId") Long dataSourceId,
                                                                   @Param("databaseName") String databaseName);
}
