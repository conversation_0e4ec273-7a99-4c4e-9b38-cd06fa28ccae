/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.chatbi.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 数据库元数据模型
 * 用于存储数据库表结构信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatabaseMetadata {
    
    /**
     * 数据库名称
     */
    private String databaseName;
    
    /**
     * 数据库类型
     */
    private String databaseType;
    
    /**
     * 表列表
     */
    private List<TableMetadata> tables;
    
    /**
     * 表之间的关系
     */
    private List<TableRelationship> relationships;
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("数据库名称: ").append(databaseName).append("\n");
        sb.append("数据库类型: ").append(databaseType != null ? databaseType : "未指定").append("\n\n");
        
        sb.append("表结构:\n");
        for (TableMetadata table : tables) {
            sb.append("- 表名: ").append(table.getTableName()).append("\n");
            sb.append("  描述: ").append(table.getDescription()).append("\n");
            sb.append("  列:\n");
            
            for (ColumnMetadata column : table.getColumns()) {
                sb.append("    - ").append(column.getColumnName())
                        .append(" (").append(column.getDataType()).append(")");
                
                if (column.isPrimaryKey()) {
                    sb.append(" [主键]");
                }
                
                if (column.getDescription() != null && !column.getDescription().isEmpty()) {
                    sb.append(": ").append(column.getDescription());
                }
                
                sb.append("\n");
            }
            sb.append("\n");
        }
        
        if (relationships != null && !relationships.isEmpty()) {
            sb.append("表关系:\n");
            for (TableRelationship relationship : relationships) {
                sb.append("- ").append(relationship.getSourceTable())
                        .append(".").append(relationship.getSourceColumn())
                        .append(" -> ")
                        .append(relationship.getTargetTable())
                        .append(".").append(relationship.getTargetColumn())
                        .append("\n");
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 表元数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TableMetadata {
        
        /**
         * 表名
         */
        private String tableName;
        
        /**
         * 表描述
         */
        private String description;
        
        /**
         * 列列表
         */
        private List<ColumnMetadata> columns;
        
        /**
         * 示例数据
         */
        private List<Map<String, Object>> sampleData;
    }
    
    /**
     * 列元数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ColumnMetadata {
        
        /**
         * 列名
         */
        private String columnName;
        
        /**
         * 数据类型
         */
        private String dataType;
        
        /**
         * 是否为主键
         */
        private boolean primaryKey;
        
        /**
         * 列描述
         */
        private String description;
        
        /**
         * 列中文名
         */
        private String chineseName;
    }
    
    /**
     * 表关系
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TableRelationship {
        
        /**
         * 源表名
         */
        private String sourceTable;
        
        /**
         * 源列名
         */
        private String sourceColumn;
        
        /**
         * 目标表名
         */
        private String targetTable;
        
        /**
         * 目标列名
         */
        private String targetColumn;
        
        /**
         * 关系类型（如"一对多"、"一对一"等）
         */
        private String relationshipType;
    }
}
