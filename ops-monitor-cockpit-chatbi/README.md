# ChatBI 模块

ChatBI是一个基于自然语言处理的BI查询系统，允许用户使用自然语言进行数据查询，支持多轮对话和上下文理解。

## 功能特点

- **自然语言查询**：使用自然语言描述查询需求，系统自动转换为SQL查询
- **多轮对话**：支持上下文理解，可以进行多轮对话，提问后续问题
- **Spring AI会话管理**：使用Spring AI的内置会话管理功能，支持多种存储后端
- **数据可视化**：自动推荐合适的图表类型，展示查询结果
- **多数据源支持**：支持配置和连接多种数据源
- **元数据管理**：提供元数据表和字段的管理界面
- **数据源管理**：提供数据源配置的管理界面
- **安全加密**：对数据源凭证进行加密存储和传输

## 技术架构

### 后端架构

- **Spring Boot**：应用框架
- **Spring AI**：集成大语言模型和会话管理
- **Spring AI ChatMemory**：内置会话管理和多种存储后端支持
- **MyBatis Plus**：数据访问层
- **Ollama**：本地大语言模型服务
- **PostgreSQL**：元数据和Spring AI会话存储

### 前端技术

- **Bootstrap 5**：UI框架
- **Chart.js**：数据可视化
- **Highlight.js**：SQL语法高亮

## 模块结构

```
ops-monitor-cockpit-chatbi/
├── src/main/java/com/chinamobile/cmss/ops/fms/chatbi/
│   ├── config/                 # 配置类
│   ├── controller/             # REST API控制器
│   ├── entity/                 # 数据库实体类
│   ├── mapper/                 # MyBatis Mapper接口
│   ├── model/                  # 数据模型和DTO
│   ├── service/                # 服务接口
│   │   └── impl/               # 服务实现类
│   └── util/                   # 工具类
├── src/main/resources/
│   ├── db/migration/           # 数据库迁移脚本
│   ├── mapper/                 # MyBatis XML映射文件
│   ├── prompts/                # 提示词模板
│   ├── static/                 # 前端静态资源
│   │   ├── css/                # 样式文件
│   │   ├── js/                 # JavaScript文件
│   │   └── *.html              # HTML页面
│   └── application.yml         # 应用配置文件
└── pom.xml                     # Maven项目配置
```

## 主要组件

- **ChatBIApplication**：应用入口
- **NaturalLanguageQueryController**：自然语言查询API
- **DataSourceConfigController**：数据源配置API
- **MetadataManagementController**：元数据管理API
- **TextToSqlService**：文本到SQL转换服务
- **ConversationService**：会话管理服务
- **DataSourceManagerService**：数据源管理服务
- **DatabaseConnectionService**：数据库连接服务

## API接口

### 自然语言查询接口

- `POST /api/nl-query`：处理自然语言查询
- `POST /api/nl-query/contextual`：处理上下文自然语言查询
- `POST /api/nl-query/conversation-query`：在指定会话中进行查询
- `GET /api/nl-query/context`：获取用户的当前会话上下文
- `GET /api/nl-query/conversation`：获取特定会话的上下文
- `POST /api/nl-query/clear-context`：清除用户的所有会话上下文
- `GET /api/nl-query/conversations`：获取用户的会话列表
- `POST /api/nl-query/new-conversation`：创建新会话
- `POST /api/nl-query/clear-cache`：清除查询缓存

### 数据源配置接口

- `GET /api/datasource-config/enabled`：获取所有启用的数据源配置
- `GET /api/datasource-config/default`：获取默认数据源配置
- `GET /api/datasource-config/{id}`：根据ID获取数据源配置
- `GET /api/datasource-config`：分页查询数据源配置
- `POST /api/datasource-config`：创建数据源配置
- `PUT /api/datasource-config/{id}`：更新数据源配置
- `DELETE /api/datasource-config/{id}`：删除数据源配置
- `POST /api/datasource-config/test-connection`：测试数据源连接

### 元数据管理接口

- `GET /api/metadata/tables`：获取元数据表列表
- `GET /api/metadata/tables/{id}`：获取元数据表详情
- `POST /api/metadata/tables`：创建元数据表
- `PUT /api/metadata/tables/{id}`：更新元数据表
- `DELETE /api/metadata/tables/{id}`：删除元数据表
- `GET /api/metadata/columns`：获取元数据列列表
- `POST /api/metadata/columns`：创建元数据列
- `PUT /api/metadata/columns/{id}`：更新元数据列
- `DELETE /api/metadata/columns/{id}`：删除元数据列

## 前端页面

- **index.html**：主页面，提供自然语言查询和会话管理
- **metadata-management.html**：元数据管理页面
- **datasource-management.html**：数据源管理页面

## 配置说明

### 应用配置

在`application.yml`中配置：

```yaml
app:
  chatbi:
    # 提示词模板文件
    prompt-template-file: classpath:prompts/text-to-sql-prompt.txt
    context-prompt-template-file: classpath:prompts/text-to-sql-context-prompt.txt
    # 最大结果行数
    max-result-rows: 1000
    # 默认数据源类型
    default-datasource: doris
    # 会话配置
    conversation:
      # 会话过期时间（分钟）
      expiration-minutes: 30
      # 最大历史记录长度
      max-history-length: 10
      # 默认启用上下文
      context-enabled: true
```

### 大语言模型配置

```yaml
spring:
  ai:
    # Ollama 配置
    ollama:
      chat:
        options:
          model: qwen3:4b  # 或其他支持的模型
        enabled: true
      base-url: http://localhost:11434  # Ollama 服务地址

    # Spring AI Chat Memory 配置
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: always  # 自动初始化数据库表
```

## Spring AI 会话管理迁移

本模块已从自定义会话管理迁移到Spring AI的内置会话管理功能。主要改进包括：

### 迁移优势
- **减少代码复杂度**：移除了1000+行自定义会话管理代码
- **更好的可维护性**：使用Spring AI经过充分测试的会话管理
- **多种存储后端**：支持In-Memory、JDBC、Cassandra、Neo4j等
- **自动上下文注入**：Spring AI自动管理会话历史
- **标准化API**：遵循Spring AI约定

### 新增组件
- **ChatClientConfig**：配置带会话内存的ChatClient
- **SpringAiConversationService**：简化的会话操作
- **ConversationBridgeService**：向后兼容性桥接

详细迁移信息请参考：[SPRING_AI_MIGRATION.md](SPRING_AI_MIGRATION.md)

## 安装与部署

### 前置条件

- Java 17+
- Maven 3.6+
- PostgreSQL 12+
- Ollama 服务（用于文本到SQL转换）

### 构建步骤

1. 克隆代码库
2. 配置数据库连接信息
3. 构建项目：`mvn clean package`
4. 运行应用：`java -jar target/ops-monitor-cockpit-chatbi.jar`

### 数据库初始化

应用启动时会自动执行`db/migration`目录下的SQL脚本，初始化数据库结构。Spring AI也会自动创建`SPRING_AI_CHAT_MEMORY`表用于会话管理。

## 使用指南

### 自然语言查询

1. 访问主页面
2. 在查询输入框中输入自然语言查询，如"近30天告警数量按照等级分类统计"
3. 选择数据源
4. 点击"发送"按钮
5. 查看查询结果和可视化图表

### 多轮对话

1. 确保"对话模式"开关已启用
2. 输入初始查询并发送
3. 在后续输入框中输入上下文相关的问题，如"按照告警来源分类"
4. 系统会根据上下文理解并执行查询

### 会话管理

1. 点击"新会话"按钮创建新的对话
2. 在左侧会话列表中点击历史会话进行切换
3. 使用"清除上下文"按钮清除当前会话的上下文

### 数据源管理

1. 访问"数据源管理"页面
2. 添加、编辑或删除数据源配置
3. 测试数据源连接

### 元数据管理

1. 访问"元数据管理"页面
2. 管理元数据表和字段
3. 配置表之间的关系

## 开发指南

### 添加新的数据源类型

1. 在`DatabaseConnectionService`接口中添加新的数据源类型
2. 实现相应的连接服务类
3. 在`DynamicDatabaseConnectionServiceFactory`中注册新的数据源类型

### 自定义提示词模板

修改`prompts`目录下的提示词模板文件，调整文本到SQL转换的提示词。

### 扩展可视化类型

在前端`main.js`中的`generateChart`函数中添加新的图表类型支持。

## 故障排除

### 常见问题

1. **连接数据源失败**：检查数据源配置和网络连接
2. **SQL生成错误**：检查提示词模板和元数据配置
3. **查询结果为空**：验证SQL语句和数据库中的实际数据

### 日志查看

应用日志位于`logs`目录，可以查看详细的错误信息和调试信息。

## 贡献指南

1. Fork 代码库
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

版权所有 © 2025 中国移动（苏州）软件技术有限公司
