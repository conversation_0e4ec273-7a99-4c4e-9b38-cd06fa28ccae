# MCP Client Module

The MCP (Model Context Protocol) Client module provides integration with Spring AI's MCP client functionality, enabling seamless connection to and interaction with MCP servers that provide tools, resources, and capabilities to AI models.

## Overview

The Model Context Protocol (MCP) is a standardized protocol that enables AI models to interact with external tools and resources in a structured way. This module implements a comprehensive MCP client solution using Spring AI's MCP Boot Starters.

## Key Features

- **Multi-Transport Support**: STDIO (process-based) and SSE (HTTP-based) transport mechanisms
- **Synchronous and Asynchronous Clients**: Support for both sync and async MCP client implementations
- **Tool Integration**: Seamless integration with Spring AI's tool execution framework
- **Resource Management**: Access and management of MCP resources from connected servers
- **Event Handling**: Comprehensive event handling for tools, resources, and prompts changes
- **Health Monitoring**: Real-time monitoring and health checks for MCP connections
- **Statistics and Metrics**: Detailed statistics and performance metrics
- **RESTful APIs**: Complete REST API for client management and tool execution

## Architecture

### Core Components

```
ops-monitor-cockpit-mcp-client/
├── src/main/java/com/chinamobile/cmss/ops/fms/mcp/
│   ├── config/                 # Configuration classes
│   │   ├── McpClientConfig.java
│   │   ├── McpClientCustomizer.java
│   │   └── OllamaIntegrationConfig.java
│   ├── controller/             # REST API controllers
│   │   ├── McpClientController.java
│   │   └── McpToolController.java
│   ├── model/                  # Data models and DTOs
│   │   ├── McpClientInfo.java
│   │   ├── McpToolInfo.java
│   │   └── McpResourceInfo.java
│   ├── service/                # Service interfaces and implementations
│   │   ├── McpClientService.java
│   │   ├── McpToolService.java
│   │   ├── McpResourceService.java
│   │   └── impl/
│   │       └── McpClientServiceImpl.java
│   └── McpClientApplication.java
├── src/main/resources/
│   ├── application.yml         # Application configuration
│   └── mcp-servers.json        # MCP server configurations
└── pom.xml                     # Maven project configuration
```

### Technology Stack

- **Spring Boot**: Application framework and auto-configuration
- **Spring AI**: MCP client integration and tool execution framework
- **Spring WebFlux**: Reactive SSE transport support
- **Ollama**: Local LLM integration for tool-enhanced conversations
- **Maven**: Dependency management and build system

## Configuration

### Application Configuration

The module is configured through `application.yml`:

```yaml
spring:
  ai:
    mcp:
      client:
        enabled: true
        name: ops-monitor-mcp-client
        type: SYNC  # SYNC or ASYNC
        request-timeout: 30s
        sse:
          connections:
            web-search-server:
              url: http://localhost:8081
              sse-endpoint: /sse
```

### MCP Server Configuration

MCP servers are configured in `mcp-servers.json` using the Claude Desktop format:

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/tmp/mcp-demo"],
      "env": {"DEBUG": "mcp*"}
    },
    "brave-search": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-brave-search"],
      "env": {"BRAVE_API_KEY": "your-api-key-here"}
    }
  }
}
```

## API Endpoints

### MCP Client Management

- `GET /api/mcp/clients` - Get all MCP clients
- `GET /api/mcp/clients/{clientId}` - Get specific client info
- `GET /api/mcp/clients/by-server/{serverName}` - Get client by server name
- `GET /api/mcp/clients/health` - Get health status of all clients
- `POST /api/mcp/clients/{clientId}/test-connection` - Test client connection
- `POST /api/mcp/clients/refresh` - Refresh client information cache
- `GET /api/mcp/clients/statistics` - Get client statistics
- `GET /api/mcp/clients/summary` - Get client summary

### MCP Tool Management

- `GET /api/mcp/tools` - Get all available tools
- `GET /api/mcp/tools/by-server/{serverName}` - Get tools from specific server
- `GET /api/mcp/tools/{toolName}` - Get specific tool info
- `POST /api/mcp/tools/{toolName}/execute` - Execute a tool
- `POST /api/mcp/tools/by-server/{serverName}/{toolName}/execute` - Execute tool from specific server
- `GET /api/mcp/tools/statistics` - Get tool execution statistics
- `GET /api/mcp/tools/{toolName}/history` - Get tool execution history
- `PUT /api/mcp/tools/{toolName}/enabled` - Enable/disable a tool
- `GET /api/mcp/tools/search?query={query}` - Search tools
- `POST /api/mcp/tools/refresh` - Refresh tool information

## Usage Examples

### Basic Client Information

```bash
# Get all MCP clients
curl http://localhost:8083/api/mcp/clients

# Get client health status
curl http://localhost:8083/api/mcp/clients/health

# Test specific client connection
curl -X POST http://localhost:8083/api/mcp/clients/mcp-client-0/test-connection
```

### Tool Execution

```bash
# Get all available tools
curl http://localhost:8083/api/mcp/tools

# Execute a filesystem tool
curl -X POST http://localhost:8083/api/mcp/tools/read_file/execute \
  -H "Content-Type: application/json" \
  -d '{"arguments": {"path": "/tmp/mcp-demo/example.txt"}}'

# Get tool execution statistics
curl http://localhost:8083/api/mcp/tools/statistics
```

### Integration with ChatBI

The MCP client can be integrated with the existing ChatBI functionality:

```java
@Autowired
private ChatClient mcpEnabledChatClient;

// Use MCP tools in conversations
String response = mcpEnabledChatClient.prompt()
    .user("Read the contents of /tmp/mcp-demo/data.txt and summarize it")
    .call()
    .content();
```

## Monitoring and Health Checks

### Health Indicators

The module provides custom health indicators accessible via Spring Boot Actuator:

- `/actuator/health/mcp` - MCP client health status
- `/actuator/metrics` - MCP-related metrics
- `/actuator/info` - Application information

### Logging

Comprehensive logging is configured for debugging and monitoring:

```yaml
logging:
  level:
    com.chinamobile.cmss.ops.fms.mcp: DEBUG
    org.springframework.ai.mcp: DEBUG
    io.mcp: DEBUG
```

## Development and Testing

### Running the Application

```bash
# Start the MCP client application
mvn spring-boot:run

# Or run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### Testing MCP Servers

Before running the application, ensure MCP servers are available:

```bash
# Install and run filesystem server
npx -y @modelcontextprotocol/server-filesystem /tmp/mcp-demo

# Install and run brave search server (requires API key)
export BRAVE_API_KEY=your-api-key
npx -y @modelcontextprotocol/server-brave-search
```

### Integration Testing

The module includes comprehensive integration tests:

```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=McpClientServiceTest
```

## Troubleshooting

### Common Issues

1. **MCP Server Connection Failed**
   - Verify server is running and accessible
   - Check network connectivity and firewall settings
   - Validate server configuration in `mcp-servers.json`

2. **Tool Execution Timeout**
   - Increase `request-timeout` in configuration
   - Check tool execution timeout settings
   - Monitor server performance and logs

3. **Missing Dependencies**
   - Ensure Node.js and npm are installed for STDIO servers
   - Verify MCP server packages are available
   - Check environment variables and API keys

### Debug Mode

Enable debug logging for detailed troubleshooting:

```yaml
logging:
  level:
    root: DEBUG
    com.chinamobile.cmss.ops.fms.mcp: TRACE
```

## Performance Considerations

- **Connection Pooling**: Configure appropriate thread pool sizes
- **Timeout Settings**: Adjust timeouts based on server response times
- **Caching**: Enable resource caching for frequently accessed data
- **Monitoring**: Use metrics to identify performance bottlenecks

## Security Considerations

- **API Keys**: Store sensitive credentials in environment variables
- **Network Security**: Use HTTPS for SSE connections in production
- **Access Control**: Implement proper authentication and authorization
- **Input Validation**: Validate all tool execution parameters

## Implementation Status

**Current Status**: This implementation provides a comprehensive framework for Spring AI MCP client integration. However, due to Spring AI version compatibility issues (MCP support requires Spring AI 1.0.0+ while the parent project uses 1.0.0-M6), the actual MCP client dependencies are not yet available.

**What's Implemented**:
- ✅ Complete project structure and architecture
- ✅ Configuration classes and customizers
- ✅ Service interfaces and mock implementations
- ✅ REST API controllers for client and tool management
- ✅ Comprehensive data models and DTOs
- ✅ Application configuration and properties
- ✅ Test framework and documentation

**What's Pending**:
- ⏳ Actual MCP client integration (waiting for Spring AI 1.0.0 release)
- ⏳ Real tool execution and resource access
- ⏳ Live server connections and protocol handling

## Migration Path

When Spring AI 1.0.0 is released and available:

1. **Update Dependencies**: Upgrade Spring AI version to 1.0.0+
2. **Replace Mock Implementations**: Replace mock service implementations with real MCP client integration
3. **Enable MCP Features**: Uncomment and configure actual MCP client beans
4. **Test Integration**: Verify tool execution and resource access functionality

## Future Enhancements

- **Async Client Support**: Full implementation of async MCP clients
- **Advanced Caching**: Intelligent caching strategies for resources
- **Load Balancing**: Support for multiple server instances
- **Custom Transports**: Additional transport mechanism support
- **Enhanced Monitoring**: Advanced metrics and alerting capabilities
