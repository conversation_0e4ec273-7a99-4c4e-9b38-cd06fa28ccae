spring:
  application:
    name: ops-monitor-cockpit-mcp-client
  
  # Spring AI MCP Client Configuration
  ai:
    mcp:
      client:
        enabled: true
        name: ops-monitor-mcp-client
        version: 1.0.0
        request-timeout: 30s
        type: SYNC  # SYNC or ASYNC
        initialized: true
        root-change-notification: true
        
        # Tool callback integration with Spring AI
        toolcallback:
          enabled: true
        

        # SSE Transport Configuration  
        sse:
          connections:
            web-search-server:
              url: http://localhost:8081
              sse-endpoint: /sse
            demo-server:
              url: http://localhost:8082
              sse-endpoint: /sse
    
    # Ollama Configuration for integration
    ollama:
      chat:
        options:
          model: qwen2.5-coder:7b
        enabled: true
      base-url: http://localhost:11434

  # Web configuration
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

server:
  port: 8083

# Logging Configuration
logging:
  level:
    root: INFO
    com.chinamobile.cmss.ops.fms.mcp: DEBUG
    org.springframework.ai.mcp: DEBUG
    io.mcp: DEBUG

# MCP Client Application Configuration
app:
  mcp:
    # Client management settings
    client:
      # Maximum number of concurrent connections
      max-connections: 10
      # Connection timeout in seconds
      connection-timeout: 30
      # Health check interval in seconds
      health-check-interval: 60
      # Auto-reconnect on failure
      auto-reconnect: true
      # Retry attempts for failed connections
      retry-attempts: 3
      
    # Tool execution settings
    tools:
      # Enable tool execution logging
      execution-logging: true
      # Tool execution timeout in seconds
      execution-timeout: 120
      # Maximum concurrent tool executions
      max-concurrent-executions: 5
      
    # Resource management settings
    resources:
      # Enable resource caching
      caching-enabled: true
      # Cache TTL in minutes
      cache-ttl: 30
      # Maximum cache size
      max-cache-size: 1000

# Management endpoints for monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,mcp
  endpoint:
    health:
      show-details: always
    mcp:
      enabled: true
