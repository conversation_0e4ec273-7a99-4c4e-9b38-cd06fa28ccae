/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.mcp.service;

import com.chinamobile.cmss.ops.fms.mcp.model.McpClientInfo;
import io.modelcontextprotocol.spec.McpSchema;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface McpClientService {
    
    /**
     * Get information about all MCP clients
     * 
     * @return list of MCP client information
     */
    List<McpClientInfo> getAllClients();
    
    /**
     * Get information about a specific MCP client
     * 
     * @param clientId the client identifier
     * @return client information if found
     */
    Optional<McpClientInfo> getClientInfo(String clientId);
    
    /**
     * Get information about a client by server configuration name
     * 
     * @param serverConfigurationName the server configuration name
     * @return client information if found
     */
    Optional<McpClientInfo> getClientByServerName(String serverConfigurationName);
    
    /**
     * Get the health status of all MCP clients
     * 
     * @return map of client ID to health status
     */
    Map<String, String> getClientsHealthStatus();
    
    /**
     * Test the connection to a specific MCP client
     * 
     * @param clientId the client identifier
     * @return true if connection is healthy
     */
    boolean testClientConnection(String clientId);
    
    /**
     * Refresh the client information cache
     */
    void refreshClientInfo();
    
    /**
     * Handle tools change notification from MCP client customizer
     * 
     * @param serverConfigurationName the server configuration name
     * @param tools the updated list of tools
     */
    void onToolsChanged(String serverConfigurationName, List<McpSchema.Tool> tools);
    
    /**
     * Handle resources change notification from MCP client customizer
     * 
     * @param serverConfigurationName the server configuration name
     * @param resources the updated list of resources
     */
    void onResourcesChanged(String serverConfigurationName, List<McpSchema.Resource> resources);
    
    /**
     * Handle prompts change notification from MCP client customizer
     * 
     * @param serverConfigurationName the server configuration name
     * @param prompts the updated list of prompts
     */
    void onPromptsChanged(String serverConfigurationName, List<McpSchema.Prompt> prompts);
    
    /**
     * Handle log message from MCP server
     * 
     * @param serverConfigurationName the server configuration name
     * @param logMessage the log message
     */
    void onLogMessage(String serverConfigurationName, McpSchema.LoggingMessageNotification logMessage);
    
    /**
     * Get client statistics
     * 
     * @return map of statistics
     */
    Map<String, Object> getClientStatistics();
    
    /**
     * Get the total number of active clients
     * 
     * @return number of active clients
     */
    int getActiveClientCount();
    
    /**
     * Get the total number of available tools across all clients
     * 
     * @return total number of tools
     */
    int getTotalToolCount();
    
    /**
     * Get the total number of available resources across all clients
     * 
     * @return total number of resources
     */
    int getTotalResourceCount();
}
