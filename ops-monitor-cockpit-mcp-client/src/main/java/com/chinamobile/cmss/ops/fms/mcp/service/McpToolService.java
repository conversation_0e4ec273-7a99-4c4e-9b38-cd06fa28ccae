/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.mcp.service;

import com.chinamobile.cmss.ops.fms.mcp.model.McpToolInfo;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface McpToolService {
    
    /**
     * Get information about all available MCP tools
     * 
     * @return list of MCP tool information
     */
    List<McpToolInfo> getAllTools();
    
    /**
     * Get information about tools from a specific server
     * 
     * @param serverConfigurationName the server configuration name
     * @return list of tools from the specified server
     */
    List<McpToolInfo> getToolsByServer(String serverConfigurationName);
    
    /**
     * Get information about a specific tool
     * 
     * @param toolName the tool name
     * @return tool information if found
     */
    Optional<McpToolInfo> getToolInfo(String toolName);
    
    /**
     * Get information about a specific tool from a specific server
     * 
     * @param serverConfigurationName the server configuration name
     * @param toolName the tool name
     * @return tool information if found
     */
    Optional<McpToolInfo> getToolInfo(String serverConfigurationName, String toolName);
    
    /**
     * Execute a tool with the given arguments
     * 
     * @param toolName the tool name
     * @param arguments the tool arguments
     * @return execution result
     */
    Map<String, Object> executeTool(String toolName, Map<String, Object> arguments);
    
    /**
     * Execute a tool from a specific server with the given arguments
     * 
     * @param serverConfigurationName the server configuration name
     * @param toolName the tool name
     * @param arguments the tool arguments
     * @return execution result
     */
    Map<String, Object> executeTool(String serverConfigurationName, String toolName, Map<String, Object> arguments);
    
    /**
     * Get tool execution statistics
     * 
     * @return map of tool statistics
     */
    Map<String, Object> getToolStatistics();
    
    /**
     * Get execution history for a specific tool
     * 
     * @param toolName the tool name
     * @param limit maximum number of history entries to return
     * @return list of execution history entries
     */
    List<Map<String, Object>> getToolExecutionHistory(String toolName, int limit);
    
    /**
     * Clear execution statistics for all tools
     */
    void clearToolStatistics();
    
    /**
     * Enable or disable a specific tool
     * 
     * @param toolName the tool name
     * @param enabled whether the tool should be enabled
     */
    void setToolEnabled(String toolName, boolean enabled);
    
    /**
     * Get the list of enabled tools
     * 
     * @return list of enabled tool names
     */
    List<String> getEnabledTools();
    
    /**
     * Get the list of disabled tools
     * 
     * @return list of disabled tool names
     */
    List<String> getDisabledTools();
    
    /**
     * Refresh tool information from all connected servers
     */
    void refreshToolInfo();
    
    /**
     * Test if a tool is available and can be executed
     * 
     * @param toolName the tool name
     * @return true if the tool is available
     */
    boolean isToolAvailable(String toolName);
    
    /**
     * Get tools by category
     * 
     * @param category the tool category
     * @return list of tools in the specified category
     */
    List<McpToolInfo> getToolsByCategory(String category);
    
    /**
     * Search tools by name or description
     * 
     * @param query the search query
     * @return list of matching tools
     */
    List<McpToolInfo> searchTools(String query);
}
