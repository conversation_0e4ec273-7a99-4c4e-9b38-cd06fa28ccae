/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.mcp.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpToolInfo {
    
    /**
     * Unique name of the tool
     */
    private String name;
    
    /**
     * Human-readable description of the tool
     */
    private String description;
    
    /**
     * JSON schema defining the tool's input parameters
     */
    private Map<String, Object> inputSchema;
    
    /**
     * Client ID that provides this tool
     */
    private String clientId;
    
    /**
     * Server configuration name that provides this tool
     */
    private String serverConfigurationName;
    
    /**
     * Tool category or type
     */
    private String category;
    
    /**
     * Tool version (if available)
     */
    private String version;
    
    /**
     * Whether the tool is currently available
     */
    private boolean available;
    
    /**
     * Whether the tool is enabled for execution
     */
    private boolean enabled;
    
    /**
     * Timestamp when the tool was first discovered
     */
    private LocalDateTime discoveredAt;
    
    /**
     * Timestamp of the last successful execution
     */
    private LocalDateTime lastExecutedAt;
    
    /**
     * Number of successful executions
     */
    private long successfulExecutions;
    
    /**
     * Number of failed executions
     */
    private long failedExecutions;
    
    /**
     * Average execution time in milliseconds
     */
    private double averageExecutionTimeMs;
    
    /**
     * Last execution duration in milliseconds
     */
    private long lastExecutionDurationMs;
    
    /**
     * Last error message (if any)
     */
    private String lastError;
    
    /**
     * Tool-specific configuration or metadata
     */
    private Map<String, Object> metadata;
    
    /**
     * Tool execution timeout in seconds
     */
    private int timeoutSeconds;
    
    /**
     * Maximum number of concurrent executions allowed
     */
    private int maxConcurrentExecutions;
    
    /**
     * Current number of active executions
     */
    private int activeExecutions;
    
    /**
     * Tool usage statistics
     */
    private ToolUsageStats usageStats;
    
    /**
     * Tool Usage Statistics
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ToolUsageStats {
        
        /**
         * Total number of invocations
         */
        private long totalInvocations;
        
        /**
         * Success rate as a percentage
         */
        private double successRate;
        
        /**
         * Average response time in milliseconds
         */
        private double averageResponseTime;
        
        /**
         * Minimum response time in milliseconds
         */
        private long minResponseTime;
        
        /**
         * Maximum response time in milliseconds
         */
        private long maxResponseTime;
        
        /**
         * Number of invocations in the last hour
         */
        private long invocationsLastHour;
        
        /**
         * Number of invocations in the last day
         */
        private long invocationsLastDay;
        
        /**
         * Timestamp of the first invocation
         */
        private LocalDateTime firstInvocationAt;
        
        /**
         * Timestamp of the last invocation
         */
        private LocalDateTime lastInvocationAt;
    }
}
