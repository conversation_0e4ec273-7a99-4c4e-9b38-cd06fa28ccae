/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.mcp.controller;

import com.chinamobile.cmss.ops.fms.mcp.model.McpClientInfo;
import com.chinamobile.cmss.ops.fms.mcp.service.McpClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/mcp/clients")
@Slf4j
public class McpClientController {
    
    @Autowired
    private McpClientService mcpClientService;
    
    /**
     * Get information about all MCP clients
     * 
     * @return list of MCP client information
     */
    @GetMapping
    public ResponseEntity<List<McpClientInfo>> getAllClients() {
        try {
            List<McpClientInfo> clients = mcpClientService.getAllClients();
            log.debug("Retrieved information for {} MCP clients", clients.size());
            return ResponseEntity.ok(clients);
        } catch (Exception e) {
            log.error("Error retrieving MCP clients: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get information about a specific MCP client
     * 
     * @param clientId the client identifier
     * @return client information if found
     */
    @GetMapping("/{clientId}")
    public ResponseEntity<McpClientInfo> getClientInfo(@PathVariable String clientId) {
        try {
            Optional<McpClientInfo> clientInfo = mcpClientService.getClientInfo(clientId);
            
            if (clientInfo.isPresent()) {
                log.debug("Retrieved information for MCP client: {}", clientId);
                return ResponseEntity.ok(clientInfo.get());
            } else {
                log.warn("MCP client not found: {}", clientId);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("Error retrieving MCP client {}: {}", clientId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get information about a client by server configuration name
     * 
     * @param serverName the server configuration name
     * @return client information if found
     */
    @GetMapping("/by-server/{serverName}")
    public ResponseEntity<McpClientInfo> getClientByServerName(@PathVariable String serverName) {
        try {
            Optional<McpClientInfo> clientInfo = mcpClientService.getClientByServerName(serverName);
            
            if (clientInfo.isPresent()) {
                log.debug("Retrieved information for MCP client by server: {}", serverName);
                return ResponseEntity.ok(clientInfo.get());
            } else {
                log.warn("MCP client not found for server: {}", serverName);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("Error retrieving MCP client by server {}: {}", serverName, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get the health status of all MCP clients
     * 
     * @return map of client ID to health status
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> getClientsHealthStatus() {
        try {
            Map<String, String> healthStatus = mcpClientService.getClientsHealthStatus();
            log.debug("Retrieved health status for {} MCP clients", healthStatus.size());
            return ResponseEntity.ok(healthStatus);
        } catch (Exception e) {
            log.error("Error retrieving MCP clients health status: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Test the connection to a specific MCP client
     * 
     * @param clientId the client identifier
     * @return connection test result
     */
    @PostMapping("/{clientId}/test-connection")
    public ResponseEntity<Map<String, Object>> testClientConnection(@PathVariable String clientId) {
        try {
            boolean isHealthy = mcpClientService.testClientConnection(clientId);
            
            Map<String, Object> result = Map.of(
                    "clientId", clientId,
                    "healthy", isHealthy,
                    "status", isHealthy ? "CONNECTED" : "DISCONNECTED",
                    "timestamp", System.currentTimeMillis());
            
            log.debug("Connection test for MCP client {}: {}", clientId, isHealthy ? "PASSED" : "FAILED");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error testing connection for MCP client {}: {}", clientId, e.getMessage(), e);
            
            Map<String, Object> result = Map.of(
                    "clientId", clientId,
                    "healthy", false,
                    "status", "ERROR",
                    "error", e.getMessage(),
                    "timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * Refresh the client information cache
     * 
     * @return refresh operation result
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshClientInfo() {
        try {
            mcpClientService.refreshClientInfo();
            
            Map<String, Object> result = Map.of(
                    "success", true,
                    "message", "Client information cache refreshed successfully",
                    "timestamp", System.currentTimeMillis());
            
            log.info("MCP client information cache refreshed successfully");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error refreshing MCP client information cache: {}", e.getMessage(), e);
            
            Map<String, Object> result = Map.of(
                    "success", false,
                    "message", "Failed to refresh client information cache",
                    "error", e.getMessage(),
                    "timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * Get client statistics
     * 
     * @return map of statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getClientStatistics() {
        try {
            Map<String, Object> statistics = mcpClientService.getClientStatistics();
            log.debug("Retrieved MCP client statistics");
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("Error retrieving MCP client statistics: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get summary information about MCP clients
     * 
     * @return summary information
     */
    @GetMapping("/summary")
    public ResponseEntity<Map<String, Object>> getClientSummary() {
        try {
            Map<String, Object> summary = Map.of(
                    "totalClients", mcpClientService.getAllClients().size(),
                    "activeClients", mcpClientService.getActiveClientCount(),
                    "totalTools", mcpClientService.getTotalToolCount(),
                    "totalResources", mcpClientService.getTotalResourceCount(),
                    "timestamp", System.currentTimeMillis());
            
            log.debug("Retrieved MCP client summary");
            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            log.error("Error retrieving MCP client summary: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
