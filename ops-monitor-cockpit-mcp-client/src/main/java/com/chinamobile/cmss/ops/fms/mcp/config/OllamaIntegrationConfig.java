/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.mcp.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.mcp.SyncMcpToolCallbackProvider;
import org.springframework.ai.model.ollama.autoconfigure.OllamaConnectionDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OllamaIntegrationConfig implements OllamaConnectionDetails {
    
    @Value("${spring.ai.ollama.base-url:http://localhost:11434}")
    private String baseUrl;
    
    @Autowired(required = false)
    private SyncMcpToolCallbackProvider mcpToolCallbackProvider;
    
    /**
     * Get the Ollama base URL
     * 
     * @return the base URL for Ollama service
     */
    @Override
    public String getBaseUrl() {
        return baseUrl;
    }
    
    /**
     * Create a ChatClient with MCP tool integration
     * 
     * @param chatModel the Ollama chat model
     * @return configured ChatClient with MCP tools
     */
    @Bean
    public ChatClient mcpEnabledChatClient(ChatModel chatModel) {
        ChatClient.Builder builder = ChatClient.builder(chatModel);
        
        // Add MCP tools if available
        if (mcpToolCallbackProvider != null) {
            builder.defaultToolCallbacks(mcpToolCallbackProvider.getToolCallbacks());
        }
        
        return builder.build();
    }
}
