/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.mcp.service.impl;

import com.chinamobile.cmss.ops.fms.mcp.model.McpClientInfo;
import com.chinamobile.cmss.ops.fms.mcp.service.McpClientService;
import io.modelcontextprotocol.client.McpSyncClient;
import io.modelcontextprotocol.spec.McpSchema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@Slf4j
public class McpClientServiceImpl implements McpClientService {
    
    // @Autowired(required = false)
    private List<McpSyncClient> mcpSyncClients = new ArrayList<>();
    
    // Cache for client information
    private final Map<String, McpClientInfo> clientInfoCache = new ConcurrentHashMap<>();
    
    // Statistics tracking
    private final Map<String, Long> clientRequestCounts = new ConcurrentHashMap<>();
    
    private final Map<String, Long> clientErrorCounts = new ConcurrentHashMap<>();
    
    private final Map<String, LocalDateTime> clientLastActivity = new ConcurrentHashMap<>();
    
    // Tools, resources, and prompts tracking
    private final Map<String, List<McpSchema.Tool>> serverTools = new ConcurrentHashMap<>();
    
    private final Map<String, List<McpSchema.Resource>> serverResources = new ConcurrentHashMap<>();
    
    private final Map<String, List<McpSchema.Prompt>> serverPrompts = new ConcurrentHashMap<>();
    
    /**
     * Initialize client information cache on startup
     */
    @Override
    public void refreshClientInfo() {
        log.info("Refreshing MCP client information cache");
        
        clientInfoCache.clear();
        
        for (int i = 0; i < mcpSyncClients.size(); i++) {
            McpSyncClient client = mcpSyncClients.get(i);
            String clientId = generateClientId(i);
            
            try {
                McpClientInfo clientInfo = buildClientInfo(client, clientId, i);
                clientInfoCache.put(clientId, clientInfo);
                
                log.debug("Cached information for client: {}", clientId);
            } catch (Exception e) {
                log.error("Error building client info for client {}: {}", clientId, e.getMessage(), e);
            }
        }
        
        log.info("Refreshed information for {} MCP clients", clientInfoCache.size());
    }
    
    @Override
    public List<McpClientInfo> getAllClients() {
        if (clientInfoCache.isEmpty()) {
            refreshClientInfo();
        }
        return new ArrayList<>(clientInfoCache.values());
    }
    
    @Override
    public Optional<McpClientInfo> getClientInfo(String clientId) {
        if (clientInfoCache.isEmpty()) {
            refreshClientInfo();
        }
        return Optional.ofNullable(clientInfoCache.get(clientId));
    }
    
    @Override
    public Optional<McpClientInfo> getClientByServerName(String serverConfigurationName) {
        return getAllClients().stream()
                .filter(client -> serverConfigurationName.equals(client.getServerConfigurationName()))
                .findFirst();
    }
    
    @Override
    public Map<String, String> getClientsHealthStatus() {
        Map<String, String> healthStatus = new HashMap<>();
        
        getAllClients().forEach(client -> {
            try {
                boolean isHealthy = testClientConnection(client.getClientId());
                healthStatus.put(client.getClientId(), isHealthy ? "HEALTHY" : "UNHEALTHY");
            } catch (Exception e) {
                healthStatus.put(client.getClientId(), "ERROR: " + e.getMessage());
            }
        });
        
        return healthStatus;
    }
    
    @Override
    public boolean testClientConnection(String clientId) {
        try {
            Optional<McpClientInfo> clientInfo = getClientInfo(clientId);
            if (clientInfo.isEmpty()) {
                return false;
            }
            
            // For now, we'll consider a client healthy if it's in our cache
            // In a real implementation, you might want to ping the server
            return clientInfo.get().getStatus() == McpClientInfo.ConnectionStatus.CONNECTED ||
                    clientInfo.get().getStatus() == McpClientInfo.ConnectionStatus.READY;
        } catch (Exception e) {
            log.error("Error testing connection for client {}: {}", clientId, e.getMessage());
            return false;
        }
    }
    
    @Override
    public void onToolsChanged(String serverConfigurationName, List<McpSchema.Tool> tools) {
        log.info("Tools changed for server '{}': {} tools", serverConfigurationName, tools.size());
        
        serverTools.put(serverConfigurationName, new ArrayList<>(tools));
        updateClientInfoForServer(serverConfigurationName);
        
        // Update statistics
        clientLastActivity.put(serverConfigurationName, LocalDateTime.now());
    }
    
    @Override
    public void onResourcesChanged(String serverConfigurationName, List<McpSchema.Resource> resources) {
        log.info("Resources changed for server '{}': {} resources", serverConfigurationName, resources.size());
        
        serverResources.put(serverConfigurationName, new ArrayList<>(resources));
        updateClientInfoForServer(serverConfigurationName);
        
        // Update statistics
        clientLastActivity.put(serverConfigurationName, LocalDateTime.now());
    }
    
    @Override
    public void onPromptsChanged(String serverConfigurationName, List<McpSchema.Prompt> prompts) {
        log.info("Prompts changed for server '{}': {} prompts", serverConfigurationName, prompts.size());
        
        serverPrompts.put(serverConfigurationName, new ArrayList<>(prompts));
        updateClientInfoForServer(serverConfigurationName);
        
        // Update statistics
        clientLastActivity.put(serverConfigurationName, LocalDateTime.now());
    }
    
    @Override
    public void onLogMessage(String serverConfigurationName, McpSchema.LoggingMessageNotification logMessage) {
        // Update last activity
        clientLastActivity.put(serverConfigurationName, LocalDateTime.now());
        
        // Log the message appropriately based on level
        String level = logMessage.level() != null ? logMessage.level().toString() : "INFO";
        String message = logMessage.data() != null ? logMessage.data().toString() : "No message";
        
        switch (level.toUpperCase()) {
            case "ERROR":
                log.error("MCP Server '{}': {}", serverConfigurationName, message);
                clientErrorCounts.merge(serverConfigurationName, 1L, Long::sum);
                break;
            case "WARN":
                log.warn("MCP Server '{}': {}", serverConfigurationName, message);
                break;
            case "DEBUG":
                log.debug("MCP Server '{}': {}", serverConfigurationName, message);
                break;
            default:
                log.info("MCP Server '{}': {}", serverConfigurationName, message);
        }
    }
    
    @Override
    public Map<String, Object> getClientStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalClients", mcpSyncClients.size());
        stats.put("activeClients", getActiveClientCount());
        stats.put("totalTools", getTotalToolCount());
        stats.put("totalResources", getTotalResourceCount());
        stats.put("totalPrompts", serverPrompts.values().stream().mapToInt(List::size).sum());
        
        // Request statistics
        long totalRequests = clientRequestCounts.values().stream().mapToLong(Long::longValue).sum();
        long totalErrors = clientErrorCounts.values().stream().mapToLong(Long::longValue).sum();
        
        stats.put("totalRequests", totalRequests);
        stats.put("totalErrors", totalErrors);
        stats.put("successRate", totalRequests > 0 ? (double) (totalRequests - totalErrors) / totalRequests * 100 : 100.0);
        
        // Per-server statistics
        Map<String, Map<String, Object>> serverStats = new HashMap<>();
        for (String serverName : serverTools.keySet()) {
            Map<String, Object> serverStat = new HashMap<>();
            serverStat.put("tools", serverTools.getOrDefault(serverName, Collections.emptyList()).size());
            serverStat.put("resources", serverResources.getOrDefault(serverName, Collections.emptyList()).size());
            serverStat.put("prompts", serverPrompts.getOrDefault(serverName, Collections.emptyList()).size());
            serverStat.put("requests", clientRequestCounts.getOrDefault(serverName, 0L));
            serverStat.put("errors", clientErrorCounts.getOrDefault(serverName, 0L));
            serverStat.put("lastActivity", clientLastActivity.get(serverName));
            
            serverStats.put(serverName, serverStat);
        }
        stats.put("serverStatistics", serverStats);
        
        return stats;
    }
    
    @Override
    public int getActiveClientCount() {
        return (int) getAllClients().stream()
                .filter(client -> client.getStatus() == McpClientInfo.ConnectionStatus.CONNECTED ||
                        client.getStatus() == McpClientInfo.ConnectionStatus.READY)
                .count();
    }
    
    @Override
    public int getTotalToolCount() {
        return serverTools.values().stream().mapToInt(List::size).sum();
    }
    
    @Override
    public int getTotalResourceCount() {
        return serverResources.values().stream().mapToInt(List::size).sum();
    }
    
    /**
     * Scheduled task to refresh client information periodically
     */
    @Scheduled(fixedRate = 60000)
    public void scheduledRefresh() {
        try {
            refreshClientInfo();
        } catch (Exception e) {
            log.error("Error during scheduled client info refresh: {}", e.getMessage(), e);
        }
    }
    
    private String generateClientId(int index) {
        return "mcp-client-" + index;
    }
    
    private McpClientInfo buildClientInfo(McpSyncClient client, String clientId, int index) {
        // Since we can't directly access client internals, we'll build info based on available data
        String serverConfigName = "server-" + index;
        
        return McpClientInfo.builder()
                .clientId(clientId)
                .name("MCP Client " + index)
                .version("1.0.0")
                .clientType("SYNC")
                .status(McpClientInfo.ConnectionStatus.CONNECTED)
                .serverConfigurationName(serverConfigName)
                .transportType("AUTO_DETECTED")
                .createdAt(LocalDateTime.now())
                .lastConnectedAt(LocalDateTime.now())
                .lastActivityAt(clientLastActivity.get(serverConfigName))
                .successfulRequests(clientRequestCounts.getOrDefault(serverConfigName, 0L))
                .failedRequests(clientErrorCounts.getOrDefault(serverConfigName, 0L))
                .availableTools(getToolNames(serverConfigName))
                .availableResources(getResourceNames(serverConfigName))
                .availablePrompts(getPromptNames(serverConfigName))
                .initialized(true)
                .autoReconnect(true)
                .requestTimeoutSeconds(30)
                .build();
    }
    
    /**
     * Update client info when server data changes
     * @param serverConfigurationName the server configuration name
     */
    private void updateClientInfoForServer(String serverConfigurationName) {
        clientInfoCache.values().stream()
                .filter(client -> serverConfigurationName.equals(client.getServerConfigurationName()))
                .forEach(client -> {
                    client.setAvailableTools(getToolNames(serverConfigurationName));
                    client.setAvailableResources(getResourceNames(serverConfigurationName));
                    client.setAvailablePrompts(getPromptNames(serverConfigurationName));
                    client.setLastActivityAt(LocalDateTime.now());
                });
    }
    
    /**
     * Get tool names for a server
     * @param serverConfigurationName the server configuration name
     * @return list of tool names
     */
    private List<String> getToolNames(String serverConfigurationName) {
        return serverTools.getOrDefault(serverConfigurationName, Collections.emptyList())
                .stream()
                .map(McpSchema.Tool::name)
                .collect(Collectors.toList());
    }
    
    /**
     * Get resource names for a server
     * @param serverConfigurationName the server configuration name
     * @return list of resource names
     */
    private List<String> getResourceNames(String serverConfigurationName) {
        return serverResources.getOrDefault(serverConfigurationName, Collections.emptyList())
                .stream()
                .map(McpSchema.Resource::uri)
                .collect(Collectors.toList());
    }
    
    /**
     * Get prompt names for a server
     * @param serverConfigurationName the server configuration name
     * @return list of prompt names
     */
    private List<String> getPromptNames(String serverConfigurationName) {
        return serverPrompts.getOrDefault(serverConfigurationName, Collections.emptyList())
                .stream()
                .map(McpSchema.Prompt::name)
                .collect(Collectors.toList());
    }
}
