/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.mcp.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpClientInfo {
    
    /**
     * Unique identifier for the MCP client instance
     */
    private String clientId;
    
    /**
     * Human-readable name of the MCP client
     */
    private String name;
    
    /**
     * Version of the MCP client
     */
    private String version;
    
    /**
     * Type of the client (SYNC or ASYNC)
     */
    private String clientType;
    
    /**
     * Current connection status
     */
    private ConnectionStatus status;
    
    /**
     * Server configuration name this client is connected to
     */
    private String serverConfigurationName;
    
    /**
     * Transport type used for communication (STDIO, SSE_HTTP, SSE_WEBFLUX)
     */
    private String transportType;
    
    /**
     * Server endpoint URL (for SSE connections)
     */
    private String serverUrl;
    
    /**
     * Timestamp when the client was created
     */
    private LocalDateTime createdAt;
    
    /**
     * Timestamp of the last successful connection
     */
    private LocalDateTime lastConnectedAt;
    
    /**
     * Timestamp of the last activity
     */
    private LocalDateTime lastActivityAt;
    
    /**
     * Number of successful requests
     */
    private long successfulRequests;
    
    /**
     * Number of failed requests
     */
    private long failedRequests;
    
    /**
     * List of available tools from the connected server
     */
    private List<String> availableTools;
    
    /**
     * List of available resources from the connected server
     */
    private List<String> availableResources;
    
    /**
     * List of available prompts from the connected server
     */
    private List<String> availablePrompts;
    
    /**
     * Server capabilities
     */
    private Map<String, Object> serverCapabilities;
    
    /**
     * Client configuration properties
     */
    private Map<String, Object> configuration;
    
    /**
     * Last error message (if any)
     */
    private String lastError;
    
    /**
     * Whether the client is initialized
     */
    private boolean initialized;
    
    /**
     * Whether auto-reconnect is enabled
     */
    private boolean autoReconnect;
    
    /**
     * Request timeout in seconds
     */
    private int requestTimeoutSeconds;
    
    /**
     * Connection Status Enumeration
     */
    public enum ConnectionStatus {
        DISCONNECTED,
        CONNECTING,
        CONNECTED,
        RECONNECTING,
        ERROR,
        INITIALIZING,
        READY
    }
}
