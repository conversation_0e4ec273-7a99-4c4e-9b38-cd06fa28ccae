/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.mcp.controller;

import com.chinamobile.cmss.ops.fms.mcp.model.McpToolInfo;
import com.chinamobile.cmss.ops.fms.mcp.service.McpToolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/mcp/tools")
@Slf4j
public class McpToolController {
    
    @Autowired(required = false)
    private McpToolService mcpToolService;
    
    /**
     * Get information about all available MCP tools
     * 
     * @return list of MCP tool information
     */
    @GetMapping
    public ResponseEntity<List<McpToolInfo>> getAllTools() {
        if (mcpToolService == null) {
            log.warn("MCP Tool Service not available");
            return ResponseEntity.ok(List.of());
        }
        
        try {
            List<McpToolInfo> tools = mcpToolService.getAllTools();
            log.debug("Retrieved information for {} MCP tools", tools.size());
            return ResponseEntity.ok(tools);
        } catch (Exception e) {
            log.error("Error retrieving MCP tools: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get information about tools from a specific server
     * 
     * @param serverName the server configuration name
     * @return list of tools from the specified server
     */
    @GetMapping("/by-server/{serverName}")
    public ResponseEntity<List<McpToolInfo>> getToolsByServer(@PathVariable String serverName) {
        if (mcpToolService == null) {
            return ResponseEntity.ok(List.of());
        }
        
        try {
            List<McpToolInfo> tools = mcpToolService.getToolsByServer(serverName);
            log.debug("Retrieved {} tools from server: {}", tools.size(), serverName);
            return ResponseEntity.ok(tools);
        } catch (Exception e) {
            log.error("Error retrieving tools from server {}: {}", serverName, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get information about a specific tool
     * 
     * @param toolName the tool name
     * @return tool information if found
     */
    @GetMapping("/{toolName}")
    public ResponseEntity<McpToolInfo> getToolInfo(@PathVariable String toolName) {
        if (mcpToolService == null) {
            return ResponseEntity.notFound().build();
        }
        
        try {
            Optional<McpToolInfo> toolInfo = mcpToolService.getToolInfo(toolName);
            
            if (toolInfo.isPresent()) {
                log.debug("Retrieved information for MCP tool: {}", toolName);
                return ResponseEntity.ok(toolInfo.get());
            } else {
                log.warn("MCP tool not found: {}", toolName);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("Error retrieving MCP tool {}: {}", toolName, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Execute a tool with the given arguments
     * 
     * @param toolName the tool name
     * @param request the execution request containing arguments
     * @return execution result
     */
    @PostMapping("/{toolName}/execute")
    public ResponseEntity<Map<String, Object>> executeTool(
                                                           @PathVariable String toolName,
                                                           @RequestBody Map<String, Object> request) {
        
        if (mcpToolService == null) {
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "error", "MCP Tool Service not available"));
        }
        
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> arguments = (Map<String, Object>) request.getOrDefault("arguments", Map.of());
            
            Map<String, Object> result = mcpToolService.executeTool(toolName, arguments);
            
            log.info("Executed MCP tool: {} with arguments: {}", toolName, arguments.keySet());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error executing MCP tool {}: {}", toolName, e.getMessage(), e);
            
            Map<String, Object> errorResult = Map.of(
                    "success", false,
                    "error", e.getMessage(),
                    "toolName", toolName,
                    "timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }
    
    /**
     * Execute a tool from a specific server with the given arguments
     * 
     * @param serverName the server configuration name
     * @param toolName the tool name
     * @param request the execution request containing arguments
     * @return execution result
     */
    @PostMapping("/by-server/{serverName}/{toolName}/execute")
    public ResponseEntity<Map<String, Object>> executeToolFromServer(
                                                                     @PathVariable String serverName,
                                                                     @PathVariable String toolName,
                                                                     @RequestBody Map<String, Object> request) {
        
        if (mcpToolService == null) {
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "error", "MCP Tool Service not available"));
        }
        
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> arguments = (Map<String, Object>) request.getOrDefault("arguments", Map.of());
            
            Map<String, Object> result = mcpToolService.executeTool(serverName, toolName, arguments);
            
            log.info("Executed MCP tool: {} from server: {} with arguments: {}",
                    toolName, serverName, arguments.keySet());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error executing MCP tool {} from server {}: {}",
                    toolName, serverName, e.getMessage(), e);
            
            Map<String, Object> errorResult = Map.of(
                    "success", false,
                    "error", e.getMessage(),
                    "toolName", toolName,
                    "serverName", serverName,
                    "timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }
    
    /**
     * Get tool execution statistics
     * 
     * @return map of tool statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getToolStatistics() {
        if (mcpToolService == null) {
            return ResponseEntity.ok(Map.of("message", "MCP Tool Service not available"));
        }
        
        try {
            Map<String, Object> statistics = mcpToolService.getToolStatistics();
            log.debug("Retrieved MCP tool statistics");
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("Error retrieving MCP tool statistics: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get execution history for a specific tool
     * 
     * @param toolName the tool name
     * @param limit maximum number of history entries to return (default: 10)
     * @return list of execution history entries
     */
    @GetMapping("/{toolName}/history")
    public ResponseEntity<List<Map<String, Object>>> getToolExecutionHistory(
                                                                             @PathVariable String toolName,
                                                                             @RequestParam(defaultValue = "10") int limit) {
        
        if (mcpToolService == null) {
            return ResponseEntity.ok(List.of());
        }
        
        try {
            List<Map<String, Object>> history = mcpToolService.getToolExecutionHistory(toolName, limit);
            log.debug("Retrieved {} execution history entries for tool: {}", history.size(), toolName);
            return ResponseEntity.ok(history);
        } catch (Exception e) {
            log.error("Error retrieving execution history for tool {}: {}", toolName, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Enable or disable a specific tool
     * 
     * @param toolName the tool name
     * @param request the request containing enabled status
     * @return operation result
     */
    @PutMapping("/{toolName}/enabled")
    public ResponseEntity<Map<String, Object>> setToolEnabled(
                                                              @PathVariable String toolName,
                                                              @RequestBody Map<String, Object> request) {
        
        if (mcpToolService == null) {
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "error", "MCP Tool Service not available"));
        }
        
        try {
            boolean enabled = (Boolean) request.getOrDefault("enabled", true);
            mcpToolService.setToolEnabled(toolName, enabled);
            
            Map<String, Object> result = Map.of(
                    "success", true,
                    "toolName", toolName,
                    "enabled", enabled,
                    "message", String.format("Tool %s %s successfully",
                            toolName, enabled ? "enabled" : "disabled"),
                    "timestamp", System.currentTimeMillis());
            
            log.info("Tool {} {}", toolName, enabled ? "enabled" : "disabled");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error setting tool {} enabled status: {}", toolName, e.getMessage(), e);
            
            Map<String, Object> result = Map.of(
                    "success", false,
                    "error", e.getMessage(),
                    "toolName", toolName,
                    "timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * Search tools by name or description
     * 
     * @param query the search query
     * @return list of matching tools
     */
    @GetMapping("/search")
    public ResponseEntity<List<McpToolInfo>> searchTools(@RequestParam String query) {
        if (mcpToolService == null) {
            return ResponseEntity.ok(List.of());
        }
        
        try {
            List<McpToolInfo> tools = mcpToolService.searchTools(query);
            log.debug("Found {} tools matching query: {}", tools.size(), query);
            return ResponseEntity.ok(tools);
        } catch (Exception e) {
            log.error("Error searching tools with query {}: {}", query, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Refresh tool information from all connected servers
     * 
     * @return refresh operation result
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshToolInfo() {
        if (mcpToolService == null) {
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "error", "MCP Tool Service not available"));
        }
        
        try {
            mcpToolService.refreshToolInfo();
            
            Map<String, Object> result = Map.of(
                    "success", true,
                    "message", "Tool information refreshed successfully",
                    "timestamp", System.currentTimeMillis());
            
            log.info("MCP tool information refreshed successfully");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error refreshing MCP tool information: {}", e.getMessage(), e);
            
            Map<String, Object> result = Map.of(
                    "success", false,
                    "message", "Failed to refresh tool information",
                    "error", e.getMessage(),
                    "timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(result);
        }
    }
}
