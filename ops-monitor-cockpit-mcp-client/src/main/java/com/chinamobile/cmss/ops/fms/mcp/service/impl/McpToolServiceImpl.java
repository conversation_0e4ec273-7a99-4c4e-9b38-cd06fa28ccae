/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.mcp.service.impl;

import com.chinamobile.cmss.ops.fms.mcp.model.McpToolInfo;
import com.chinamobile.cmss.ops.fms.mcp.service.McpToolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.mcp.SyncMcpToolCallbackProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@Slf4j
public class McpToolServiceImpl implements McpToolService {
    
    // @Autowired(required = false)
    // private List<McpSyncClient> mcpSyncClients = new ArrayList<>();
    
    // Execution history (limited size)
    private static final int MAX_HISTORY_SIZE = 100;
    
    @Autowired(required = false)
    private SyncMcpToolCallbackProvider toolCallbackProvider;
    
    // Tool information cache
    private final Map<String, McpToolInfo> toolInfoCache = new ConcurrentHashMap<>();
    
    // Tool execution statistics
    
    private final Map<String, Long> toolExecutionCounts = new ConcurrentHashMap<>();
    
    private final Map<String, Long> toolErrorCounts = new ConcurrentHashMap<>();
    
    private final Map<String, LocalDateTime> toolLastExecuted = new ConcurrentHashMap<>();
    
    private final Map<String, Boolean> toolEnabledStatus = new ConcurrentHashMap<>();
    
    private final Map<String, List<Map<String, Object>>> executionHistory = new ConcurrentHashMap<>();
    
    @Override
    public List<McpToolInfo> getAllTools() {
        refreshToolInfo();
        return new ArrayList<>(toolInfoCache.values());
    }
    
    @Override
    public List<McpToolInfo> getToolsByServer(String serverConfigurationName) {
        return getAllTools().stream()
                .filter(tool -> serverConfigurationName.equals(tool.getServerConfigurationName()))
                .collect(Collectors.toList());
    }
    
    @Override
    public Optional<McpToolInfo> getToolInfo(String toolName) {
        refreshToolInfo();
        return toolInfoCache.values().stream()
                .filter(tool -> toolName.equals(tool.getName()))
                .findFirst();
    }
    
    @Override
    public Optional<McpToolInfo> getToolInfo(String serverConfigurationName, String toolName) {
        return getToolsByServer(serverConfigurationName).stream()
                .filter(tool -> toolName.equals(tool.getName()))
                .findFirst();
    }
    
    @Override
    public Map<String, Object> executeTool(String toolName, Map<String, Object> arguments) {
        try {
            // Check if tool is enabled
            if (!toolEnabledStatus.getOrDefault(toolName, true)) {
                return createErrorResult(toolName, "Tool is disabled");
            }
            
            // For now, we'll simulate tool execution since we don't have direct access to tool execution
            // In a real implementation, this would use the MCP client to execute the tool
            log.info("Executing tool: {} with arguments: {}", toolName, arguments.keySet());
            
            // Update statistics
            toolExecutionCounts.merge(toolName, 1L, Long::sum);
            toolLastExecuted.put(toolName, LocalDateTime.now());
            
            // Add to execution history
            addToExecutionHistory(toolName, arguments, true, null);
            
            // Simulate successful execution
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("toolName", toolName);
            result.put("executedAt", LocalDateTime.now());
            result.put("arguments", arguments);
            result.put("result", "Tool executed successfully (simulated)");
            
            return result;
            
        } catch (Exception e) {
            log.error("Error executing tool {}: {}", toolName, e.getMessage(), e);
            
            // Update error statistics
            toolErrorCounts.merge(toolName, 1L, Long::sum);
            addToExecutionHistory(toolName, arguments, false, e.getMessage());
            
            return createErrorResult(toolName, e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> executeTool(String serverConfigurationName, String toolName, Map<String, Object> arguments) {
        // For now, delegate to the general execute method
        // In a real implementation, this would target the specific server
        return executeTool(toolName, arguments);
    }
    
    @Override
    public Map<String, Object> getToolStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        long totalExecutions = toolExecutionCounts.values().stream().mapToLong(Long::longValue).sum();
        long totalErrors = toolErrorCounts.values().stream().mapToLong(Long::longValue).sum();
        
        stats.put("totalTools", toolInfoCache.size());
        stats.put("enabledTools", getEnabledTools().size());
        stats.put("disabledTools", getDisabledTools().size());
        stats.put("totalExecutions", totalExecutions);
        stats.put("totalErrors", totalErrors);
        stats.put("successRate", totalExecutions > 0 ? (double) (totalExecutions - totalErrors) / totalExecutions * 100 : 100.0);
        
        // Per-tool statistics
        Map<String, Map<String, Object>> toolStats = new HashMap<>();
        for (String toolName : toolInfoCache.keySet()) {
            Map<String, Object> toolStat = new HashMap<>();
            toolStat.put("executions", toolExecutionCounts.getOrDefault(toolName, 0L));
            toolStat.put("errors", toolErrorCounts.getOrDefault(toolName, 0L));
            toolStat.put("lastExecuted", toolLastExecuted.get(toolName));
            toolStat.put("enabled", toolEnabledStatus.getOrDefault(toolName, true));
            
            toolStats.put(toolName, toolStat);
        }
        stats.put("toolStatistics", toolStats);
        
        return stats;
    }
    
    @Override
    public List<Map<String, Object>> getToolExecutionHistory(String toolName, int limit) {
        List<Map<String, Object>> history = executionHistory.getOrDefault(toolName, new ArrayList<>());
        return history.stream()
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    @Override
    public void clearToolStatistics() {
        toolExecutionCounts.clear();
        toolErrorCounts.clear();
        toolLastExecuted.clear();
        executionHistory.clear();
        log.info("Tool statistics cleared");
    }
    
    @Override
    public void setToolEnabled(String toolName, boolean enabled) {
        toolEnabledStatus.put(toolName, enabled);
        log.info("Tool {} {}", toolName, enabled ? "enabled" : "disabled");
    }
    
    @Override
    public List<String> getEnabledTools() {
        return toolInfoCache.keySet().stream()
                .filter(toolName -> toolEnabledStatus.getOrDefault(toolName, true))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<String> getDisabledTools() {
        return toolInfoCache.keySet().stream()
                .filter(toolName -> !toolEnabledStatus.getOrDefault(toolName, true))
                .collect(Collectors.toList());
    }
    
    @Override
    public void refreshToolInfo() {
        // Since we don't have direct access to MCP client tool information,
        // we'll create mock tool information for demonstration
        // In a real implementation, this would query the MCP clients for available tools
        
        toolInfoCache.clear();
        
        // Create some example tools
        createMockToolInfo("read_file", "filesystem", "Read contents of a file", "file");
        createMockToolInfo("write_file", "filesystem", "Write contents to a file", "file");
        createMockToolInfo("list_directory", "filesystem", "List directory contents", "file");
        createMockToolInfo("search_web", "brave-search", "Search the web using Brave Search", "search");
        createMockToolInfo("get_weather", "weather", "Get current weather information", "weather");
        
        log.debug("Refreshed tool information: {} tools available", toolInfoCache.size());
    }
    
    @Override
    public boolean isToolAvailable(String toolName) {
        return toolInfoCache.containsKey(toolName) &&
                toolEnabledStatus.getOrDefault(toolName, true);
    }
    
    @Override
    public List<McpToolInfo> getToolsByCategory(String category) {
        return getAllTools().stream()
                .filter(tool -> category.equals(tool.getCategory()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<McpToolInfo> searchTools(String query) {
        String lowerQuery = query.toLowerCase();
        return getAllTools().stream()
                .filter(tool -> tool.getName().toLowerCase().contains(lowerQuery) ||
                        (tool.getDescription() != null && tool.getDescription().toLowerCase().contains(lowerQuery)))
                .collect(Collectors.toList());
    }
    
    /**
     * Create mock tool information for demonstration
     * @param name the tool name
     * @param server the server configuration name
     * @param description the tool description
     * @param category the tool category
     */
    private void createMockToolInfo(String name, String server, String description, String category) {
        McpToolInfo toolInfo = McpToolInfo.builder()
                .name(name)
                .description(description)
                .serverConfigurationName(server)
                .category(category)
                .available(true)
                .enabled(toolEnabledStatus.getOrDefault(name, true))
                .discoveredAt(LocalDateTime.now())
                .successfulExecutions(toolExecutionCounts.getOrDefault(name, 0L))
                .failedExecutions(toolErrorCounts.getOrDefault(name, 0L))
                .lastExecutedAt(toolLastExecuted.get(name))
                .timeoutSeconds(30)
                .maxConcurrentExecutions(5)
                .activeExecutions(0)
                .inputSchema(createMockInputSchema(name))
                .build();
        
        toolInfoCache.put(name, toolInfo);
    }
    
    /**
     * Create mock input schema for a tool
     * @param toolName the tool name
     * @return the input schema map
     */
    private Map<String, Object> createMockInputSchema(String toolName) {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        switch (toolName) {
            case "read_file":
            case "write_file":
                properties.put("path", Map.of("type", "string", "description", "File path"));
                if ("write_file".equals(toolName)) {
                    properties.put("content", Map.of("type", "string", "description", "File content"));
                }
                break;
            case "list_directory":
                properties.put("path", Map.of("type", "string", "description", "Directory path"));
                break;
            case "search_web":
                properties.put("query", Map.of("type", "string", "description", "Search query"));
                break;
            case "get_weather":
                properties.put("location", Map.of("type", "string", "description", "Location name"));
                break;
            default:
                // Default case for unknown tools
                properties.put("input", Map.of("type", "string", "description", "Tool input"));
                break;
        }
        
        schema.put("properties", properties);
        schema.put("required", new ArrayList<>(properties.keySet()));
        
        return schema;
    }
    
    /**
     * Create error result map
     * @param toolName the tool name
     * @param error the error message
     * @return the error result map
     */
    private Map<String, Object> createErrorResult(String toolName, String error) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("toolName", toolName);
        result.put("error", error);
        result.put("timestamp", LocalDateTime.now());
        return result;
    }
    
    /**
     * Add execution to history
     * @param toolName the tool name
     * @param arguments the execution arguments
     * @param success whether the execution was successful
     * @param error the error message if any
     */
    private void addToExecutionHistory(String toolName, Map<String, Object> arguments, boolean success, String error) {
        final List<Map<String, Object>> history = executionHistory.computeIfAbsent(toolName, k -> new ArrayList<>());
        
        Map<String, Object> entry = new HashMap<>();
        entry.put("timestamp", LocalDateTime.now());
        entry.put("arguments", arguments);
        entry.put("success", success);
        if (error != null) {
            entry.put("error", error);
        }
        
        // Add to beginning of list (most recent first)
        history.add(0, entry);
        
        // Limit history size
        if (history.size() > MAX_HISTORY_SIZE) {
            history.remove(history.size() - 1);
        }
    }
}
