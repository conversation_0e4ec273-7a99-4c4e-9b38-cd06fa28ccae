/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.fms.mcp.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Configuration
@Slf4j
public class McpClientConfig {
    
    @Value("${app.mcp.client.max-connections:10}")
    private int maxConnections;
    
    @Value("${app.mcp.client.connection-timeout:30}")
    private int connectionTimeoutSeconds;
    
    @Value("${app.mcp.tools.max-concurrent-executions:5}")
    private int maxConcurrentToolExecutions;
    
    @Value("${app.mcp.tools.execution-timeout:120}")
    private int toolExecutionTimeoutSeconds;
    
    /**
     * Thread pool executor for MCP client operations
     * 
     * @return configured thread pool executor
     */
    @Bean(name = "mcpClientExecutor")
    public Executor mcpClientExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(maxConnections);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("mcp-client-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        
        log.info("Initialized MCP client thread pool with max connections: {}", maxConnections);
        return executor;
    }
    
    /**
     * Thread pool executor for MCP tool executions
     * 
     * @return configured thread pool executor for tool executions
     */
    @Bean(name = "mcpToolExecutor")
    public Executor mcpToolExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(maxConcurrentToolExecutions);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("mcp-tool-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(toolExecutionTimeoutSeconds);
        executor.initialize();
        
        log.info("Initialized MCP tool execution thread pool with max concurrent executions: {}",
                maxConcurrentToolExecutions);
        return executor;
    }
    
    /**
     * Get the maximum number of connections
     * 
     * @return maximum connections
     */
    public int getMaxConnections() {
        return maxConnections;
    }
    
    /**
     * Get the connection timeout in seconds
     * 
     * @return connection timeout
     */
    public int getConnectionTimeoutSeconds() {
        return connectionTimeoutSeconds;
    }
    
    /**
     * Get the maximum concurrent tool executions
     * 
     * @return maximum concurrent tool executions
     */
    public int getMaxConcurrentToolExecutions() {
        return maxConcurrentToolExecutions;
    }
    
    /**
     * Get the tool execution timeout in seconds
     * 
     * @return tool execution timeout
     */
    public int getToolExecutionTimeoutSeconds() {
        return toolExecutionTimeoutSeconds;
    }
}
