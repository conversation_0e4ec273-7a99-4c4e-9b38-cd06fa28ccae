spring:
  application:
    name: ops-monitor-cockpit-mcp-client-test
  
  # Test configuration for MCP Client
  ai:
    mcp:
      client:
        enabled: false  # Disable for tests to avoid external dependencies
        name: test-mcp-client
        version: 1.0.0
        request-timeout: 5s
        type: SYNC
        initialized: false
        
        # Disable tool callback integration for tests
        toolcallback:
          enabled: false
    
    # Disable Ollama for tests
    ollama:
      chat:
        enabled: false

  # Test server configuration
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

server:
  port: 0  # Use random port for tests

# Test logging configuration
logging:
  level:
    root: WARN
    com.chinamobile.cmss.ops.fms.mcp: DEBUG

# Test MCP configuration
app:
  mcp:
    client:
      max-connections: 2
      connection-timeout: 5
      health-check-interval: 10
      auto-reconnect: false
      retry-attempts: 1
      
    tools:
      execution-logging: false
      execution-timeout: 10
      max-concurrent-executions: 2
      
    resources:
      caching-enabled: false
      cache-ttl: 1
      max-cache-size: 10

# Disable management endpoints for tests
management:
  endpoints:
    enabled-by-default: false
