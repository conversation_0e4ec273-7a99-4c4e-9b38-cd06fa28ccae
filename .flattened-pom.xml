<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.chinamobile.cmss.ops.fms</groupId>
  <artifactId>ops-monitor-cockpit</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>ops-monitor-cockpit</name>
  <description>ops-monitor-cockpit</description>
  <modules>
    <module>ops-monitor-cockpit-common</module>
    <module>ops-monitor-cockpit-web</module>
    <module>ops-monitor-cockpit-external-api-proxy</module>
    <module>ops-monitor-cockpit-chatbi</module>
    <module>ops-monitor-cockpit-mcp-client</module>
  </modules>
  <scm>
    <connection>ssh://*******************/SRE/Ops/ops_monitor_cockpit.git</connection>
    <developerConnection>scm:*******************/SRE/Ops/ops_monitor_cockpit.git</developerConnection>
    <url>http://gitlab.cmss.com/SRE/Ops/ops_monitor_cockpit.git</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>central</id>
      <name>maven-virtual-release</name>
      <url>http://************:9092/artifactory/maven-virtual-release</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>maven-virtual-snapshot</name>
      <url>http://************:9092/artifactory/maven-virtual-snapshot</url>
    </snapshotRepository>
  </distributionManagement>
  <properties>
    <maven-checkstyle-plugin.version>3.2.1</maven-checkstyle-plugin.version>
    <taglist-maven-plugin.version>2.4</taglist-maven-plugin.version>
    <java.encoding>UTF-8</java.encoding>
    <commons-math3.version>3.6.1</commons-math3.version>
    <error_prone_annotations.version>2.22.0</error_prone_annotations.version>
    <ooxml-schemas.version>1.4</ooxml-schemas.version>
    <flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>
    <maven.javadoc.skip>true</maven.javadoc.skip>
    <sonar-maven-plugin.version>3.9.1.2184</sonar-maven-plugin.version>
    <j2objc-annotations.version>1.3</j2objc-annotations.version>
    <pagehelper-spring-boot-starter.version>2.1.0</pagehelper-spring-boot-starter.version>
    <spring-cloud.version>2024.0.1</spring-cloud.version>
    <maven-javadoc-plugin.version>3.5.0</maven-javadoc-plugin.version>
    <commons-lang3.version>3.14.0</commons-lang3.version>
    <fastjson.version>2.0.42</fastjson.version>
    <commons-codec.version>1.16.0</commons-codec.version>
    <langchain4j.embedding.version>0.27.1</langchain4j.embedding.version>
    <caffeine.version>2.9.3</caffeine.version>
    <mybatis.version>3.0.3</mybatis.version>
    <maven-jar-plugin.version>3.3.0</maven-jar-plugin.version>
    <jasypt-version>2.1.1</jasypt-version>
    <maven.compiler.source>17</maven.compiler.source>
    <maven-shade-plugin.version>3.5.1</maven-shade-plugin.version>
    <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
    <maven-toolchains-plugin.version>3.1.0</maven-toolchains-plugin.version>
    <revision>0.0.1-SNAPSHOT</revision>
    <spotless-maven-plugin.version>2.22.1</spotless-maven-plugin.version>
    <maven-enforcer-plugin.version>3.2.1</maven-enforcer-plugin.version>
    <maven-pmd-plugin.version>3.20.0</maven-pmd-plugin.version>
    <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
    <maven.version.range>[3.0.4,)</maven.version.range>
    <spring-boot-maven-plugin.version>2.7.18</spring-boot-maven-plugin.version>
    <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
    <java-util.version>2.4.0</java-util.version>
    <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
    <mysql.version>8.4.0</mysql.version>
    <checker-qual.version>3.40.0</checker-qual.version>
    <checksum-maven-plugin.version>1.10</checksum-maven-plugin.version>
    <spring-ai.version>1.0.0-M6</spring-ai.version>
    <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
    <maven-release-plugin.version>3.0.0</maven-release-plugin.version>
    <maven-project-info-reports-plugin.version>3.4.2</maven-project-info-reports-plugin.version>
    <easyExcel.version>3.3.3</easyExcel.version>
    <apollo.version>2.1.0</apollo.version>
    <hutool.version>5.8.36</hutool.version>
    <findsecbugs.version>1.12.0</findsecbugs.version>
    <guava.version>33.4.0-jre</guava.version>
    <spring-boot.version>3.4.4</spring-boot.version>
    <langchain4j.version>0.34.0</langchain4j.version>
    <org.postgresql.version>42.7.5</org.postgresql.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven-jxr-plugin.version>3.3.0</maven-jxr-plugin.version>
    <fb-contrib.version>7.6.0</fb-contrib.version>
    <maven-deploy-plugin.version>3.1.1</maven-deploy-plugin.version>
    <elasticsearch-rest.version>7.17.24</elasticsearch-rest.version>
    <snakeyaml.version>2.2</snakeyaml.version>
    <jasypt.version>2.1.1</jasypt.version>
    <java.version>17</java.version>
    <mybatis.plus.version>3.5.11</mybatis.plus.version>
    <druid.starter.version>1.2.23</druid.starter.version>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven-assembly-plugin.version>3.5.0</maven-assembly-plugin.version>
    <spotbugs-maven-plugin.version>*******</spotbugs-maven-plugin.version>
    <maven.compiler.target>17</maven.compiler.target>
    <checkstyle.version>10.12.5</checkstyle.version>
    <woodstox-core.version>6.6.2</woodstox-core.version>
    <maven.deploy.skip>false</maven.deploy.skip>
    <apache-httpclient.version>5.1.3</apache-httpclient.version>
    <maven-site-plugin.version>4.0.0-M6</maven-site-plugin.version>
    <commons-pool2.version>2.12.0</commons-pool2.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>${org.postgresql.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.woodstox</groupId>
        <artifactId>woodstox-core</artifactId>
        <version>${woodstox-core.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>${commons-codec.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mysql</groupId>
        <artifactId>mysql-connector-j</artifactId>
        <version>${mysql.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
        <version>${commons-pool2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.yaml</groupId>
        <artifactId>snakeyaml</artifactId>
        <version>${snakeyaml.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>ooxml-schemas</artifactId>
        <version>${ooxml-schemas.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>easyexcel</artifactId>
        <version>${easyExcel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>org.checkerframework</groupId>
        <artifactId>checker-qual</artifactId>
        <version>${checker-qual.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.errorprone</groupId>
        <artifactId>error_prone_annotations</artifactId>
        <version>${error_prone_annotations.version}</version>
      </dependency>
      <dependency>
        <groupId>com.cedarsoftware</groupId>
        <artifactId>java-util</artifactId>
        <version>${java-util.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.j2objc</groupId>
        <artifactId>j2objc-annotations</artifactId>
        <version>${j2objc-annotations.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.ulisesbocchio</groupId>
        <artifactId>jasypt-spring-boot-starter</artifactId>
        <version>${jasypt-version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.fastjson2</groupId>
        <artifactId>fastjson2</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons-lang3.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-bom</artifactId>
        <version>${spring-ai.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.github.pagehelper</groupId>
        <artifactId>pagehelper-spring-boot-starter</artifactId>
        <version>${pagehelper-spring-boot-starter.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>${druid.starter.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-starter</artifactId>
        <version>${druid.starter.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.chinamobile.cmss.ops.fms</groupId>
        <artifactId>ops-monitor-cockpit-common</artifactId>
        <version>0.0.1-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.chinamobile.cmss.ops.fms</groupId>
        <artifactId>ops-monitor-cockpit-external-api-proxy</artifactId>
        <version>0.0.1-SNAPSHOT</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cedarsoftware</groupId>
      <artifactId>java-util</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba.fastjson2</groupId>
      <artifactId>fastjson2</artifactId>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>releases</id>
      <name>release repository</name>
      <url>http://************:9092/artifactory/maven-virtual-release</url>
    </repository>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
      <id>snapshots</id>
      <name>snapshot repository</name>
      <url>http://************:9092/artifactory/maven-virtual-snapshot</url>
    </repository>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>snapshot-repo</id>
      <url>https://s01.oss.sonatype.org/content/repositories/snapshots</url>
    </repository>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>oss-snapshot-repo</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>releases</id>
      <name>release repository</name>
      <url>http://************:9092/artifactory/maven-virtual-release</url>
    </pluginRepository>
    <pluginRepository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
      <id>snapshots</id>
      <name>snapshot repository</name>
      <url>http://************:9092/artifactory/maven-virtual-snapshot</url>
    </pluginRepository>
  </pluginRepositories>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
          <configuration>
            <source>${maven.compiler.source}</source>
            <target>${maven.compiler.target}</target>
            <forceJavacCompilerUse>true</forceJavacCompilerUse>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>flatten-maven-plugin</artifactId>
          <version>${flatten-maven-plugin.version}</version>
          <executions>
            <execution>
              <id>flatten</id>
              <phase>process-resources</phase>
              <goals>
                <goal>flatten</goal>
              </goals>
            </execution>
            <execution>
              <id>flatten.clean</id>
              <phase>clean</phase>
              <goals>
                <goal>clean</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <updatePomFile>true</updatePomFile>
            <flattenMode>resolveCiFriendliesOnly</flattenMode>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${maven-enforcer-plugin.version}</version>
          <executions>
            <execution>
              <id>enforce-banned-dependencies</id>
              <goals>
                <goal>enforce</goal>
              </goals>
              <configuration>
                <rules>
                  <requireMavenVersion>
                    <version>${maven.version.range}</version>
                  </requireMavenVersion>
                  <requireJavaVersion>
                    <version>${java.version}</version>
                  </requireJavaVersion>
                </rules>
                <fail>true</fail>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-maven-plugin</artifactId>
          <executions>
            <execution>
              <goals>
                <goal>repackage</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${maven-resources-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
          <configuration>
            <trimStackTrace>false</trimStackTrace>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven-jar-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>${maven-source-plugin.version}</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <phase>verify</phase>
              <goals>
                <goal>jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${maven-deploy-plugin.version}</version>
          <configuration>
            <skip>${maven.deploy.skip}</skip>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>${maven-release-plugin.version}</version>
          <configuration>
            <tagNameFormat>@{project.version}</tagNameFormat>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${maven-assembly-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-shade-plugin</artifactId>
          <version>${maven-shade-plugin.version}</version>
          <configuration>
            <minimizeJar>true</minimizeJar>
            <shadedArtifactAttached>false</shadedArtifactAttached>
            <shadeSourcesContent>true</shadeSourcesContent>
            <createDependencyReducedPom>false</createDependencyReducedPom>
            <createSourcesJar>false</createSourcesJar>
            <filters>
              <filter>
                <artifact>*:*</artifact>
                <excludes>
                  <exclude>META-INF/*.SF</exclude>
                  <exclude>META-INF/*.DSA</exclude>
                  <exclude>META-INF/*.RSA</exclude>
                </excludes>
              </filter>
            </filters>
            <transformers>
              <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer" />
            </transformers>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${maven-javadoc-plugin.version}</version>
          <configuration>
            <source>${java.version}</source>
            <charset>${project.build.sourceEncoding}</charset>
            <encoding>${project.build.sourceEncoding}</encoding>
            <docencoding>${project.build.sourceEncoding}</docencoding>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>taglist-maven-plugin</artifactId>
          <version>${taglist-maven-plugin.version}</version>
          <configuration>
            <aggregate>true</aggregate>
          </configuration>
        </plugin>
        <plugin>
          <groupId>com.diffplug.spotless</groupId>
          <artifactId>spotless-maven-plugin</artifactId>
          <version>${spotless-maven-plugin.version}</version>
          <configuration>
            <java>
              <eclipse>
                <file>${maven.multiModuleProjectDirectory}/src/resources/spotless/java.xml</file>
              </eclipse>
              <licenseHeader>
                <file>${maven.multiModuleProjectDirectory}/src/resources/spotless/copyright.txt</file>
              </licenseHeader>
            </java>
            <pom>
              <sortPom>
                <encoding>UTF-8</encoding>
                <nrOfIndentSpace>4</nrOfIndentSpace>
                <keepBlankLines>true</keepBlankLines>
                <indentBlankLines>true</indentBlankLines>
                <indentSchemaLocation>false</indentSchemaLocation>
                <spaceBeforeCloseEmptyElement>true</spaceBeforeCloseEmptyElement>
                <sortModules>false</sortModules>
                <sortExecutions>false</sortExecutions>
                <predefinedSortOrder>custom_1</predefinedSortOrder>
                <expandEmptyElements>false</expandEmptyElements>
                <sortProperties>false</sortProperties>
              </sortPom>
              <replace>
                <name>Leading blank line</name>
                <search>-->
                                    &lt;project</search>
                <replacement>-->

                                    &lt;project</replacement>
              </replace>
            </pom>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>${maven-pmd-plugin.version}</version>
          <configuration>
            <aggregate>true</aggregate>
            <targetJdk>${java.version}</targetJdk>
            <rulesets>
              <ruleset>${maven.multiModuleProjectDirectory}/src/resources/pmd.xml</ruleset>
            </rulesets>
          </configuration>
        </plugin>
        <plugin>
          <groupId>com.github.spotbugs</groupId>
          <artifactId>spotbugs-maven-plugin</artifactId>
          <version>${spotbugs-maven-plugin.version}</version>
          <configuration>
            <fork>false</fork>
            <failOnError>false</failOnError>
            <excludeFilterFile>${maven.multiModuleProjectDirectory}/src/resources/spotbugs.xml</excludeFilterFile>
            <plugins>
              <plugin>
                <groupId>com.mebigfatguy.fb-contrib</groupId>
                <artifactId>fb-contrib</artifactId>
                <version>${fb-contrib.version}</version>
              </plugin>
              <plugin>
                <groupId>com.h3xstream.findsecbugs</groupId>
                <artifactId>findsecbugs-plugin</artifactId>
                <version>${findsecbugs.version}</version>
              </plugin>
            </plugins>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.diffplug.spotless</groupId>
        <artifactId>spotless-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>apply</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
  <reporting>
    <plugins>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
        <version>${maven-site-plugin.version}</version>
      </plugin>
      <plugin>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>${maven-project-info-reports-plugin.version}</version>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${maven-javadoc-plugin.version}</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>aggregate</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>${maven-jxr-plugin.version}</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>aggregate</report>
            </reports>
            <inherited>false</inherited>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <failOnViolation>false</failOnViolation>
          <failsOnError>false</failsOnError>
          <violationSeverity>warning</violationSeverity>
          <configLocation>${maven.multiModuleProjectDirectory}/src/resources/checkstyle.xml</configLocation>
          <includeTestSourceDirectory>true</includeTestSourceDirectory>
          <excludes>
            <exclude>**/langchain4j/**,**/openai4j/**,**/autogen/**/*,**/ai/**/*</exclude>
          </excludes>
          <goal>checkstyle-aggregate</goal>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <configuration>
          <xmlOutput>true</xmlOutput>
          <xmlOutputDirectory>target/site</xmlOutputDirectory>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>jdepend-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>taglist-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </reporting>
  <profiles>
    <profile>
      <id>release</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <artifactId>maven-checkstyle-plugin</artifactId>
              <version>${maven-checkstyle-plugin.version}</version>
              <configuration>
                <consoleOutput>true</consoleOutput>
                <failOnViolation>true</failOnViolation>
                <failsOnError>true</failsOnError>
                <violationSeverity>error</violationSeverity>
                <configLocation>${maven.multiModuleProjectDirectory}/src/resources/checkstyle.xml</configLocation>
                <includeTestSourceDirectory>true</includeTestSourceDirectory>
                <excludes>**/autogen/**/*,**/clover/**/*,**/langchain4j/**/*,**/openai4j/**/*,**/ai/**/*</excludes>
              </configuration>
            </plugin>
            <plugin>
              <artifactId>maven-toolchains-plugin</artifactId>
              <version>${maven-toolchains-plugin.version}</version>
              <executions>
                <execution>
                  <goals>
                    <goal>toolchain</goal>
                  </goals>
                </execution>
              </executions>
              <configuration>
                <toolchains>
                  <jdk>
                    <version>${java.version}</version>
                  </jdk>
                </toolchains>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
        <plugins>
          <plugin>
            <artifactId>maven-toolchains-plugin</artifactId>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>clover</id>
      <activation />
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <artifactId>maven-toolchains-plugin</artifactId>
              <version>${maven-toolchains-plugin.version}</version>
              <executions>
                <execution>
                  <goals>
                    <goal>toolchain</goal>
                  </goals>
                </execution>
              </executions>
              <configuration>
                <toolchains>
                  <jdk>
                    <version>${java.version}</version>
                  </jdk>
                </toolchains>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
        <plugins>
          <plugin>
            <artifactId>maven-toolchains-plugin</artifactId>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>local</id>
      <activation />
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <artifactId>maven-checkstyle-plugin</artifactId>
              <version>${maven-checkstyle-plugin.version}</version>
              <configuration>
                <consoleOutput>true</consoleOutput>
                <failOnViolation>true</failOnViolation>
                <failsOnError>true</failsOnError>
                <violationSeverity>error</violationSeverity>
                <configLocation>${maven.multiModuleProjectDirectory}/src/resources/checkstyle.xml</configLocation>
                <includeTestSourceDirectory>true</includeTestSourceDirectory>
                <excludes>**/autogen/**/*</excludes>
                <excludes>**/clover/**/*</excludes>
                <excludes>**/langchain4j/**/*</excludes>
                <excludes>**/openai4j/**/*</excludes>
                <excludes>**/ai/**/*</excludes>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
        <plugins>
          <plugin>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <executions>
              <execution>
                <id>validate</id>
                <phase>validate</phase>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
