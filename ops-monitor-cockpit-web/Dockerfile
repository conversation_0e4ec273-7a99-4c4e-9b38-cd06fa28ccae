FROM registry.paas/ops/openjdk:17
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone
COPY target/ops-monitor-cockpit-web-*.jar app.jar
COPY config/logback.xml logback.xml
ENTRYPOINT ["nohup","java","-jar","-XX:+UnlockExperimentalVMOptions","-XX:-UseContainerSupport","-Dspring.profiles.active=prod", "-Dlogging.config=./logback.xml","-DlogFilePath=/var/log/fms", "-DlogFileName=ops-monitor-cockpit-web" ,"/app.jar"]
