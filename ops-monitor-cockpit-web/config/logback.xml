<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
  <!--  <property name="logFilePath" value="./logs" />-->
  <!--  <property name="logFileName" value="fms-rule" />-->
  <property name="logFilePath" value="/var/log/ops/screen" />
  <property name="logFileName" value="ops-monitor-cockpit" />

  <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${logFilePath}/${logFileName}.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${logFilePath}/${logFileName}.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <MaxFileSize>100MB</MaxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss} [%t] [username=%X{username:-},traceId=%X{traceId:-},url=%X{uri:-}] [%-5level] %logger{96} [%M:%L] - %msg%n</pattern>
      <charset>UTF-8</charset>
    </encoder>
  </appender>
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <charset>UTF-8</charset>
      <pattern>%d{yyyy-MM-dd HH:mm:ss} [%t] [username=%X{username:-},traceId=%X{traceId:-},url=%X{uri:-}] [%-5level] %logger{96} [%M:%L] - %msg%n</pattern>
    </encoder>
  </appender>

  <logger name="com.chinamobile.cmss" level="debug" additivity="false">
    <appender-ref ref="STDOUT"/>
    <appender-ref ref="RollingFile"/>
  </logger>

  <appender name="AUDIT_INFO_LOG"
            class="ch.qos.logback.core.rolling.RollingFileAppender">
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>INFO</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <encoder>
      <pattern>${FILE_LOG_PATTERN}</pattern>
    </encoder>
    <file>${log.logdir}/audit.${log.appname}.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${log.logdir}/audit.${log.appname}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>100MB</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
      <maxHistory>90</maxHistory>
      <totalSizeCap>1GB</totalSizeCap>
    </rollingPolicy>
  </appender>

  <!-- audit log -->
  <logger name="com.chinamobile.cmss.cnet.common.advice.LogAspect" level="info" additivity="false">
    <!--    <appender-ref ref="CONSOLE"/>-->
    <appender-ref ref="AUDIT_INFO_LOG"  />
  </logger>

  <root level="info">
    <appender-ref ref="STDOUT"/>
    <appender-ref ref="RollingFile"/>
  </root>
</configuration>
