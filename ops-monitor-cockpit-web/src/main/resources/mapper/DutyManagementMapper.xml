<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.cmss.ops.monitor.cockpit.web.mapper.DutyManagementMapper">
    <insert id="insertNotice">
        INSERT INTO notice_message (id, content, update_time)
        VALUES (#{id}, #{content}, NOW())
    </insert>
    <insert id="insertDutyTemplate">
        INSERT INTO duty_attendance (duty_date,
                                     team_template_name,
                                     major,
                                     start_time,
                                     end_time,
                                     expected_count)
        VALUES (#{dutyDate}, #{dutyTemplateName},
                #{majorName}, #{startTime}, #{endTime}, #{expectedDutyCount});
    </insert>
    <update id="updateNotice">
        UPDATE notice_message
        SET content = #{content}, update_time = NOW()
        WHERE id = 1
    </update>
    <update id="updateDutyAttendance">
        UPDATE duty_attendance
        SET
            expected_count = #{expectedCount},
            actual_count = #{actualCount},
            modefier = #{modefier},
            update_time = NOW()
        WHERE
            id = #{id}
    </update>

    <select id="findNotice" resultType="com.chinamobile.cmss.ops.monitor.cockpit.web.vo.NoticeVO">
        select *
        from notice_message
        where id = 1
        order by update_time desc limit 1;
    </select>
    <select id="findDutyAttendance"
            resultType="com.chinamobile.cmss.ops.monitor.cockpit.web.vo.DutyAttendanceVO">
        SELECT *
        FROM duty_attendance
        WHERE duty_date = #{dutyDate}
          AND (
            start_time IS NOT NULL AND start_time &lt;&gt; ''
                AND end_time IS NOT NULL AND end_time &lt;&gt; ''
                AND CAST(#{nowStr} AS time) &gt;= CAST(start_time AS time)
                AND (
                end_time = '24:00:00' OR CAST(#{nowStr} AS time) &lt; CAST(end_time AS time)
                )
            )
    </select>
    <select id="findDutyTemplateExit"
            resultType="com.chinamobile.cmss.ops.monitor.cockpit.web.vo.DutyAttendanceVO">
        select *
        from duty_attendance
        <where>
            team_template_name = #{dutyTemplateName}
            and major = #{majorName}
            <choose>
                <when test="dutyDate != null">
                    AND duty_date = #{dutyDate}
                </when>
                <otherwise>
                    AND duty_date IS NULL
                </otherwise>
            </choose>

            <choose>
                <when test="startTime != null and startTime != ''">
                    AND start_time = #{startTime}
                </when>
                <otherwise>
                    AND start_time IS NULL
                </otherwise>
            </choose>

            <choose>
                <when test="endTime != null and endTime != ''">
                    AND end_time = #{endTime}
                </when>
                <otherwise>
                    AND end_time IS NULL
                </otherwise>
            </choose>

        </where>
    </select>

</mapper>