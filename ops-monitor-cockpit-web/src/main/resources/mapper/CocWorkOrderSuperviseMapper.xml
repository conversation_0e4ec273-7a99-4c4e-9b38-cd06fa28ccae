<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.cmss.ops.monitor.cockpit.web.mapper.CocWorkOrderSuperviseMapper">

    <resultMap id="CocWorkOrderSuperviseBaseResultMap" type="com.chinamobile.cmss.ops.monitor.cockpit.web.entity.CocWorkOrderSupervise">
        <id column="id" jdbcType="VARCHAR" property="id"></id>
        <result column="work_order_no" jdbcType="VARCHAR" property="workOrderNo"></result>
        <result column="supervise_num" jdbcType="INTEGER" property="superviseNum"></result>
        <result column="last_supervise_time" jdbcType="TIMESTAMP" property="lastSuperviseTime"></result>
    </resultMap>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into coc_work_order_supervise
        (id, work_order_no, supervise_num, last_supervise_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.workOrderNo,jdbcType=VARCHAR},
            #{item.superviseNum,jdbcType=INTEGER}, #{item.lastSuperviseTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update coc_work_order_supervise
            <set>
                <if test="item.id != null" >
                    id = #{item.id,jdbcType=VARCHAR},
                </if>
                <if test="item.workOrderNo != null" >
                    work_order_no = #{item.workOrderNo,jdbcType=VARCHAR},
                </if>
                <if test="item.superviseNum != null" >
                    supervise_num = #{item.superviseNum,jdbcType=INTEGER},
                </if>
                <if test="item.lastSuperviseTime != null" >
                    last_supervise_time = #{item.lastSuperviseTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

</mapper>