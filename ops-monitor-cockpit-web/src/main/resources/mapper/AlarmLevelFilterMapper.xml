<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.cmss.ops.monitor.cockpit.web.mapper.AlarmLevelFilterMapper">
    <insert id="saveFilter">
        INSERT INTO alarm_level_filter (filter_name,
                                        filter_id,
                                        enable,
                                        sort_order,
                                        create_time,
                                        create_by)
        VALUES (#{filterName},
                #{filterId}, #{enable}, #{sortOrder}, #{createTime}, #{createBy});

    </insert>
    <update id="updateStatus">
        update alarm_level_filter
        set enable=#{enable},
            update_time=#{updateTime},
            update_by=#{updateBy}
        where id = #{id}
    </update>
    <update id="updateFilterById">
        update alarm_level_filter
        set enable=#{enable},
            update_time=#{updateTime},
            update_by=#{updateBy},
            filter_name = #{filterName},
            filter_id=#{filterId}
        where id = #{id}
    </update>
    <update id="updateOrder">
        UPDATE alarm_level_filter
        SET sort_order = CASE id
        <foreach collection="sortOrder" item="sort" index="id">
            WHEN #{id} THEN #{sort}
        </foreach>
        END
        WHERE id IN
        <foreach collection="sortOrder.keySet()" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </update>
    <delete id="removeById">
        delete from alarm_level_filter where id = #{id}
    </delete>


    <select id="getAlarmLevelFilterList"
            resultType="com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmLevelFilter">
        select * from alarm_level_filter order by sort_order
    </select>
    <select id="getById" resultType="com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmLevelFilter">
        select * from alarm_level_filter where id=#{id}
    </select>

</mapper>