# Server Configuration
server.port=8088

# Application Configuration
spring.application.name=ops-monitor-cockpit

# DataSource Configuration
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=*********************************************************
spring.datasource.username=fms
spring.datasource.password=5G8EZHvW%Ix6

# Hikari Connection Pool Configuration
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=HikariCP
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

# MyBatis-Plus Configuration
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.chinamobile.cmss.ops.monitor.cockpit.web.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.id-type=assign_id
mybatis-plus.global-config.db-config.schema=public
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0

# MyBatis-Plus Pagination Configuration
mybatis-plus.configuration.default-enum-type-handler=org.apache.ibatis.type.EnumOrdinalTypeHandler
mybatis-plus.configuration.jdbc-type-for-null=null

# Logging Configuration
logging.level.com.chinamobile.cmss.ops.monitor.cockpit=debug
logging.level.org.springframework=warn

# OPS-Scheduler Configuration
ops.monitor.cockpit.scheduler.base-url=http://foc.ccops.bcopstest.com:30366
ops.monitor.cockpit.scheduler.fault-scene-list-remote-url=/api/scheduler/extsys/v1/ext/monitor/cockpit/querySceneListForCockpit
ops.monitor.cockpit.scheduler.msg-audit-task-num-remote-url=/api/scheduler/configuration/v1/ext/message/audit/queryAuditTaskNum

ops.monitor.cockpit.alarm.base-url=http://foc.ccops.bcopstest.com:30366

# OPS-Influence Configuration
ops.monitor.cockpit.influence.base-url=http://foc.ccops.bcopstest.com:30366
ops.monitor.cockpit.influence.fault-level-statistics-remote-url=/ops-foc/fault-analyse/listing/active/aggregate_board/

# data-queryEngine
data.queryengine.base-url=http://data-queryengine.ccops.cmecloud.cn:31387/v3/dataquery/query/
data.queryengine.app-code=fms-fot
data.queryengine.app-secret=f1b4f349-0160-4f17-83f5-83440107a2bf
data.queryengine.bkdata-authentication-method=token
data.queryengine.data-token=5DvWKxcXH9egicceqVeGsj4xFeZcIqNP5n9dJKN4u7IiH3iWQiJ6BDGYURnRGBWD

# message center
ops.monitor.cockpit.message.base-url=http://
ops.monitor.cockpit.message.short-message-remote-url=************:9001/msn/v1.1/rest/tenant/sms/send
ops.monitor.cockpit.message.cloud-space-message-remote-url=************:9001/msn/v1.1/rest/tenant/app/send/message
ops.monitor.cockpit.message.key = BC-OPS-SCHEDULER
ops.monitor.cockpit.message.token = BC-OPS-SCHEDULER
ops.monitor.cockpit.message.source-system = BC-OPS-SCHEDULER

# Compensate Configuration
work.order.supervise.compensate = true

jasypt.encryptor.password = G0CvDz7oJn67sncklUKai09OI
jasypt.encryptor.iv-generator-classname = org.jasypt.iv.NoIvGenerator
jasypt.encryptor.algorithm = PBEWithMD5AndTripleDES

ops.monitor.cockpit.cmdb.base-url = http://bkapi.ccops.bcopstest.com
ops.monitor.cockpit.cmdb.bk-app-code = monitor-manager
ops.monitor.cockpit.cmdb.bk-app-secret = 1236b85c-11cf-40ba-a0b9-f3dea6e82857
ops.monitor.cockpit.cmdb.bk-username = admin

# thread pool
spring.task.pool.corePoolSize = 10
spring.task.pool.maxPoolSize = 20
spring.task.pool.keepAliveSeconds = 60
spring.task.pool.queueCapacity = 100
spring.task.pool.threadNamePrefix = cockpitThreadPool