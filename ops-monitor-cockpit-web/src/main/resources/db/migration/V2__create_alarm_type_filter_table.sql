-- 创建告警类型过滤器表
CREATE TABLE IF NOT EXISTS alarm_type_filter (
                                                 id SERIAL PRIMARY KEY,
                                                 filter_name VARCHAR(100) NOT NULL, -- 告警等级过滤器名称
    filter_id VARCHAR(100) NOT NULL, -- 告警过滤器ID
    enable SMALLINT NOT NULL DEFAULT 0, -- 是否启用（0-不启用，1-启用）
    sort_order INTEGER NOT NULL DEFAULT 0, -- 排序值（按排序值从大到小排列）
    create_time TIMESTAMP NOT NULL, -- 创建时间
    update_time TIMESTAMP , -- 更新时间
    create_by VARCHAR(50), -- 创建人
    update_by VARCHAR(50), -- 更新人
    picture bytea, -- 图片URL
  CONSTRAINT uk_type_filter_id UNIQUE (filter_id)
);

-- 添加表注释
COMMENT ON TABLE alarm_type_filter IS '告警类型过滤器表';
COMMENT ON COLUMN alarm_type_filter.id IS '主键ID';
COMMENT ON COLUMN alarm_type_filter.filter_name IS '告警类型过滤器名称';
COMMENT ON COLUMN alarm_type_filter.filter_id IS '告警过滤器ID';
COMMENT ON COLUMN alarm_type_filter.enable IS '是否启用（0-不启用，1-启用）';
COMMENT ON COLUMN alarm_type_filter.sort_order IS '排序值（按排序值从大到小排列）';
COMMENT ON COLUMN alarm_type_filter.create_time IS '创建时间';
COMMENT ON COLUMN alarm_type_filter.update_time IS '更新时间';
COMMENT ON COLUMN alarm_type_filter.create_by IS '创建人';
COMMENT ON COLUMN alarm_type_filter.update_by IS '更新人';
