/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.WorkOrderSuperviseDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.WorkOrderService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderNumVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderSuperviseResultVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderSuperviseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 工单访问（故障工单/事件工单/变更工单）控制器
 */
@RestController
@RequestMapping("/work/order")
public class WorkOrderController {
    
    @Autowired
    private WorkOrderService workOrderService;
    
    /**
     * 查询工单督办列表
     *
     * @return 督办工单列表
     */
    @GetMapping("/querySuperviseList")
    public WorkOrderSuperviseVO querySuperviseList() {
        return workOrderService.querySuperviseList();
    }
    
    /**
     * 查询正在实施的变更数量
     *
     * @return 正在实施的变更数量
     */
    @GetMapping("/queryOperatingChangeWorkOrderNum")
    public WorkOrderNumVO queryOperatingChangeWorkOrderNum() {
        return workOrderService.queryOperatingChangeWorkOrderNum();
    }
    
    /**
     * 工单批量督办
     * @param superviseDTOList 督办列表
     * @return 工单督办的结果
     */
    @PostMapping("/batchSupervise")
    public List<WorkOrderSuperviseResultVO> batchSupervise(@RequestBody List<WorkOrderSuperviseDTO> superviseDTOList) {
        return workOrderService.batchSupervise(superviseDTOList);
    }
    
}
