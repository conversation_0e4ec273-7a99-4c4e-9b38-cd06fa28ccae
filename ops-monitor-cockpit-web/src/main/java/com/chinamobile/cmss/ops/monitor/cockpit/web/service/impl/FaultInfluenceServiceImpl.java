/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.pojo.FaultAggregateBoardInfo;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.service.InfluenceService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.FaultInfluenceService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.FaultLevelAggregateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 故障影响面服务实现类
 */
@Service
public class FaultInfluenceServiceImpl implements FaultInfluenceService {
    
    @Autowired
    private InfluenceService influenceService;
    
    @Override
    public FaultLevelAggregateVO queryAggregateFaultLevel() {
        
        FaultAggregateBoardInfo faultAggregateBoardInfo = influenceService.queryFaultAggregateBoardInfo().getData();
        FaultAggregateBoardInfo.FaultLevelInfo faultLevelInfo = faultAggregateBoardInfo.getFaultLevelInfo();
        
        return FaultLevelAggregateVO.builder().generalEvent(faultLevelInfo.getGeneralEvent()).normalFault(faultLevelInfo.getNormalFault())
                .seriousFault(faultLevelInfo.getSeriousFault()).criticalFault(faultLevelInfo.getCriticalFault())
                .majorFault(faultLevelInfo.getMajorFault()).build();
        
    }
}
