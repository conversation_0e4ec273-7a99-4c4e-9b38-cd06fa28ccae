/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.DutyAttendanceDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.DutyAttendanceVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.MajorDutyVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Describe
 */
public interface DutyManagementService {
    
    /**
     * 获取当天专业值班信息
     * @return 值班信息
     */
    List<MajorDutyVO> getTodayMajorDuty();
    
    /**
     * 获取公告
     * @return string
     */
    String getNotice();
    
    /**
     * 保存通告
     * @param content 通告
     */
    void saveNotice(String content);
    
    /**
     * 查询考勤人数
     * @return 考勤
     */
    List<DutyAttendanceVO> getDutyAttendance();
    
    /**
     * 修改考勤人数
     * @param dutyAttendanceDTO dto
     */
    void saveDutyAttendance(DutyAttendanceDTO dutyAttendanceDTO);
}
