/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ParamInvalidException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ServerException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.UserUtils;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmLevelFilterStatisticInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.service.AlarmService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.AlarmLevelFilterDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmLevelFilter;
import com.chinamobile.cmss.ops.monitor.cockpit.web.mapper.AlarmLevelFilterMapper;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.AlarmLevelFilterService;

import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmLevelFilterStatisticVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 告警等级过滤器服务实现类
 */
@Service
public class AlarmLevelFilterServiceImpl implements AlarmLevelFilterService {
    
    @Autowired
    AlarmLevelFilterMapper alarmLevelFilterMapper;
    
    @Autowired
    private AlarmService alarmService;
    
    @Override
    @Transactional
    public boolean add(String filterName, String filterId, Short enable) {
        if (StrUtil.isBlank(filterId)) {
            throw new ParamInvalidException("过滤器id不能为空");
        }
        AlarmLevelFilter addEntity = new AlarmLevelFilter();
        addEntity.setFilterName(filterName);
        addEntity.setFilterId(filterId);
        addEntity.setEnable(enable != null ? enable : 0);
        addEntity.setSortOrder(0);
        addEntity.setCreateTime(LocalDateTime.now());
        // 设置创建人
        String currentUser = UserUtils.getUserName();
        addEntity.setCreateBy(currentUser);
        
        return alarmLevelFilterMapper.saveFilter(addEntity);
    }
    
    @Override
    public boolean update(AlarmLevelFilterDTO filterDTO) {
        AlarmLevelFilter entity = alarmLevelFilterMapper.getById(filterDTO.getId());
        if (entity == null) {
            return false;
        }
        
        filterDTO.setUpdateTime(LocalDateTime.now());
        // 设置更新人
        filterDTO.setUpdateBy(UserUtils.getUserName());
        
        return alarmLevelFilterMapper.updateFilterById(filterDTO);
    }
    
    @Override
    public void delete(Integer id) {
        
        if (id != null) {
            alarmLevelFilterMapper.removeById(id);
        }
    }
    
    @Override
    public boolean updateStatus(Integer id, Short enabled) {
        AlarmLevelFilter entity = alarmLevelFilterMapper.getById(id);
        if (entity == null) {
            return false;
        }
        
        entity.setEnable(enabled);
        entity.setUpdateTime(LocalDateTime.now());
        // 设置更新人
        entity.setUpdateBy(UserUtils.getUserName());
        
        return alarmLevelFilterMapper.updateStatus(entity);
    }
    
    @Override
    public List<AlarmLevelFilter> getAlarmLevelFilterList() {
        
        List<AlarmLevelFilter> list = alarmLevelFilterMapper.getAlarmLevelFilterList();
        return list;
    }
    
    @Override
    public boolean updateOrder(Map<Integer, Integer> sortOrder) {
        if (CollectionUtil.isNotEmpty(sortOrder)) {
            
            return alarmLevelFilterMapper.updateOrder(sortOrder);
            
        }
        return false;
    }
    
    /**
     * 根据id查询告警统计信息
     * @param startTime 发生时间
     * @param endTime   发生时间
     * @param id        过滤器id
     * @return AlarmLevelFilterStatisticVo
     */
    @Override
    public AlarmLevelFilterStatisticVo getAlarmLevelFilterStatistic(String startTime, String endTime, Integer id) {
        if (!ObjectUtil.isNotEmpty(id)) {
            return new AlarmLevelFilterStatisticVo();
        }
        AlarmLevelFilter alarmLevelFilter = alarmLevelFilterMapper.getById(id);
        
        AlarmResponse<AlarmLevelFilterStatisticInfos> response = alarmService.getLevelFilterStatistic(startTime, endTime, alarmLevelFilter.getFilterId());
        if (!"20000".equals(response.getMeta().getCode())) {
            throw new ServerException("告警接口请求失败");
        }
        AlarmLevelFilterStatisticInfos alarmLevelFilterStatisticInfos = response.getData();
        
        AlarmLevelFilterStatisticVo alarmLevelFilterStatisticVo = new AlarmLevelFilterStatisticVo();
        
        alarmLevelFilterStatisticVo.setId(id);
        alarmLevelFilterStatisticVo.setFilterName(alarmLevelFilter.getFilterName());
        alarmLevelFilterStatisticVo.setFilterId(alarmLevelFilter.getFilterId());
        alarmLevelFilterStatisticVo.setMajorAlarmCount(alarmLevelFilterStatisticInfos.getMajorAlarmCount());
        alarmLevelFilterStatisticVo.setImportantAlarmCount(alarmLevelFilterStatisticInfos.getImportantAlarmCount());
        alarmLevelFilterStatisticVo.setSecondARYAlarmCount(alarmLevelFilterStatisticInfos.getSecondARYAlarmCount());
        return alarmLevelFilterStatisticVo;
    }
}
