/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Describe
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DutyAttendanceVO {
    
    private Long id;
    
    // 值班日期
    private String dutyDate;
    
    // 对应的一组或二组模板名称
    private String teamTemplateName;
    
    // 专业名称
    private String major;
    
    // 班次起始时间（例如 09:00:00）
    private String startTime;
    
    // 班次结束时间（例如 18:00:00）
    private String endTime;
    
    // 应到人数
    private Integer expectedCount;
    
    // 实到人数
    private Integer actualCount;
    
    // 修改人
    private String modefier;
    
    // 更新时间
    private String updateTime;
    
}
