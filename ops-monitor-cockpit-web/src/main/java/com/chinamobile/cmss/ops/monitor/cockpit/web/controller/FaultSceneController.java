/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.CockpitFaultSceneDetail;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.MsgAuditTaskNum;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.FaultSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 故障作战室控制器
 */
@RestController
@RequestMapping("/fault/scene")
public class FaultSceneController {
    
    @Autowired
    private FaultSceneService faultSceneService;
    
    /**
     * 查询驾驶舱的故障作战室列表
     * @return 故障作战室列表
     */
    @PostMapping("/querySceneListForCockpit")
    public List<CockpitFaultSceneDetail> querySceneListForCockpit() {
        return faultSceneService.querySceneListForCockpit();
    }
    
    /**
     * 查询驾驶舱的消息审批任务数目
     * @return 消息审批的任务数目
     */
    @GetMapping("/queryMsgAuditTaskNum")
    public MsgAuditTaskNum queryMsgAuditTaskNum() {
        return faultSceneService.queryMsgAuditTaskNum();
    }
}
