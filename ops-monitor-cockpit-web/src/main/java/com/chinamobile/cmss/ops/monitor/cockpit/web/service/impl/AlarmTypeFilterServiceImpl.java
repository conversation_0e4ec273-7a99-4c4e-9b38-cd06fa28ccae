/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import cn.hutool.core.util.StrUtil;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ParamInvalidException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ServerException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.UserUtils;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto.AlarmTypeFilterStatisticDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.service.AlarmService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.AlarmTypeFilterDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmTypeFilter;
import com.chinamobile.cmss.ops.monitor.cockpit.web.mapper.AlarmTypeFilterMapper;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.AlarmTypeFilterService;

import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmTypeFilterCountVo;
import com.google.common.io.Files;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 告警等级过滤器服务实现类
 */
@Service
public class AlarmTypeFilterServiceImpl implements AlarmTypeFilterService {
    
    @Autowired
    private AlarmTypeFilterMapper alarmTypeFilterMapper;
    
    @Autowired
    private AlarmService alarmService;
    
    @Override
    @Transactional
    public boolean add(String filterName, String filterId, Short enable, MultipartFile avatarFile) throws IOException {
        String extension = getFileExtension(avatarFile);
        if (StrUtil.isBlank(filterId)) {
            throw new ParamInvalidException("过滤器id不能为空");
        }
        AlarmTypeFilter addEntity = new AlarmTypeFilter();
        addEntity.setExtension(extension);
        addEntity.setFilterName(filterName);
        addEntity.setFilterId(filterId);
        addEntity.setEnable(enable != null ? enable : 0);
        addEntity.setSortOrder(0);
        addEntity.setCreateTime(LocalDateTime.now());
        
        byte[] fileBytes = avatarFile.getBytes();
        addEntity.setPicture(fileBytes);
        // 设置创建人
        String currentUser = UserUtils.getUserName();
        addEntity.setCreateBy(currentUser);
        
        return alarmTypeFilterMapper.saveFilter(addEntity);
    }
    
    @Override
    public boolean update(Integer id, String filterName, String filterId,
                          Short enable, MultipartFile avatarFile) throws IOException {
        if (StrUtil.isBlank(filterId)) {
            throw new ParamInvalidException("过滤器id不能为空");
        }
        AlarmTypeFilter entity = alarmTypeFilterMapper.getById(id);
        if (entity == null) {
            return false;
        }
        AlarmTypeFilterDTO filterDTO = new AlarmTypeFilterDTO();
        filterDTO.setId(id);
        filterDTO.setFilterName(filterName);
        filterDTO.setFilterId(filterId);
        filterDTO.setEnable(enable != null ? enable : 0);
        
        String extension = getFileExtension(avatarFile);
        filterDTO.setExtension(extension);
        
        byte[] fileBytes = avatarFile.getBytes();
        filterDTO.setPicture(fileBytes);
        
        filterDTO.setUpdateTime(LocalDateTime.now());
        // 设置更新人
        filterDTO.setUpdateBy(UserUtils.getUserName());
        
        return alarmTypeFilterMapper.updateFilterById(filterDTO);
    }
    
    @Override
    public void delete(Integer id) {
        
        if (id != null) {
            alarmTypeFilterMapper.removeById(id);
        }
    }
    
    @Override
    public boolean updateStatus(Integer id, Short enabled) {
        AlarmTypeFilter entity = alarmTypeFilterMapper.getById(id);
        if (entity == null) {
            return false;
        }
        
        entity.setEnable(enabled);
        entity.setUpdateTime(LocalDateTime.now());
        // 设置更新人
        entity.setUpdateBy(UserUtils.getUserName());
        
        return alarmTypeFilterMapper.updateStatus(entity);
    }
    
    @Override
    public List<AlarmTypeFilter> getAlarmTypeFilterList() {
        
        List<AlarmTypeFilter> list = alarmTypeFilterMapper.getAlarmTypeFilterList();
        return list;
    }
    
    @Override
    public boolean updateOrder(Map<Integer, Integer> sortOrder) {
        if (CollectionUtil.isNotEmpty(sortOrder)) {
            
            return alarmTypeFilterMapper.updateOrder(sortOrder);
            
        }
        return false;
    }
    
    /**
     * 查询高等类型过滤器的重大告警数量
     * @param startTime start
     * @param endTime   end
     * @return list
     */
    @Override
    public List<AlarmTypeFilterCountVo> getMajorAlarmCount(String startTime, String endTime) {
        
        List<AlarmTypeFilterCountVo> getEnableAlarmTypeFilterList = alarmTypeFilterMapper.getEnableAlarmTypeFilterList();
        
        List<String> filterIds = getEnableAlarmTypeFilterList.stream()
                .map(AlarmTypeFilterCountVo::getFilterId)
                .toList();
        AlarmTypeFilterStatisticDTO alarmTypeFilterStatisticDto = AlarmTypeFilterStatisticDTO.builder()
                .startTime(startTime)
                .endTime(endTime)
                .filterIds(filterIds)
                .build();
        
        AlarmResponse<Map<String, Integer>> getMajorAlarmCountByFilter = alarmService.getMajorAlarmCountByFilter(alarmTypeFilterStatisticDto);
        if (!"20000".equals(getMajorAlarmCountByFilter.getMeta().getCode())) {
            throw new ServerException("告警接口请求失败");
        }
        Map<String, Integer> majorAlarmCountMap = getMajorAlarmCountByFilter.getData();
        
        for (AlarmTypeFilterCountVo alarmTypeFilterCountVo : getEnableAlarmTypeFilterList) {
            alarmTypeFilterCountVo.setMajorAlarmCount(majorAlarmCountMap.get(alarmTypeFilterCountVo.getFilterId()));
        }
        
        return getEnableAlarmTypeFilterList;
    }
    
    /**
     * 校验文件并获取拓展名
     * @param avatarFile file
     * @return string
     * @throws ParamInvalidException io
     */
    public String getFileExtension(MultipartFile avatarFile) {
        if (avatarFile == null || avatarFile.isEmpty()) {
            throw new ParamInvalidException("文件为空，请重新上传");
        }
        String originalFilename = avatarFile.getOriginalFilename();
        
        // 获取文件扩展名
        String fileExtension = null;
        if (originalFilename != null) {
            fileExtension = Files.getFileExtension(originalFilename);
        }
        
        // 定义允许的文件类型
        Set<String> allowedExtensions = new HashSet<>(Arrays.asList("png", "svg"));
        
        // 检查文件类型是否允许
        if (fileExtension == null || !allowedExtensions.contains(fileExtension.toLowerCase())) {
            throw new ParamInvalidException("只允许上传图片文件 ( PNG, SVG)");
        }
        return fileExtension;
    }
}
