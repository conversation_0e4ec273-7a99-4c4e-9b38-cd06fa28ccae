/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service;

import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmTypeFilter;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmTypeFilterCountVo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 告警等级过滤器服务接口
 */
public interface AlarmTypeFilterService {
    
    /**
     * 新增告警等级过滤器
     * @param filterName 告警等级过滤器名称
     * @param filterId   告警等级过滤器id
     * @param enable     是否启用
     * @param avatarFile 告警等级过滤器图标文件
     * @return bool
     * @throws IOException io
     */
    boolean add(String filterName, String filterId,
                Short enable, MultipartFile avatarFile) throws IOException;
    
    /**
     * 修改告警等级过滤器
     * @param id         id
     * @param filterName name
     * @param filterId   id
     * @param enable     1/0
     * @param avatarFile file
     * @return 是否成功
     * @throws IOException io
     */
    boolean update(Integer id, String filterName, String filterId,
                   Short enable, MultipartFile avatarFile) throws IOException;
    
    /**
     * 删除告警等级过滤器
     * @param id id
     * @throws IOException io
     */
    void delete(Integer id);
    
    /**
     * 启用/停用告警等级过滤器
     * @param id      告警等级过滤器ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean updateStatus(Integer id, Short enabled);
    
    /**
     * 查询
     * @return list
     */
    List<AlarmTypeFilter> getAlarmTypeFilterList();
    
    /**
     * 更新
     * @param sortOrder order
     * @return bool
     */
    boolean updateOrder(Map<Integer, Integer> sortOrder);
    
    /**
     * 查询高等类型过滤器的重大告警数量
     * @param startTime start
     * @param endTime   end
     * @return list
     */
    List<AlarmTypeFilterCountVo> getMajorAlarmCount(String startTime, String endTime);
}
