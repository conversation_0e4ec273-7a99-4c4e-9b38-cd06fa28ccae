/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.UserUtils;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto.FilterSearchView;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.service.AlarmService;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.dto.DateDutySelectByDayRangeDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.DateDutySelectByDayRangeResponseInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.DutyTemplateInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.SchedulerResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.service.SchedulerService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.DutyAttendanceDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.DutyAttendanceInsertEntity;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.DutyGroupEntity;
import com.chinamobile.cmss.ops.monitor.cockpit.web.mapper.DutyManagementMapper;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.DutyManagementService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.DutyAttendanceVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.MajorDutyVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.NoticeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Describe
 */
@Service
@Slf4j
public class DutyManagementServiceImpl implements DutyManagementService {
    
    @Value("#{'${duty-template-id}'.split(',')}")
    private List<String> dutyTemplateList;
    
    @Autowired
    private SchedulerService schedulerService;
    
    @Autowired
    private DutyManagementMapper managementMapper;
    
    @Autowired
    private AlarmService alarmService;
    
    @Autowired
    private DutyManagementMapper dutyManagementMapper;
    
    /**
     * 获取当天专业值班信息
     * @return 值班信息
     */
    @Override
    public List<MajorDutyVO> getTodayMajorDuty() {
        List<MajorDutyVO> result = new ArrayList<>();
        
        // 1. 获取所有值班模板
        SchedulerResponse<List<DutyTemplateInfos>> dutyTemplateInfos = schedulerService.querySchedulingTemplateList(0, null, null);
        if (!CollectionUtil.isNotEmpty(dutyTemplateInfos.getData())) {
            return result;
        }
        
        // 初始化 Map 存储模板信息
        Map<String, DutyGroupEntity> dutyLeaderMap = new HashMap<>();
        Map<String, DutyGroupEntity> groupOneMap = new HashMap<>();
        Map<String, DutyGroupEntity> groupTwoMap = new HashMap<>();
        
        // 模板分类
        for (DutyTemplateInfos item : dutyTemplateInfos.getData()) {
            String name = item.getName();
            if (name == null) {
                continue;
            }
            
            DutyGroupEntity info = new DutyGroupEntity(name, item.getVersionCode());
            
            if (name.startsWith("驾驶舱-")) {
                if (name.contains("一组")) {
                    groupOneMap.put(item.getId(), info);
                } else if (name.contains("二组")) {
                    groupTwoMap.put(item.getId(), info);
                }
            }
            
            // 值班长（读配置）
            if (dutyTemplateList.contains(item.getId())) {
                dutyLeaderMap.put(item.getId(), info);
            }
        }
        
        // 当前查询日
        Date queryDay = DateUtil.parse(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN), DatePattern.NORM_DATE_PATTERN);
        
        // 一组
        MajorDutyVO oneGroup = buildMajorDutyVO(dutyTemplateList.get(0), dutyLeaderMap, groupOneMap, queryDay);
        result.add(oneGroup);
        
        // 二组
        MajorDutyVO twoGroup = buildMajorDutyVO(dutyTemplateList.get(1), dutyLeaderMap, groupTwoMap, queryDay);
        result.add(twoGroup);
        
        // 初始化考勤数据
        initDutyAttendance(result);
        return result;
    }
    
    /**
     * 初始化考勤信息表
     * @param result 值班信息
     */
    public void initDutyAttendance(List<MajorDutyVO> result) {
        if (CollectionUtil.isEmpty(result)) {
            return;
        }
        
        for (MajorDutyVO item : result) {
            if (StrUtil.isBlank(item.getDutyTemplateName())) {
                continue;
            }
            
            List<MajorDutyVO.MajorDutyPersonVO> personList = item.getMajorDutyPersonVOList();
            if (CollectionUtil.isEmpty(personList)) {
                continue;
            }
            
            for (MajorDutyVO.MajorDutyPersonVO majorDetail : personList) {
                if (StrUtil.isBlank(majorDetail.getMajorName())) {
                    continue;
                }
                
                DutyAttendanceInsertEntity entity = DutyAttendanceInsertEntity.builder()
                        .dutyDate(StrUtil.isNotBlank(majorDetail.getDutyDate()) ? majorDetail.getDutyDate() : DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN))
                        .dutyTemplateName(item.getDutyTemplateName())
                        .majorName(majorDetail.getMajorName())
                        .startTime(majorDetail.getStartTime())
                        .endTime(majorDetail.getEndTime())
                        .build();
                
                // 判断是否已存在记录
                List<DutyAttendanceVO> existing = managementMapper.findDutyTemplateExit(entity);
                if (CollectionUtil.isEmpty(existing)) {
                    entity.setExpectedDutyCount(majorDetail.getExpectedDutyCount());
                    managementMapper.insertDutyTemplate(entity);
                }
            }
        }
    }
    
    /**
     * 获取公告
     * @return string
     */
    @Override
    public String getNotice() {
        List<NoticeVO> noticeVO = managementMapper.findNotice();
        if (CollectionUtil.isNotEmpty(noticeVO) && StrUtil.isNotBlank(noticeVO.get(0).getContent())) {
            return noticeVO.get(0).getContent();
        } else {
            
            long endTime = Instant.now().toEpochMilli();
            long startTime = Instant.now().minusSeconds(24 * 60 * 60).toEpochMilli();
            FilterSearchView filterSearchView = new FilterSearchView();
            filterSearchView.setIsProject(0);
            filterSearchView.setStatus(0);
            filterSearchView.setAckStatus(0);
            filterSearchView.setFirstOccurrenceTime(new String[]{String.valueOf(startTime), String.valueOf(endTime)});
            
            AlarmResponse<Map<String, Integer[]>> getAlarmNumResponse = alarmService.getAlarmNum(filterSearchView);
            Integer alarmCount = 0;
            if ("20000".equals(getAlarmNumResponse.getMeta().getCode())) {
                Integer[] alarmCountArray = getAlarmNumResponse.getData().get("alarm_level_count");
                alarmCount = alarmCountArray[0] + alarmCountArray[1];
            }
            return "近24小时内未清除的重大重要告警总数：" + alarmCount;
        }
    }
    
    /**
     * 保存通告
     * @param content 通告
     */
    @Override
    public void saveNotice(String content) {
        
        List<NoticeVO> existing = managementMapper.findNotice();
        if (CollectionUtil.isNotEmpty(existing)) {
            managementMapper.updateNotice(content);
        } else {
            NoticeVO newEntity = new NoticeVO();
            newEntity.setId(1);
            newEntity.setContent(content);
            managementMapper.insertNotice(newEntity);
        }
        
    }
    
    private MajorDutyVO buildMajorDutyVO(String templateId,
                                         Map<String, DutyGroupEntity> leaderMap,
                                         Map<String, DutyGroupEntity> groupMap,
                                         Date queryDay) {
        // 初始化默认值
        String dutyTemplateName = "";
        String dutyLeader = "";
        String dutyLeaderPhone = "";
        List<MajorDutyVO.MajorDutyPersonVO> majorDutyPersonList = new ArrayList<>();
        
        // 处理 dutyLeader
        DutyGroupEntity leaderInfo = leaderMap.get(templateId);
        if (leaderInfo != null) {
            dutyTemplateName = leaderInfo.getName();
            
            DateDutySelectByDayRangeDTO leaderDTO = DateDutySelectByDayRangeDTO.builder()
                    .queryStartDay(queryDay)
                    .queryEndDay(queryDay)
                    .templateId(templateId)
                    .templateVersionCode(leaderInfo.getVersionCode())
                    .build();
            SchedulerResponse<DateDutySelectByDayRangeResponseInfos> leaderResponse = schedulerService.selectByDayRange(leaderDTO);
            MajorDutyVO.MajorDutyPersonVO leaderUser = findCurrentDutyPerson(leaderResponse.getData());
            if (leaderUser != null) {
                dutyLeader = leaderUser.getDutyPerson();
                dutyLeaderPhone = leaderUser.getDutyPersonPhone();
            }
        }
        
        // 处理 groupMap 专业值班人员
        if (MapUtil.isNotEmpty(groupMap)) {
            for (Map.Entry<String, DutyGroupEntity> entry : groupMap.entrySet()) {
                String id = entry.getKey();
                DutyGroupEntity entity = entry.getValue();
                DateDutySelectByDayRangeDTO groupDTO = DateDutySelectByDayRangeDTO.builder()
                        .queryStartDay(queryDay)
                        .queryEndDay(queryDay)
                        .templateId(id)
                        .templateVersionCode(entity.getVersionCode())
                        .build();
                SchedulerResponse<DateDutySelectByDayRangeResponseInfos> groupResponse = schedulerService.selectByDayRange(groupDTO);
                
                MajorDutyVO.MajorDutyPersonVO user = findCurrentDutyPerson(groupResponse.getData());
                if (user == null) {
                    user = new MajorDutyVO.MajorDutyPersonVO();
                }
                user.setMajorName(extractProfession(entity.getName()));
                majorDutyPersonList.add(user);
            }
        }
        
        return MajorDutyVO.builder()
                .dutyTemplateName(dutyTemplateName)
                .dutyLeader(dutyLeader)
                .dutyLeaderPhone(dutyLeaderPhone)
                .majorDutyPersonVOList(majorDutyPersonList)
                .build();
    }
    
    /**
     * 查询考勤人数
     * @return 考勤
     */
    @Override
    public List<DutyAttendanceVO> getDutyAttendance() {
        
        // 当前时间
        String dutyDate = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        String nowStr = DateUtil.format(DateUtil.date(), "HH:mm:ss");
        
        List<DutyAttendanceVO> result = dutyManagementMapper.findDutyAttendance(dutyDate, nowStr);
        return result;
    }
    
    /**
     * 修改考勤人数
     * @param dutyAttendanceDTO dto
     */
    @Override
    public void saveDutyAttendance(DutyAttendanceDTO dutyAttendanceDTO) {
        String username = UserUtils.getUserName();
        dutyAttendanceDTO.setModefier(username);
        dutyManagementMapper.updateDutyAttendance(dutyAttendanceDTO);
    }
    
    /**
     * 查找当前值班人员信息
     * @param dutyResponse dto
     * @return 值班人员信息
     */
    public MajorDutyVO.MajorDutyPersonVO findCurrentDutyPerson(DateDutySelectByDayRangeResponseInfos dutyResponse) {
        if (dutyResponse == null
                || CollectionUtil.isEmpty(dutyResponse.getTbody())
                || MapUtil.isEmpty(dutyResponse.getTbody().get(0).getDutyMap())) {
            return null;
        }
        
        String nowStr = DateUtil.format(new Date(), "HH:mm:ss");
        DateTime now = DateUtil.parse(nowStr, "HH:mm:ss");
        
        for (DateDutySelectByDayRangeResponseInfos.DutyConfigShiftTimeResponseVO shift : dutyResponse.getShiftTime()) {
            String startStr = shift.getStartTime();
            String endStr = shift.getEndTime();
            
            // 解析 startTime
            DateTime start = DateUtil.parse(startStr, "HH:mm:ss");
            DateTime end;
            
            // 特殊处理 endTime = "24:00:00"
            if ("24:00:00".equals(endStr)) {
                end = DateUtil.beginOfDay(now).offset(DateField.DAY_OF_YEAR, 1);
            } else {
                end = DateUtil.parse(endStr, "HH:mm:ss");
            }
            
            // 判断当前时间是否在时间段内
            if (DateUtil.isIn(now, start, end)) {
                String code = shift.getCode();
                List<DateDutySelectByDayRangeResponseInfos.DateDutyBodyResponseVO.DateDutyUserResponseVO> userList =
                        dutyResponse.getTbody().get(0).getDutyMap().getOrDefault(code, Collections.emptyList());
                
                int totalCount = userList.size();
                
                return userList.stream()
                        .filter(p -> p.getRoleType() != null && p.getRoleType() == 1)
                        .findFirst()
                        .map(p -> {
                            MajorDutyVO.MajorDutyPersonVO vo = new MajorDutyVO.MajorDutyPersonVO();
                            vo.setDutyPerson(p.getFullName());
                            vo.setDutyPersonPhone(p.getPhone());
                            vo.setExpectedDutyCount(totalCount);
                            vo.setStartTime(startStr);
                            vo.setEndTime(endStr);
                            vo.setDutyDate(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN));
                            return vo;
                        })
                        .orElse(null);
            }
        }
        
        return null;
    }
    
    /**
     * 提取专业名称
     * @param input 值班模板名
     * @return 专业
     */
    public String extractProfession(String input) {
        if (StrUtil.isBlank(input)) {
            return null;
        }
        
        // 正则：提取“驾驶舱-xxx专业一组”或“驾驶舱-xxx专业二组”中的“xxx专业”
        Pattern pattern = Pattern.compile("驾驶舱-([^-组]+)(?:一组|二组)");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }
    
}