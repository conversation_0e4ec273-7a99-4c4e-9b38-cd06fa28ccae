/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.dto;

import jakarta.validation.Valid;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

import java.time.LocalDateTime;

/**
 * 告警等级过滤器DTO
 */
@Data
@Valid
public class AlarmLevelFilterDTO {
    
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * 告警等级过滤器名称
     */
    @NotBlank(message = "告警等级过滤器名称不能为空")
    private String filterName;
    
    /**
     * 告警过滤器ID
     */
    @NotBlank(message = "告警过滤器ID不能为空")
    private String filterId;
    
    /**
     * 是否启用（0-不启用，1-启用）
     */
    private Short enable;
    
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
}
