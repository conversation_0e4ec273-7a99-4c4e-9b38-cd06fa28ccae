/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Describe
 */
@Data
public class FaultListVO {
    
    /**
     * 总数
     */
    private Long total;
    
    private List<FaultEntity> faultEntity;
    
    @Data
    public static class FaultEntity {
        
        /**
         * 工单号
         */
        private String cell1;
        
        /**
         * 工单标题
         */
        private String incidentTitle;
        
        /**
         * 是否调度（是否建立对应工单号的作战室）
         */
        private Boolean isScheduled;
        
        /**
         * 工单状态
         */
        private String state;
        
        /**
         * 故障等级
         */
        private String faultLevel;
        
        /**
         * 故障一级分类
         */
        private String faultType1;
        
        /**
         * 故障二级分类
         */
        private String faultType2;
        
        /**
         * 故障三级分类
         */
        private String faultType3;
        
        /**
         * 建单时间（用于计算处理时长）
         */
        private String createTime;
        
        /**
         * 处理时长（当前时间 - 建单时间）
         */
        private String handleDuration;
    }
    
}
