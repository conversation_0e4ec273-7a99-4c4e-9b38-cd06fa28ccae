/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Describe
 */
@Data
public class AlarmListVO {
    
    /**
     * 总数
     */
    private Long total;
    
    private List<AlarmEntity> alarmEntity;
    
    @Data
    public static class AlarmEntity {
        
        /**
         * 告警ID
         */
        private String alarmId;
        
        /**
         * 告警标题
         */
        private String alarmTitle;
        
        /**
         * 是否确认
         */
        private String ackStatusLabel;
        
        /**
         * 最后发生时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private LocalDateTime lastAlarmTime;
        
        /**
         * 告警级别（critical/major/minor）
         */
        private String alarmLevelLabel;
        
        /**
         * 派单状态（pending/assigned/closed）
         */
        private String dispatchStatus;
    }
    
}
