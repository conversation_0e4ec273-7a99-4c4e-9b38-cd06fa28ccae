/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.AlarmLevelFilterDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmLevelFilter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 告警等级过滤器Mapper接口
 */
@Mapper
public interface AlarmLevelFilterMapper extends BaseMapper<AlarmLevelFilter> {
    
    /**
     * 查询
     * @return list
     */
    List<AlarmLevelFilter> getAlarmLevelFilterList();
    
    /**
     * 启用
     * @param entity e
     * @return r
     */
    boolean updateStatus(AlarmLevelFilter entity);
    
    /**
     * 更新
     * @param filterDTO f
     * @return up
     */
    boolean updateFilterById(AlarmLevelFilterDTO filterDTO);
    
    /**
     * 排序
     * @param sortOrder 排序
     * @return up
     */
    boolean updateOrder(@Param("sortOrder") Map<Integer, Integer> sortOrder);
    
    /**
     * save
     * @param addEntity add
     * @return sa
     */
    boolean saveFilter(AlarmLevelFilter addEntity);
    
    /**
     * 查询
     * @param id id
     * @return all
     */
    AlarmLevelFilter getById(Integer id);
    
    /**
     * del
     * @param id id
     */
    void removeById(Integer id);
}
