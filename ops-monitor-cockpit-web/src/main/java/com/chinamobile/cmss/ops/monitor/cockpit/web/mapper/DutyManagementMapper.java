/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.mapper;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.DutyAttendanceDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.DutyAttendanceInsertEntity;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.DutyAttendanceVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.NoticeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Describe
 */
@Mapper
public interface DutyManagementMapper {
    
    /**
     * 查询公告
     * @return 公告
     */
    List<NoticeVO> findNotice();
    
    /**
     * 更新公告
     * @param content 公告
     */
    void updateNotice(String content);
    
    /**
     * 插入公告
     * @param newEntity 公告
     */
    void insertNotice(NoticeVO newEntity);
    
    /**
     * 查询考勤人人数
     * @param dutyDate 日期
     * @param nowStr 时间
     * @return list
     */
    List<DutyAttendanceVO> findDutyAttendance(@Param("dutyDate") String dutyDate, @Param("nowStr") String nowStr);
    
    /**
     * 是否已存在记录
     * @param dutyAttendanceInsertEntity dto
     * @return list
     */
    List<DutyAttendanceVO> findDutyTemplateExit(DutyAttendanceInsertEntity dutyAttendanceInsertEntity);
    
    /**
     * 插入考勤记录
     * @param dutyAttendanceInsertEntity entity
     */
    void insertDutyTemplate(DutyAttendanceInsertEntity dutyAttendanceInsertEntity);
    
    /**
     * 修改考勤记录
     * @param dutyAttendanceDTO dto
     */
    void updateDutyAttendance(DutyAttendanceDTO dutyAttendanceDTO);
}
