/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Describe
 */
@Data
public class HoneycombChartOverviewVO {
    
    /**
     * 总数
     */
    private Long total;
    
    /**
     * 根据资源池总览
     */
    private List<RegionOverview> regionOverviewList;
    
    @Data
    public static class RegionOverview {
        
        /**
         * 资源池名称
         */
        private String regionName;
        
        /**
         * 资源池编码
         */
        private String regionCode;
        
        /**
         * 故障数量
         */
        private Integer faultCount;
        
        /**
         * 重大告警数量
         */
        private Integer majorAlarmCount;
        
        /**
         * 重要告警数量
         */
        private Integer importantAlarmCount;
        
        /**
         * 安全攻击类告警数量
         */
        private Integer securityAttacksAlarmCount;
        
        /**
         * 变更中
         */
        private Integer inChangeCount;
        
        /**
         * 全局资源池数量
         */
        private Integer allRegionFlag;
        
        /**
         * 异常类型数量
         */
        private Integer abnormalTypeCount = 0;
        
        /**
         * 异常类型代码列表字段
         */
        private List<String> abnormalTypeCodes = List.of("5");
    }
    
}
