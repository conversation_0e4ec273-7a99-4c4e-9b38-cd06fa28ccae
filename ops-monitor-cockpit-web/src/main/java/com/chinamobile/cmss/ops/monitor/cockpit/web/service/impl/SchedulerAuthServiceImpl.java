/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.AESGCMBase64KeyInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.service.SchedulerService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.SchedulerAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Describe
 */
@Service
public class SchedulerAuthServiceImpl implements SchedulerAuthService {
    
    @Autowired
    private SchedulerService schedulerService;
    
    /**
     * 查询调度密钥
     * @return 密钥
     */
    @Override
    public AESGCMBase64KeyInfos queryAESGCMBase64Key() {
        return schedulerService.queryAESGCMBase64Key().getData();
    }
}
