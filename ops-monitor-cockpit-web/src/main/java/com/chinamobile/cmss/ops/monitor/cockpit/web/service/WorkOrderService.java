/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.WorkOrderSuperviseDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderNumVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderSuperviseResultVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderSuperviseVO;

import java.util.List;

/**
 * 工单服务接口
 */
public interface WorkOrderService {
    
    /**
     * 查询驾驶舱的工单督办列表（故障工单/事件工单）
     * @return 工单督办列表信息
     */
    WorkOrderSuperviseVO querySuperviseList();
    
    /**
     * 查询驾驶舱的正在实施中的变更工单数目
     * @return 正在实施中的变更工单数目
     */
    WorkOrderNumVO queryOperatingChangeWorkOrderNum();
    
    /**
     * 批量督办
     * @param superviseDTOList 工单批量督办入参
     * @return 批量督办结果
     */
    List<WorkOrderSuperviseResultVO> batchSupervise(List<WorkOrderSuperviseDTO> superviseDTOList);
}
