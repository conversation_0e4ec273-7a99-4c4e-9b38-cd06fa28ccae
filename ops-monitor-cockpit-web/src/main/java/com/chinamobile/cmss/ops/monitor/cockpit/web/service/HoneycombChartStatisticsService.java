/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.HoneycombChartListQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.HoneycombChartStatisticsDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.BkOperatorTypeVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.BkRegionVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.ChangeListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.FaultListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.HoneycombChartOverviewVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <AUTHOR>
 * @Describe
 */
public interface HoneycombChartStatisticsService {
    
    /**
     * 从cmdb根据资源池类型查询资源池
     * @param operationType 资源池类型
     * @return 资源池列表
     */
    List<BkRegionVO> getRegionFromCmdb(String operationType);
    
    /**
     * 蜂窝图总览
     * @param honeycombChartStatisticsDTO 资源池类型、总览类型
     * @return 蜂窝图
     */
    HoneycombChartOverviewVO getHoneycombChartOverview(HoneycombChartStatisticsDTO honeycombChartStatisticsDTO);
    
    /**
     * 根据资源池查询告警
     * @param dto 资源池编码
     * @return 告警列表
     */
    AlarmListVO getAlarmByRegion(HoneycombChartListQueryDTO dto);
    
    /**
     * 获取资源池类型枚举
     * @return 源池类型枚举列表
     */
    List<BkOperatorTypeVO> getOperationTypeFromCmdb();
    
    /**
     * 查询故障列表
     * @param dto dto
     * @return 列表
     */
    FaultListVO getFaultByRegion(@Valid HoneycombChartListQueryDTO dto);
    
    /**
     * 安全攻击列表
     * @param dto 资源池编码
     * @return 列表
     */
    AlarmListVO getSecurityAttacksAlarmByRegion(@Valid HoneycombChartListQueryDTO dto);
    
    /**
     * 变更列表
     * @param dto 资源池编码
     * @return 列表
     */
    ChangeListVO getChangeByRegion(@Valid HoneycombChartListQueryDTO dto);
}
