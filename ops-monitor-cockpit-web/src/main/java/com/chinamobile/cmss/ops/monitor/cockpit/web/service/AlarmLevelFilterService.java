/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.AlarmLevelFilterDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmLevelFilter;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmLevelFilterStatisticVo;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 告警等级过滤器服务接口
 */
public interface AlarmLevelFilterService {
    
    /**
     * 新增告警等级过滤器
     * @param filterName filterName
     * @param filterId   filterId
     * @param enable     enable
     * @return bool
     */
    boolean add(String filterName, String filterId,
                Short enable);
    
    /**
     * 修改告警等级过滤器
     * @param filterDTO  filterDTO
     * @return bool
     */
    boolean update(AlarmLevelFilterDTO filterDTO);
    
    /**
     * 删除告警等级过滤器
     * @param id 告警等级过滤器ID
     * @throws IOException io
     */
    void delete(Integer id);
    
    /**
     * 启用/停用告警等级过滤器
     * @param id      告警等级过滤器ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean updateStatus(Integer id, Short enabled);
    
    /**
     * 查询
     * @return list
     */
    List<AlarmLevelFilter> getAlarmLevelFilterList();
    
    /**
     * 更新
     * @param sortOrder order
     * @return bool
     */
    boolean updateOrder(Map<Integer, Integer> sortOrder);
    
    /**
     * 查询等级过滤器的告警数量
     * @param startTime 开始
     * @param endTime 结束
     * @param id id
     * @return AlarmLevelFilterStatisticVo
     */
    AlarmLevelFilterStatisticVo getAlarmLevelFilterStatistic(String startTime, String endTime, Integer id);
}
