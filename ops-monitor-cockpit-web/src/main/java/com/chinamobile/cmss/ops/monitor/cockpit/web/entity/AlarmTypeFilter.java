/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 告警类型过滤器实体类
 */
@Data
@TableName("alarm_type_filter")
public class AlarmTypeFilter {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 告警等级过滤器名称：驾驶舱展示名称
     */
    private String filterName;
    
    /**
     * 告警过滤器ID：告警过滤器
     */
    private String filterId;
    
    /**
     * 是否启用（0-不启用，1-启用）
     */
    private Short enable;
    
    /**
     * 排序值（按排序值从大到小排列）
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 头像
     */
    private byte[] picture;
    
    /**
     * 拓展名
     */
    private String extension;
}
