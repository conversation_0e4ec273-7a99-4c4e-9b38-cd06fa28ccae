/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lingmeng
 * @Date: 2025/5/11 11:07
 * @Description: 工单分类(全部/即将超时/已超时)督办VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WorkOrderClassifySuperviseVO {
    
    private Integer workOrderNum;
    
    private List<WorkOrderSuperviseItemVO> workOrderSuperviseItemVOList;
    
}
