/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class FaultLevelAggregateVO {
    
    /**
     * 一般事件
     */
    @JsonProperty("generalEvent")
    private Integer generalEvent;
    
    /**
     * 一般故障
     */
    @JsonProperty("normalFault")
    private Integer normalFault;
    
    /**
     * 较大故障
     */
    @JsonProperty("seriousFault")
    private Integer seriousFault;
    
    /**
     * 重大故障
     */
    @JsonProperty("majorFault")
    private Integer majorFault;
    
    /**
     * 严重故障
     */
    @JsonProperty("criticalFault")
    private Integer criticalFault;
    
}
