/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.HoneycombChartListQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.HoneycombChartStatisticsDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.HoneycombChartStatisticsService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.BkOperatorTypeVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.BkRegionVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.ChangeListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.FaultListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.HoneycombChartOverviewVO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/honeycomb-chart/statistics")
public class HoneycombChartStatisticsController {
    
    @Resource
    private HoneycombChartStatisticsService honeycombChartStatisticsService;
    
    /**
     * 从cmdb获取资源池类型列表
     * @return 资源池类型
     */
    @GetMapping("/operation-type")
    public List<BkOperatorTypeVO> getOperationType() {
        List<BkOperatorTypeVO> operationTypeList = honeycombChartStatisticsService.getOperationTypeFromCmdb();
        return operationTypeList;
    }
    
    /**
     * 从cmdb获取资源池
     * @param operationType 资源池类型
     * @return list
     */
    @GetMapping("/region")
    public List<BkRegionVO> getRegion(@RequestParam(value = "operationType", required = false) String operationType) {
        List<BkRegionVO> regionList = honeycombChartStatisticsService.getRegionFromCmdb(operationType);
        return regionList;
    }
    
    /**
     * 蜂窝图总览
     * @param honeycombChartStatisticsDTO 查询参数
     * @return 蜂窝图
     */
    @PostMapping("/overview")
    public HoneycombChartOverviewVO honeycombChartOverview(@RequestBody @Validated HoneycombChartStatisticsDTO honeycombChartStatisticsDTO) {
        return honeycombChartStatisticsService.getHoneycombChartOverview(honeycombChartStatisticsDTO);
    }
    
    /**
     * 告警列表
     * @param dto 资源池编码
     * @return 告警列表
     */
    @PostMapping("/alarm/list")
    public AlarmListVO getAlarmList(@RequestBody @Valid HoneycombChartListQueryDTO dto) {
        return honeycombChartStatisticsService.getAlarmByRegion(dto);
    }
    
    /**
     * 故障列表
     * @param dto 资源池编码
     * @return 列表
     */
    @PostMapping("/fault/list")
    public FaultListVO getFaultList(@RequestBody @Valid HoneycombChartListQueryDTO dto) {
        return honeycombChartStatisticsService.getFaultByRegion(dto);
    }
    
    /**
     * 安全攻击列表
     * @param dto 资源池编码
     * @return 列表
     */
    @PostMapping("/security-attacks-alarm/list")
    public AlarmListVO getSecurityAttacksAlarmList(@RequestBody @Valid HoneycombChartListQueryDTO dto) {
        return honeycombChartStatisticsService.getSecurityAttacksAlarmByRegion(dto);
    }
    
    /**
     * 变更列表
     * @param dto 资源池编码
     * @return 列表
     */
    @PostMapping("/change/list")
    public ChangeListVO getChangeList(@RequestBody @Valid HoneycombChartListQueryDTO dto) {
        return honeycombChartStatisticsService.getChangeByRegion(dto);
    }
}
