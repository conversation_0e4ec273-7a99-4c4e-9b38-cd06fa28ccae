/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.CockpitFaultSceneDetail;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.MsgAuditTaskNum;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.FaultSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.service.SchedulerService;

import java.util.List;

/**
 * 故障作战室服务实现类
 */
@Service
public class FaultSceneServiceImpl implements FaultSceneService {
    
    @Autowired
    private SchedulerService schedulerService;
    
    @Override
    public List<CockpitFaultSceneDetail> querySceneListForCockpit() {
        List<CockpitFaultSceneDetail> cockpitFaultSceneDetailList = schedulerService.querySceneListForCockpit().getData();
        // 处理工单号，如果有多个工单号则只返回第一个
        if (CollectionUtil.isNotEmpty(cockpitFaultSceneDetailList)) {
            for (CockpitFaultSceneDetail cockpitFaultSceneDetail : cockpitFaultSceneDetailList) {
                String relatedOrderNo = cockpitFaultSceneDetail.getRelatedOrderNo();
                if (StrUtil.isNotBlank(relatedOrderNo)) {
                    String[] relatedOrderNoArr = relatedOrderNo.split(";");
                    cockpitFaultSceneDetail.setRelatedOrderNo(relatedOrderNoArr[0]);
                }
            }
        }
        return cockpitFaultSceneDetailList;
    }
    
    @Override
    public MsgAuditTaskNum queryMsgAuditTaskNum() {
        
        return schedulerService.queryAuditTaskNum().getData();
    }
}
