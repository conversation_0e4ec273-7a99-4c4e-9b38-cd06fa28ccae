/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.AlarmTypeFilterDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmTypeFilter;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmTypeFilterCountVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 告警类型过滤器Mapper接口
 */
@Mapper
public interface AlarmTypeFilterMapper extends BaseMapper<AlarmTypeFilter> {
    
    /**
     * 查询
     * @return list
     */
    List<AlarmTypeFilter> getAlarmTypeFilterList();
    
    /**
     * 启用
     * @param entity e
     * @return up
     */
    boolean updateStatus(AlarmTypeFilter entity);
    
    /**
     * 更新
     * @param filterDTO f
     * @return up
     */
    boolean updateFilterById(AlarmTypeFilterDTO filterDTO);
    
    /**
     * 排序
     * @param sortOrder 排序
     * @return bool
     */
    boolean updateOrder(@Param("sortOrder") Map<Integer, Integer> sortOrder);
    
    /**
     * save
     * @param addEntity add
     * @return bool
     */
    boolean saveFilter(AlarmTypeFilter addEntity);
    
    /**
     * 查询
     * @param id id
     * @return all
     */
    AlarmTypeFilter getById(Integer id);
    
    /**
     * del
     * @param id id
     */
    void removeById(Integer id);
    
    /**
     * 获取已启用的
     * @return list
     */
    List<AlarmTypeFilterCountVo> getEnableAlarmTypeFilterList();
}
