/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 工单督办实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("coc_work_order_supervise")
@Builder
public class CocWorkOrderSupervise {
    
    /**
     * 主键ID
     */
    private String id;
    
    /**
     * 工单号
     */
    private String workOrderNo;
    
    /**
     * 督办次数
     */
    private Integer superviseNum;
    
    /**
     * 最近督办时间
     */
    private Date lastSuperviseTime;
    
}