/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.entity;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Describe
 */
@Builder
@Data
public class DutyAttendanceInsertEntity {
    
    /**
     * 值班日期
     */
    private String dutyDate;
    
    /**
     * 班组模板名（匹配字段，原表字段可能是 team_template_id 或 name）
     */
    private String dutyTemplateName;
    
    /**
     * 专业名称
     */
    private String majorName;
    
    /**
     * 班次开始时间，格式 "HH:mm:ss"
     */
    private String startTime;
    
    /**
     * 班次结束时间，格式 "HH:mm:ss"
     */
    private String endTime;
    
    /**
     * 应到人数
     */
    private Integer expectedDutyCount;
    
    /**
     * 实到人数
     */
    private Integer actualCount;
    
    /**
     * 修改人
     */
    private String modefier;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
