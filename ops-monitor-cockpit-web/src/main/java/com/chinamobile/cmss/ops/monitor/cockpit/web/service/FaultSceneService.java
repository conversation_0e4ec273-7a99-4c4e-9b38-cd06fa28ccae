/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service;

import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.CockpitFaultSceneDetail;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.MsgAuditTaskNum;

import java.util.List;

/**
 * 故障作战室服务接口
 */
public interface FaultSceneService {
    
    /**
     * 查询故障作战室-活动作战室列表
     * @return 活动作战室列表
     */
    List<CockpitFaultSceneDetail> querySceneListForCockpit();
    
    /**
     * 查询消息审批的任务数目
     * @return 消息审批的任务数目
     */
    MsgAuditTaskNum queryMsgAuditTaskNum();
}
