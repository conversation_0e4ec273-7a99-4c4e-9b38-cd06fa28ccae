/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmTypeFilter;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.AlarmTypeFilterService;

import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmTypeFilterCountVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 告警等级过滤器控制器
 */
@RestController
@RequestMapping("/alarmTypeFilter")
public class AlarmTypeFilterController {
    
    @Autowired
    private AlarmTypeFilterService alarmTypeFilterService;
    
    /**
     * 新增告警等级过滤器
     * @param filterName 告警等级过滤器名称
     * @param filterId   f
     * @param enable     e
     * @param avatarFile a
     * @return a
     * @throws IOException i
     */
    @PostMapping("/add")
    public boolean addAlarmTypeFilter(@RequestParam("filterName") String filterName, @RequestParam("filterId") String filterId,
                                      @RequestParam("enable") Short enable, @RequestParam("avatarFile") MultipartFile avatarFile) throws IOException {
        return alarmTypeFilterService.add(filterName, filterId, enable, avatarFile);
    }
    
    /**
     * 删除告警等级过滤器
     * @param id 告警等级过滤器ID
     * @throws IOException io
     */
    @DeleteMapping("/del")
    public void deleteAlarmTypeFilter(@RequestParam("id") Integer id) {
        alarmTypeFilterService.delete(id);
    }
    
    /**
     * 查询告警等级过滤器
     * @return list
     */
    @GetMapping("/list")
    public List<AlarmTypeFilter> alarmTypeFilterList() {
        return alarmTypeFilterService.getAlarmTypeFilterList();
    }
    
    /**
     * 修改告警等级过滤器
     * @param id id
     * @param filterName s
     * @param filterId id
     * @param enable 1/0
     * @param avatarFile file
     * @return bool
     * @throws IOException io
     */
    @PutMapping("/update")
    public boolean updateAlarmTypeFilter(@RequestParam("id") Integer id, @RequestParam("filterName") String filterName, @RequestParam("filterId") String filterId,
                                         @RequestParam("enable") Short enable, @RequestParam("avatarFile") MultipartFile avatarFile) throws IOException {
        return alarmTypeFilterService.update(id, filterName, filterId, enable, avatarFile);
    }
    
    /**
     * 启用告警等级过滤器
     * @param id     告警等级过滤器ID
     * @param enable e
     * @return 是否成功
     */
    @PutMapping("/enable")
    public boolean enableAlarmTypeFilter(@RequestParam("id") Integer id, @RequestParam("enable") Short enable) {
        return alarmTypeFilterService.updateStatus(id, enable);
    }
    
    /**
     * 调整顺序
     * @param sortOrder s
     * @return u
     */
    @PutMapping("/order")
    public boolean updateOrder(@RequestBody Map<Integer, Integer> sortOrder) {
        return alarmTypeFilterService.updateOrder(sortOrder);
    }
    
    /**
     * 查询高等类型过滤器的重大告警数量
     * @param startTime 开始
     * @param endTime   结束
     * @return list
     */
    @GetMapping("/majorAlarmCount")
    public List<AlarmTypeFilterCountVo> alarmTypeFilterCountVo(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        return alarmTypeFilterService.getMajorAlarmCount(startTime, endTime);
    }
    
}
