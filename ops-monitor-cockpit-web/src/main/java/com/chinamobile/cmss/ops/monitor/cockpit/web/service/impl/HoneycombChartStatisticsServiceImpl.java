/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ParamInvalidException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ServerException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ThirdPartyAPIException;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto.FaultSceneListQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.FaultScenePageListInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.service.AlarmService;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto.CmdbBaseArgs;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto.SearchInstDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto.SearchObjectAttributeDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.CmdbResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.SearchInstInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.SearchObjectAttributeInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.service.CmdbService;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.dto.DataEngineQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.DataEngineResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.service.DataEngineService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.HoneycombChartListQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.HoneycombChartStatisticsDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.HoneycombChartStatisticsService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.BkOperatorTypeVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.BkRegionVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.ChangeListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.FaultListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.HoneycombChartOverviewVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Describe
 */
@Slf4j
@Service
public class HoneycombChartStatisticsServiceImpl implements HoneycombChartStatisticsService {
    
    private static final int ONE_DAY_SECONDS = 86400;
    
    private static final int DEFAULT_LIMIT = 10000;
    
    private static final String GLOBAL_REGION_CODE = "CIDC_ALL";
    
    private static final String OVERVIEW_NORMAL_TYPE = "5";
    
    @Value("${data.queryengine.table.monitorDeltaInfoTable:3_ods_ccops_cmcloud_monitor_delta_info}")
    private String monitorDeltaInfoTable;
    
    @Value("${data.queryengine.table.monitorWorkorderFocPoolTable:3_ads_ccops_monitor_workorder_foc_pool}")
    private String monitorWorkorderFocPoolTable;
    
    @Value("${data.queryengine.table.workorderFaultRtTable:3_ods_ccops_work_order_fault_rt}")
    private String workorderFaultRtTable;
    
    @Value("${data.queryengine.table.workorderChangeRtFocTable:3_ods_ccops_work_order_change_rt_foc}")
    private String workorderChangeRtFocTable;
    
    @Resource
    private CmdbService cmdbService;
    
    @Resource
    private AlarmService alarmService;
    
    @Autowired
    private DataEngineService dataEngineService;
    
    /**
     * 从cmdb根据资源池类型查询资源池
     * @param operationType 资源池类型
     * @return 资源池列表
     */
    @Override
    public List<BkRegionVO> getRegionFromCmdb(String operationType) {
        
        // 资源池类型作为查询条件
        Map<String, List<SearchInstDTO.BaseParam>> condition = null;
        if (StrUtil.isNotBlank(operationType)) {
            List<SearchInstDTO.BaseParam> searchInstDTOBaseParamList = new ArrayList<>();
            SearchInstDTO.BaseParam baseParam = SearchInstDTO.BaseParam.builder()
                    .field("operation_type")
                    .operator("$eq")
                    .value(operationType)
                    .build();
            searchInstDTOBaseParamList.add(baseParam);
            condition = new HashMap<>();
            condition.put("resource_pool", searchInstDTOBaseParamList);
        }
        
        Map<String, List<String>> resourcePool = new HashMap<>();
        resourcePool.put("resource_pool", Arrays.asList("bk_inst_id", "code", "bk_inst_name", "province", "operation_type"));
        
        SearchInstDTO searchInstDTO = SearchInstDTO.builder()
                .bkObjId("resource_pool")
                .bkSupplierAccount("0")
                .fields(resourcePool)
                .condition(condition)
                .page(new CmdbBaseArgs.Page(0, 0, "bk_inst_id"))
                .build();
        
        List<BkRegionVO> regionInfoVOList = new ArrayList<>();
        try {
            CmdbResponse<SearchInstInfos> getRegionResponse = cmdbService.searchInst(searchInstDTO);
            
            if (ObjectUtil.isNotEmpty(getRegionResponse.getData().getInfo())) {
                
                for (SearchInstInfos.SearchInstInfo bkRegionAzInfoVO : getRegionResponse.getData().getInfo()) {
                    regionInfoVOList.add(BkRegionVO.builder()
                            .regionName(bkRegionAzInfoVO.getBkInstName())
                            .regionCode(bkRegionAzInfoVO.getCode())
                            .province(bkRegionAzInfoVO.getProvince())
                            .operationType(bkRegionAzInfoVO.getOperationType())
                            .build());
                }
                
            }
            
        } catch (Exception e) {
            log.error("查询BK资源池信息失败", e);
            throw new ServerException("查询资源池信息失败");
        }
        
        return regionInfoVOList;
    }
    
    /**
     * 查询类型为空-查全量：
     * 先查cmdb
     * 再查数据治理，有全局资源池，则所有资源池变更+全局资源池数，返回cmdb全量
     * @param dto dto
     * @return 蜂窝图
     */
    HoneycombChartOverviewVO handleEmptyOverviewType(HoneycombChartStatisticsDTO dto) {
        List<BkRegionVO> getRegionFromCmdb = getRegionFromCmdb(dto.getOperationType());
        // 获取数据治理统计数据
        List<HoneycombChartOverviewVO.RegionOverview> dataEngineResponse = getDataFromDataEngine(dto);
        
        // 获取全局资源池数量
        Integer allRegionCount = 0;
        if (CollectionUtil.isNotEmpty(dataEngineResponse)) {
            allRegionCount = extractAllRegionCount(dataEngineResponse);
        }
        // 初始化
        Map<String, HoneycombChartOverviewVO.RegionOverview> regionMap = initCmdbRegion(getRegionFromCmdb, allRegionCount);
        
        // 用数据治理统计结果覆盖原有数据（除 inChangeCount 保留加和）
        mergeRegionStats(regionMap, dataEngineResponse);
        
        // 返回结果
        HoneycombChartOverviewVO vo = buildOverviewVO(regionMap.entrySet().size(), new ArrayList<>(regionMap.values()), dto.getPageSize(), dto.getPageNum());
        
        return vo;
    }
    
    /**
     * 查询类型不为空且查正常：
     * 先查数据治理全量，如果有全局资源池，直接返回空列表
     * 如果没全局资源池，从cmdb全量里刨除数据治理
     * @param dto dto
     * @return 蜂窝图
     */
    HoneycombChartOverviewVO handleNormalOverviewType(HoneycombChartStatisticsDTO dto) {
        
        // 获取数据治理统计数据
        List<HoneycombChartOverviewVO.RegionOverview> dataEngineResponse = getDataFromDataEngine(dto);
        
        // 获取全局资源池数量
        Integer allRegionCount = 0;
        if (CollectionUtil.isNotEmpty(dataEngineResponse)) {
            allRegionCount = extractAllRegionCount(dataEngineResponse);
        }
        if (0 != allRegionCount) {
            HoneycombChartOverviewVO vo = new HoneycombChartOverviewVO();
            vo.setTotal(0L);
            vo.setRegionOverviewList(new ArrayList<>());
            return vo;
        } else {
            // cmdbList 是从 CMDB 获取的资源池列表
            List<BkRegionVO> cmdbList = getRegionFromCmdb(dto.getOperationType());
            
            // 先提取数据治理中出现的 regionCode
            Set<String> usedRegionCodes = dataEngineResponse.stream()
                    .map(HoneycombChartOverviewVO.RegionOverview::getRegionCode)
                    .collect(Collectors.toSet());
            
            // 从 cmdbList 中排除这些 regionCode 对应的资源池
            List<BkRegionVO> unusedRegions = cmdbList.stream()
                    .filter(region -> !usedRegionCodes.contains(region.getRegionCode()))
                    .collect(Collectors.toList());
            
            Map<String, HoneycombChartOverviewVO.RegionOverview> regionMap = initCmdbRegion(unusedRegions, allRegionCount);
            // 返回结果
            HoneycombChartOverviewVO vo = buildOverviewVO(regionMap.entrySet().size(), new ArrayList<>(regionMap.values()), dto.getPageSize(), dto.getPageNum());
            
            return vo;
        }
    }
    
    /**
     * 查询类型不为空且不含有正常
     * 先按条件查数据治理
     * 无全局资源池，则直接查数据治理返回结果
     * 有全局资源池，则所有资源池变更+全局资源池数，返回cmdb全量
     * @param dto dto
     * @return 蜂窝图
     */
    HoneycombChartOverviewVO handleNonNormalOverviewType(HoneycombChartStatisticsDTO dto) {
        // 获取数据治理统计数据
        List<HoneycombChartOverviewVO.RegionOverview> dataEngineResponse = getDataFromDataEngine(dto);
        
        // 获取全局资源池数量
        Integer allRegionCount = 0;
        if (CollectionUtil.isNotEmpty(dataEngineResponse)) {
            allRegionCount = extractAllRegionCount(dataEngineResponse);
        }
        if (0 != allRegionCount) {
            // 获取cmdb资源池
            List<BkRegionVO> getRegionFromCmdb = getRegionFromCmdb(dto.getOperationType());
            // 初始化
            Map<String, HoneycombChartOverviewVO.RegionOverview> regionMap = initCmdbRegion(getRegionFromCmdb, allRegionCount);
            
            // 用数据治理统计结果覆盖cmdb原有数据（除 inChangeCount 保留加和）
            mergeRegionStats(regionMap, dataEngineResponse);
            
            HoneycombChartOverviewVO vo = buildOverviewVO(regionMap.entrySet().size(), new ArrayList<>(regionMap.values()), dto.getPageSize(), dto.getPageNum());
            return vo;
        } else {
            // 返回结果
            HoneycombChartOverviewVO vo = buildOverviewVO(dataEngineResponse.stream().count(), dataEngineResponse, dto.getPageSize(), dto.getPageNum());
            for (HoneycombChartOverviewVO.RegionOverview regionOverview : vo.getRegionOverviewList()) {
                int faultCount = Optional.ofNullable(regionOverview.getFaultCount()).orElse(0);
                int majorAlarmCount = Optional.ofNullable(regionOverview.getMajorAlarmCount()).orElse(0);
                int importantAlarmCount = Optional.ofNullable(regionOverview.getImportantAlarmCount()).orElse(0);
                int securityAttacksAlarmCount = Optional.ofNullable(regionOverview.getSecurityAttacksAlarmCount()).orElse(0);
                int inChangeCount = Optional.ofNullable(regionOverview.getInChangeCount()).orElse(0);
                int abnormalTypeCount = 0;
                List<String> abnormalTypeCodes = List.of(OVERVIEW_NORMAL_TYPE);
                
                // 计算异常类型，以下顺序不能改！
                if (inChangeCount > 0) {
                    abnormalTypeCodes = Arrays.asList("3");
                    abnormalTypeCount++;
                }
                if (majorAlarmCount > 0 || importantAlarmCount > 0 || securityAttacksAlarmCount > 0) {
                    abnormalTypeCodes = Arrays.asList("0", "2", "4");
                    abnormalTypeCount++;
                }
                
                if (faultCount > 0) {
                    abnormalTypeCodes = List.of("1");
                    abnormalTypeCount++;
                }
                regionOverview.setAbnormalTypeCount(abnormalTypeCount);
                regionOverview.setAbnormalTypeCodes(abnormalTypeCodes);
            }
            
            return vo;
        }
    }
    
    /**
     * 蜂窝图总览
     * @param dto 资源池类型、总览类型
     * @return 蜂窝图
     */
    @Override
    public HoneycombChartOverviewVO getHoneycombChartOverview(HoneycombChartStatisticsDTO dto) {
        
        // 查询类型为空-查全量：
        // 先查cmdb
        // 再查数据治理，有全局资源池，则所有资源池变更+全局资源池数，返回cmdb全量
        if (CollectionUtil.isEmpty(dto.getOverviewType())) {
            return handleEmptyOverviewType(dto);
        }
        
        // 查询类型不为空且查正常：
        // 先查数据治理全量，如果有全局资源池，直接返回空列表
        // 如果没全局资源池，从cmdb全量里刨除数据治理
        if (dto.getOverviewType().contains(OVERVIEW_NORMAL_TYPE)) {
            return handleNormalOverviewType(dto);
        }
        // 查询类型不为空且不含有正常
        // 先按条件查数据治理
        // 无全局资源池，则直接查数据治理返回结果
        // 有全局资源池，则所有资源池变更+全局资源池数，返回cmdb全量
        return handleNonNormalOverviewType(dto);
    }
    
    /**
     * 初始化cmdb资源池
     * @param getRegionFromCmdb cmdb资源池
     * @param allRegionCount    全局资源池数量
     * @return 初始化
     */
    public Map<String, HoneycombChartOverviewVO.RegionOverview> initCmdbRegion(List<BkRegionVO> getRegionFromCmdb, Integer allRegionCount) {
        Map<String, HoneycombChartOverviewVO.RegionOverview> regionMap = new HashMap<>();
        for (BkRegionVO bkRegion : getRegionFromCmdb) {
            HoneycombChartOverviewVO.RegionOverview overview = new HoneycombChartOverviewVO.RegionOverview();
            overview.setRegionName(bkRegion.getRegionName());
            overview.setRegionCode(bkRegion.getRegionCode());
            overview.setFaultCount(0);
            overview.setMajorAlarmCount(0);
            overview.setImportantAlarmCount(0);
            overview.setSecurityAttacksAlarmCount(0);
            overview.setInChangeCount(allRegionCount);
            overview.setAllRegionFlag(allRegionCount);
            regionMap.put(bkRegion.getRegionCode(), overview);
        }
        return regionMap;
    }
    
    /**
     * 提取统计值覆盖逻辑
     * @param regionMap          资源池map
     * @param dataEngineResponse 数据治理
     */
    
    private void mergeRegionStats(Map<String, HoneycombChartOverviewVO.RegionOverview> regionMap,
                                  List<HoneycombChartOverviewVO.RegionOverview> dataEngineResponse) {
        
        // 将 dataEngineResponse 变为 map 结构，便于按 regionCode 快速获取
        Map<String, HoneycombChartOverviewVO.RegionOverview> responseMap = dataEngineResponse.stream()
                .collect(Collectors.toMap(HoneycombChartOverviewVO.RegionOverview::getRegionCode, Function.identity(), (a, b) -> a));
        
        for (Map.Entry<String, HoneycombChartOverviewVO.RegionOverview> entry : regionMap.entrySet()) {
            String regionCode = entry.getKey();
            HoneycombChartOverviewVO.RegionOverview targetRegion = entry.getValue();
            HoneycombChartOverviewVO.RegionOverview statRegion = responseMap.get(regionCode);
            
            // 先初始化为 0（即便 statRegion == null）
            int faultCount = 0;
            int majorAlarmCount = 0;
            int importantAlarmCount = 0;
            int securityAttacksAlarmCount = 0;
            int inChangeCount = 0;
            
            if (statRegion != null) {
                faultCount = Optional.ofNullable(statRegion.getFaultCount()).orElse(0);
                majorAlarmCount = Optional.ofNullable(statRegion.getMajorAlarmCount()).orElse(0);
                importantAlarmCount = Optional.ofNullable(statRegion.getImportantAlarmCount()).orElse(0);
                securityAttacksAlarmCount = Optional.ofNullable(statRegion.getSecurityAttacksAlarmCount()).orElse(0);
                inChangeCount = Optional.ofNullable(statRegion.getInChangeCount()).orElse(0);
            }
            
            targetRegion.setFaultCount(faultCount);
            targetRegion.setMajorAlarmCount(majorAlarmCount);
            targetRegion.setImportantAlarmCount(importantAlarmCount);
            targetRegion.setSecurityAttacksAlarmCount(securityAttacksAlarmCount);
            targetRegion.setInChangeCount(Optional.ofNullable(targetRegion.getInChangeCount()).orElse(0) + inChangeCount);
            
            int abnormalTypeCount = 0;
            List<String> abnormalTypeCodes = List.of(OVERVIEW_NORMAL_TYPE);
            
            // 计算异常类型，以下顺序不能改！
            if (inChangeCount > 0) {
                abnormalTypeCodes = Arrays.asList("3");
                abnormalTypeCount++;
            }
            if (majorAlarmCount > 0 || importantAlarmCount > 0 || securityAttacksAlarmCount > 0) {
                abnormalTypeCodes = Arrays.asList("0", "2", "4");
                abnormalTypeCount++;
            }
            
            if (faultCount > 0) {
                abnormalTypeCodes = List.of("1");
                abnormalTypeCount++;
            }
            
            targetRegion.setAbnormalTypeCodes(abnormalTypeCodes);
            targetRegion.setAbnormalTypeCount(abnormalTypeCount);
        }
    }
    
    /**
     * 分页和排序：排序规则如下（按先后顺序）：
     * 有故障
     * 有重大重要告警
     * 有安全攻击
     * 有正在进行的变更
     * 无以上情况（字母降序排列）
     * @param regionOverviewList re
     * @param pageNum            pagenum
     * @param pageSize           页码
     * @return 排序
     */
    public List<HoneycombChartOverviewVO.RegionOverview> sortAndPagePart(List<HoneycombChartOverviewVO.RegionOverview> regionOverviewList, Integer pageSize, Integer pageNum) {
        
        regionOverviewList.sort(Comparator
                // 1. 有故障优先
                .comparing((HoneycombChartOverviewVO.RegionOverview r) -> r.getFaultCount() > 0 ? 0 : 1)
                // 2. 有重大或重要告警
                .thenComparing(r -> (r.getMajorAlarmCount() > 0 || r.getImportantAlarmCount() > 0) ? 0 : 1)
                // 3. 有安全攻击
                .thenComparing(r -> r.getSecurityAttacksAlarmCount() > 0 ? 0 : 1)
                // 4. 有正在变更
                .thenComparing(r -> r.getInChangeCount() > 0 ? 0 : 1)
                // 5. 资源池名称降序（Z 到 A）
                .thenComparing(HoneycombChartOverviewVO.RegionOverview::getRegionName, Comparator.reverseOrder()));
        
        // 分页（pageNum 从 1 开始）
        
        int fromIndex = Math.min((pageNum - 1) * pageSize, regionOverviewList.size());
        int toIndex = Math.min(fromIndex + pageSize, regionOverviewList.size());
        List<HoneycombChartOverviewVO.RegionOverview> pageList = regionOverviewList.subList(fromIndex, toIndex);
        return pageList;
    }
    
    /**
     * 提取全局资源池计数逻辑
     * @param dataEngineResponse 数据治理
     * @return 技术
     */
    private int extractAllRegionCount(List<HoneycombChartOverviewVO.RegionOverview> dataEngineResponse) {
        Integer allRegionCount = 0;
        if (CollectionUtil.isNotEmpty(dataEngineResponse)) {
            for (HoneycombChartOverviewVO.RegionOverview region : dataEngineResponse) {
                if (GLOBAL_REGION_CODE.equals(region.getRegionCode())) {
                    allRegionCount = region.getAllRegionFlag();
                }
            }
        }
        return allRegionCount;
    }
    
    /**
     * 从数据治理获取数据
     * @param dto dto
     * @return 数据治理
     * @throws ThirdPartyAPIException 调用数据引擎异常时抛出
     */
    public List<HoneycombChartOverviewVO.RegionOverview> getDataFromDataEngine(HoneycombChartStatisticsDTO dto) {
        // 获取统计信息
        Instant now = Instant.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.systemDefault());
        
        String endTime = formatter.format(now);
        String startTime = formatter.format(now.minusSeconds(ONE_DAY_SECONDS));
        
        StringBuilder queryBaseBuilder = new StringBuilder();
        queryBaseBuilder.append("SELECT region_name as regionName, region_code as regionCode,")
                .append(" SUM(CASE WHEN ticket_type = '1' AND state = 0 THEN 1 ELSE 0 END) AS faultCount,")
                .append(" SUM(CASE WHEN ticket_type = '0' AND state = 0 THEN 1 ELSE 0 END) AS majorAlarmCount,")
                .append(" SUM(CASE WHEN ticket_type = '4' AND state = 0 THEN 1 ELSE 0 END) AS importantAlarmCount,")
                .append(" SUM(CASE WHEN ticket_type = '2' AND state = 0 THEN 1 ELSE 0 END) AS securityAttacksAlarmCount,")
                .append(" SUM(CASE WHEN region_code != 'CIDC_ALL' AND ticket_type = '3' and state=0 THEN 1 ELSE 0 END) AS inChangeCount,")
                .append(" SUM(CASE WHEN region_code = 'CIDC_ALL' AND ticket_type='3' and state=0 THEN 1 ELSE 0 END) AS allRegionFlag")
                .append(" FROM ").append(monitorWorkorderFocPoolTable)
                .append(" WHERE region_code IS NOT NULL")
                .append(" and create_time between '").append(startTime).append("'")
                .append(" and '").append(endTime).append("'")
                .append(" and state = 0");
        
        // 添加过滤条件
        if (StrUtil.isNotBlank(dto.getOperationType())) {
            queryBaseBuilder.append(" AND pool_type = '").append(dto.getOperationType()).append("'");
        }
        
        queryBaseBuilder.append(" GROUP BY region_name, region_code");
        
        queryBaseBuilder.append(" LIMIT ").append(DEFAULT_LIMIT);
        
        String finalQuerySQL = queryBaseBuilder.toString();
        log.info("查询蜂窝图总览 SQL:\n{}", finalQuerySQL);
        
        DataEngineQueryDTO dataQueryDTO = DataEngineQueryDTO.builder()
                .sql(finalQuerySQL)
                .preferStorage("doris")
                .build();
        
        List<HoneycombChartOverviewVO.RegionOverview> result;
        try {
            DataEngineResponse<?> dataResp = dataEngineService.requestDataEngine(dataQueryDTO);
            result = convertToList(dataResp.getData().getList(), HoneycombChartOverviewVO.RegionOverview.class);
            
            if (CollectionUtil.isNotEmpty(dto.getOverviewType()) && !dto.getOverviewType().contains(OVERVIEW_NORMAL_TYPE)) {
                Set<String> types = new HashSet<>(dto.getOverviewType());
                
                result = result.stream()
                        .filter(r -> matchesOverviewType(r, types))
                        .collect(Collectors.toList());
            }
            
        } catch (Exception e) {
            log.error("查询蜂窝图总览异常，SQL:{}", finalQuerySQL, e);
            throw new ThirdPartyAPIException("查询蜂窝图总览异常");
        }
        return result;
    }
    
    /**
     * 筛选需要保留的数据
     * @param date  数据治理的返回
     * @param types 总览类型
     * @return 数据
     */
    private boolean matchesOverviewType(HoneycombChartOverviewVO.RegionOverview date, Set<String> types) {
        for (String t : types) {
            switch (t) {
                case "0":
                    if (date.getMajorAlarmCount() > 0) {
                        return true;
                    }
                    break;
                case "1":
                    if (date.getFaultCount() > 0) {
                        return true;
                    }
                    break;
                case "2":
                    if (date.getSecurityAttacksAlarmCount() > 0) {
                        return true;
                    }
                    break;
                case "3":
                    if (date.getInChangeCount() > 0 || date.getAllRegionFlag() > 0) {
                        return true;
                    }
                    break;
                case "4":
                    if (date.getImportantAlarmCount() > 0) {
                        return true;
                    }
                    break;
                default:
                    log.warn("未知的 overviewType 类型：{}", t);
                    break;
            }
        }
        return false;
    }
    
    /**
     * 转换
     * @param rawList     数据治理返回
     * @param targetClass 实际实体
     * @param <T>         泛型
     * @return list
     */
    public <T> List<T> convertToList(List<?> rawList, Class<T> targetClass) {
        return rawList.stream()
                .map(item -> JSONUtil.toBean(JSONUtil.toJsonStr(item), targetClass))
                .collect(Collectors.toList());
    }
    
    /**
     * 封装返回vo
     * @param total    总数
     * @param list     资源池数据
     * @param pageSize 页码
     * @param pageNum  数量
     * @return 返回前端
     */
    private HoneycombChartOverviewVO buildOverviewVO(long total, List<HoneycombChartOverviewVO.RegionOverview> list, Integer pageSize, Integer pageNum) {
        HoneycombChartOverviewVO vo = new HoneycombChartOverviewVO();
        vo.setTotal(total);
        vo.setRegionOverviewList(sortAndPagePart(list, pageSize, pageNum));
        return vo;
    }
    
    /**
     * 根据资源池查询告警
     * @param dto 资源池编码
     * @return 告警列表
     */
    @Override
    public AlarmListVO getAlarmByRegion(HoneycombChartListQueryDTO dto) {
        if (!StrUtil.isNotBlank(dto.getRegionCode())) {
            throw new ParamInvalidException("资源池编码不能为空");
        }
        Instant now = Instant.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.systemDefault());
        
        dto.setEndDate(formatter.format(now));
        dto.setStartDate(formatter.format(now.minusSeconds(ONE_DAY_SECONDS)));
        
        final String[] selectColumns = {
                "alarm_id", "alarm_title", "ack_status_label",
                "last_alarm_time", "alarm_level_label", "dispatch_status_label"
        };
        
        String baseCondition = String.join(" AND ",
                "(sub_alarm_ids IS NOT NULL OR is_child = 'false' OR is_child IS NULL)",
                "filter_status <> '1'",
                "notify_type = 0",
                "project_flag_id = 0",
                "region_code = '" + dto.getRegionCode() + "'",
                "first_alarm_time BETWEEN '" + dto.getStartDate() + "' AND '" + dto.getEndDate() + "'",
                "alarm_level_label in ('重大','重要')");
        
        // 构建分页查询SQL
        String dataSQL = String.format(
                "SELECT %s FROM %s WHERE %s order by alarm_level_label desc LIMIT %d OFFSET %d",
                String.join(",", selectColumns),
                monitorDeltaInfoTable,
                baseCondition,
                dto.getPageSize(),
                (dto.getPageNum() - 1) * dto.getPageSize());
        
        // 构建总量查询SQL（最多查 10000 条）
        String countSQL = String.format(
                "SELECT alarm_id as total FROM %s WHERE %s LIMIT 10000",
                monitorDeltaInfoTable,
                baseCondition);
        
        log.info("蜂窝图资源池告警列表 SQL：{}", dataSQL);
        
        AlarmListVO result = new AlarmListVO();
        try {
            DataEngineQueryDTO countDTO = DataEngineQueryDTO.builder()
                    .sql(countSQL)
                    .preferStorage("doris")
                    .build();
            
            DataEngineQueryDTO dataQueryDTO = DataEngineQueryDTO.builder()
                    .sql(dataSQL)
                    .preferStorage("doris")
                    .build();
            
            DataEngineResponse<Long> totalResp = dataEngineService.requestDataEngine(countDTO);
            DataEngineResponse<AlarmListVO.AlarmEntity> dataResp = dataEngineService.requestDataEngine(dataQueryDTO);
            
            result.setTotal(totalResp.getData().getTotalRecords());
            result.setAlarmEntity(dataResp.getData().getList());
        } catch (Exception e) {
            log.error("查询蜂窝图告警列表失败", e);
            throw new ThirdPartyAPIException("查询告警列表异常");
        }
        
        return result;
    }
    
    /**
     * 获取资源池类型枚举
     * @return 源池类型枚举列表
     */
    @Override
    public List<BkOperatorTypeVO> getOperationTypeFromCmdb() {
        SearchObjectAttributeDTO searchObjectAttributeDTO = SearchObjectAttributeDTO.builder()
                .bkObjId("resource_pool")
                .bkPropertyId("operation_type")
                .bkSupplierAccount("0")
                .build();
        
        List<BkOperatorTypeVO> bkOperatorTypeVOList = new ArrayList<>();
        try {
            CmdbResponse<List<SearchObjectAttributeInfos>> getOperationTypeResponse = cmdbService.searchObjectAttribute(searchObjectAttributeDTO);
            
            if (ObjectUtil.isNotEmpty(getOperationTypeResponse.getData())) {
                
                List<SearchObjectAttributeInfos.TypeEnums> option = getOperationTypeResponse.getData().get(0).getOption();
                for (SearchObjectAttributeInfos.TypeEnums typeEnums : option) {
                    bkOperatorTypeVOList.add(BkOperatorTypeVO.builder()
                            .operatorTypeId(typeEnums.getId())
                            .operatorTypeName(typeEnums.getName())
                            .build());
                }
                
            }
            
        } catch (Exception e) {
            log.error("查询BK资源池类型枚举失败", e);
            throw new ServerException("查询资源池类型枚举失败");
        }
        
        return bkOperatorTypeVOList;
    }
    
    /**
     * 查询故障列表
     * @param dto dto
     * @return 列表
     */
    @Override
    public FaultListVO getFaultByRegion(HoneycombChartListQueryDTO dto) {
        if (!StrUtil.isNotBlank(dto.getRegionName())) {
            throw new ParamInvalidException("资源池名称不能为空");
        }
        Instant now = Instant.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.systemDefault());
        
        dto.setEndDate(formatter.format(now));
        dto.setStartDate(formatter.format(now.minusSeconds(ONE_DAY_SECONDS)));
        
        // 公共 SQL 片段
        String baseSelectSQL = String.join(" ",
                "SELECT cell1, incidentTitle, state,",
                "fault_level, fault_type1, fault_type2, fault_type3, create_time, " +
                        "CONCAT(ROUND((UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(create_time)) / 3600, 1), ' 小时') AS handleDuration " +
                        "FROM " + workorderFaultRtTable,
                "WHERE state!='已关单' and resource_pool = '" + dto.getRegionName() + "'",
                "AND create_time BETWEEN '" + dto.getStartDate() + "' AND '" + dto.getEndDate() + "'");
        
        // 数据分页 SQL
        String dataSQL = baseSelectSQL + " LIMIT " + dto.getPageSize() + " OFFSET " + ((dto.getPageNum() - 1) * dto.getPageSize());
        log.info("查询蜂窝图资源池故障列表SQL：{}", dataSQL);
        
        String countSQL = "SELECT base_id  FROM " + workorderFaultRtTable +
                " WHERE state!='已关单' and resource_pool = '" + dto.getRegionName() + "'" +
                " AND create_time BETWEEN '" + dto.getStartDate() + "' AND '" + dto.getEndDate() + "'";
        
        // 构建查询请求
        DataEngineQueryDTO countDTO = DataEngineQueryDTO.builder()
                .sql(countSQL)
                .preferStorage("doris")
                .build();
        
        DataEngineQueryDTO dataDTO = DataEngineQueryDTO.builder()
                .sql(dataSQL)
                .preferStorage("doris")
                .build();
        
        FaultListVO result = new FaultListVO();
        try {
            DataEngineResponse<Long> totalResp = dataEngineService.requestDataEngine(countDTO);
            DataEngineResponse<FaultListVO.FaultEntity> dataResp = dataEngineService.requestDataEngine(dataDTO);
            
            List<FaultListVO.FaultEntity> faultEntities = convertToList(dataResp.getData().getList(), FaultListVO.FaultEntity.class);
            
            List<String> workOrderNos = queryFaultSceneList();
            for (FaultListVO.FaultEntity faultEntity : faultEntities) {
                
                if (workOrderNos.contains(faultEntity.getCell1())) {
                    faultEntity.setIsScheduled(Boolean.TRUE);
                } else {
                    faultEntity.setIsScheduled(Boolean.FALSE);
                }
            }
            result.setTotal(totalResp.getData().getTotalRecords());
            result.setFaultEntity(faultEntities);
        } catch (Exception e) {
            log.error("查询故障列表异常", e);
            throw new ThirdPartyAPIException("查询故障异常");
        }
        
        return result;
    }
    
    /**
     * 获取建立驾驶舱的工单号列表
     * @return list
     * @throws ServerException ServerException
     */
    public List<String> queryFaultSceneList() {
        FaultSceneListQueryDTO faultSceneListQueryDTO = FaultSceneListQueryDTO.builder()
                .faultSceneNo("")
                .faultSceneName("")
                .createStartTime("")
                .faultSceneStatus(1)
                .page(1)
                .size(500)
                .build();
        AlarmResponse<FaultScenePageListInfos> queryFaultSceneList = alarmService.queryFaultSceneList(faultSceneListQueryDTO);
        if (!"20000".equals(queryFaultSceneList.getMeta().getCode())) {
            throw new ServerException("调度接口请求失败");
        }
        List<FaultScenePageListInfos.FaultScenePlainVO> list = queryFaultSceneList.getData().getList();
        List<String> relatedOrderNos = list.stream()
                .map(FaultScenePageListInfos.FaultScenePlainVO::getRelatedOrderNo)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        
        return relatedOrderNos;
    }
    
    /**
     * 安全攻击列表
     * @param dto 资源池编码
     * @return 列表
     */
    @Override
    public AlarmListVO getSecurityAttacksAlarmByRegion(HoneycombChartListQueryDTO dto) {
        if (StrUtil.isBlank(dto.getRegionCode())) {
            throw new ParamInvalidException("资源池编码不能为空");
        }
        
        final String keyword = "安全攻击";
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.systemDefault());
        
        Instant now = Instant.now();
        dto.setEndDate(formatter.format(now));
        dto.setStartDate(formatter.format(now.minusSeconds(ONE_DAY_SECONDS)));
        
        // 基础 where 条件
        String baseWhere = String.join(" ",
                "FROM", monitorDeltaInfoTable,
                "WHERE (sub_alarm_ids IS NOT NULL OR is_child = 'false' OR is_child IS NULL)",
                "AND filter_status <> '1'",
                "AND notify_type = 0",
                "AND project_flag_id = 0",
                "AND region_code = '" + dto.getRegionCode() + "'",
                "AND alarm_title LIKE '%" + keyword + "%'",
                "AND first_alarm_time BETWEEN '" + dto.getStartDate() + "' AND '" + dto.getEndDate() + "'");
        
        // 数据查询 SQL
        String dataSQL = String.join(" ",
                "SELECT alarm_id, alarm_title, ack_status_label, last_alarm_time,",
                "alarm_level_label, dispatch_status_label",
                baseWhere,
                "LIMIT " + dto.getPageSize(),
                "OFFSET " + ((dto.getPageNum() - 1) * dto.getPageSize()));
        
        // 总数 SQL（推荐用 COUNT 替代 LIMIT）
        String countSQL = String.join(" ",
                "SELECT alarm_id",
                baseWhere);
        
        log.info("查询蜂窝图资源池安全攻击告警列表SQL：{}", dataSQL);
        
        DataEngineQueryDTO countDTO = DataEngineQueryDTO.builder()
                .sql(countSQL)
                .preferStorage("doris")
                .build();
        
        DataEngineQueryDTO dataDTO = DataEngineQueryDTO.builder()
                .sql(dataSQL)
                .preferStorage("doris")
                .build();
        
        AlarmListVO result = new AlarmListVO();
        try {
            DataEngineResponse<Long> totalResp = dataEngineService.requestDataEngine(countDTO);
            DataEngineResponse<AlarmListVO.AlarmEntity> dataResp = dataEngineService.requestDataEngine(dataDTO);
            
            result.setTotal(totalResp.getData().getTotalRecords());
            result.setAlarmEntity(dataResp.getData().getList());
        } catch (Exception e) {
            log.error("查询告警安全攻击列表异常", e);
            throw new ThirdPartyAPIException("查询告警安全攻击列表异常");
        }
        
        return result;
    }
    
    /**
     * 变更列表
     * @param dto 资源池编码
     * @return 列表
     */
    @Override
    public ChangeListVO getChangeByRegion(HoneycombChartListQueryDTO dto) {
        if (StrUtil.isBlank(dto.getRegionName())) {
            throw new ParamInvalidException("资源池名称不能为空");
        }
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.systemDefault());
        
        Instant now = Instant.now();
        dto.setEndDate(formatter.format(now));
        dto.setStartDate(formatter.format(now.minusSeconds(ONE_DAY_SECONDS)));
        
        // 拼接基础 WHERE 条件
        String baseWhere = String.join(" ",
                "FROM", workorderChangeRtFocTable,
                "WHERE changeState = '实施变更' and (resourcePool = '全局资源'",
                "OR CONCAT(',', REPLACE(resourcePool, ' ', ''), ',') LIKE '%," + dto.getRegionName() + ",%')",
                "AND createTime BETWEEN '" + dto.getStartDate() + "' AND '" + dto.getEndDate() + "'");
        
        // 数据 SQL
        String dataSQL = String.join(" ",
                "SELECT changeId, cell9, changeState, creatorDept,",
                "riskLevel, businessSystem, publishType, Implementer as implementer",
                baseWhere,
                "LIMIT " + dto.getPageSize(),
                "OFFSET " + ((dto.getPageNum() - 1) * dto.getPageSize()));
        
        // 计数 SQL（推荐使用 COUNT 替代 LIMIT）
        String countSQL = String.join(" ",
                "SELECT changeId",
                baseWhere);
        
        log.info("查询蜂窝图资源池变更列表SQL：{}", dataSQL);
        
        DataEngineQueryDTO countDTO = DataEngineQueryDTO.builder()
                .sql(countSQL)
                .preferStorage("doris")
                .build();
        
        DataEngineQueryDTO dataDTO = DataEngineQueryDTO.builder()
                .sql(dataSQL)
                .preferStorage("doris")
                .build();
        
        ChangeListVO result = new ChangeListVO();
        try {
            DataEngineResponse<Long> totalResp = dataEngineService.requestDataEngine(countDTO);
            DataEngineResponse<ChangeListVO.ChangeEntity> dataResp = dataEngineService.requestDataEngine(dataDTO);
            
            List<ChangeListVO.ChangeEntity> changeEntities = convertToList(dataResp.getData().getList(), ChangeListVO.ChangeEntity.class);
            
            for (ChangeListVO.ChangeEntity changeEntity : changeEntities) {
                String implementer = changeEntity.getImplementer();
                if (implementer == null || implementer.trim().isEmpty() || "[]".equals(implementer.trim())) {
                    changeEntity.setImplementer("");
                }
            }
            
            result.setTotal(totalResp.getData().getTotalRecords());
            result.setChangeEntity(changeEntities);
            
        } catch (Exception e) {
            log.error("查询变更列表异常", e);
            throw new ThirdPartyAPIException("查询变更列表异常");
        }
        
        return result;
    }
    
}
