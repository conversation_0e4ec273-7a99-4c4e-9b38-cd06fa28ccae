/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.DutyAttendanceDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.DutyManagementService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.DutyAttendanceVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.MajorDutyVO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Describe
 */
@RestController
@RequestMapping("/dutyManagement")
public class DutyManagementController {
    
    @Autowired
    private DutyManagementService dutyManagementService;
    
    /**
     * 获取当天专业值班信息
     * @return 值班信息
     */
    @GetMapping("/duties/today/major")
    public List<MajorDutyVO> getTodayMajorDuty() {
        return dutyManagementService.getTodayMajorDuty();
    }
    
    /**
     * 查询通告
     * @return 通告
     */
    @GetMapping("/notice/msg")
    public String getNotice() {
        return dutyManagementService.getNotice();
    }
    
    /**
     * 保存新通告
     * @param param 通告
     */
    @PostMapping("/save/notice")
    public void saveNotice(@RequestBody Map<String, String> param) {
        String content = param.get("content");
        dutyManagementService.saveNotice(content);
    }
    
    /**
     * 查询考勤人数
     * @return 考勤
     */
    @GetMapping("/get/duty/attendance")
    public List<DutyAttendanceVO> getDutyAttendance() {
        return dutyManagementService.getDutyAttendance();
    }
    
    /**
     * 修改考勤人数
     * @param dutyAttendanceDTO dto
     */
    @PutMapping("/save/duty/attendance")
    public void saveDutyAttendance(@RequestBody @Valid DutyAttendanceDTO dutyAttendanceDTO) {
        dutyManagementService.saveDutyAttendance(dutyAttendanceDTO);
        
    }
    
}
