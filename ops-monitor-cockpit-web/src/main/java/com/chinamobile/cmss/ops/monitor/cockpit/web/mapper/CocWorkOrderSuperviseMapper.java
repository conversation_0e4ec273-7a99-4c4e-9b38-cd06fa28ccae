/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.CocWorkOrderSupervise;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 工单督办Mapper接口
 */
@Mapper
public interface CocWorkOrderSuperviseMapper extends BaseMapper<CocWorkOrderSupervise> {
    
    /**
     * 批量插入工单督办信息
     * @param cocWorkOrderSuperviseList 工单督办记录列表
     * @return int
     */
    int insertBatch(List<CocWorkOrderSupervise> cocWorkOrderSuperviseList);
    
    /**
     * 批量更新工单督办信息
     * @param cocWorkOrderSuperviseList 工单督办记录列表
     * @return int
     */
    int updateBatch(List<CocWorkOrderSupervise> cocWorkOrderSuperviseList);
}
