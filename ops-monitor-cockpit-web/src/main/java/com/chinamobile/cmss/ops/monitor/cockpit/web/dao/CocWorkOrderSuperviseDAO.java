/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.dao;

import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.CocWorkOrderSupervise;

import java.util.List;

/**
 * 工单督办DAO
 */
public interface CocWorkOrderSuperviseDAO {
    
    /**
     * 删除工单督办信息
     * @param id 工单督办id
     * @return boolean
     */
    boolean deleteByPrimaryKey(String id);
    
    /**
     * 插入工单督办信息
     * @param record 工单督办记录
     * @return boolean
     */
    boolean insert(CocWorkOrderSupervise record);
    
    /**
     * 查询工单督办信息
     * @param id 工单督办记录id
     * @return CocWorkOrderSupervise
     */
    CocWorkOrderSupervise selectByPrimaryKey(String id);
    
    /**
     * 更新工单督办信息
     * @param record 工单督办记录
     * @return boolean
     */
    boolean updateByPrimaryKey(CocWorkOrderSupervise record);
    
    /**
     * 批量插入工单督办信息
     * @param cocWorkOrderSuperviseList 工单督办记录列表
     * @return int
     */
    int insertBatch(List<CocWorkOrderSupervise> cocWorkOrderSuperviseList);
    
    /**
     * 批量更新工单督办信息
     * @param cocWorkOrderSuperviseList 工单督办记录列表
     * @return int
     */
    int updateBatch(List<CocWorkOrderSupervise> cocWorkOrderSuperviseList);
    
    /**
     * 查询全量的工单督办信息
     * @return List
     */
    List<CocWorkOrderSupervise> selectAll();
    
    /**
     * 根据工单号列表查询工单督办信息
     * @param workOrderNoList 工单号列表
     * @return List
     */
    List<CocWorkOrderSupervise> selectByWorkOrderNoList(List<String> workOrderNoList);
    
}
