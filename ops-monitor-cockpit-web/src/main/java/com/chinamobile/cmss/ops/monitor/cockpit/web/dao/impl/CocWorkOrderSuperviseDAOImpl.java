/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ServerException;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dao.CocWorkOrderSuperviseDAO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.CocWorkOrderSupervise;
import com.chinamobile.cmss.ops.monitor.cockpit.web.mapper.CocWorkOrderSuperviseMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工单督办DAOImpl
 */
@Service
@Slf4j
public class CocWorkOrderSuperviseDAOImpl extends ServiceImpl<CocWorkOrderSuperviseMapper, CocWorkOrderSupervise> implements CocWorkOrderSuperviseDAO {
    
    @Autowired
    private CocWorkOrderSuperviseMapper cocWorkOrderSuperviseMapper;
    
    @Override
    public boolean deleteByPrimaryKey(String id) {
        boolean result = false;
        try {
            result = this.removeById(id);
        } catch (final Exception e) {
            log.error("coc_worker_order_supervise数据删除失败!异常信息：{}", e.getMessage(), e);
            throw new ServerException("删除工单督办信息失败!");
        }
        return result;
    }
    
    @Override
    public boolean insert(CocWorkOrderSupervise record) {
        boolean result = false;
        try {
            result = this.save(record);
        } catch (final Exception e) {
            log.error("coc_worker_order_supervise数据插入失败!异常信息：{}", e.getMessage(), e);
            throw new ServerException("插入工单督办信息失败!");
        }
        return result;
    }
    
    @Override
    public CocWorkOrderSupervise selectByPrimaryKey(String id) {
        CocWorkOrderSupervise cocWorkOrderSupervise = null;
        try {
            cocWorkOrderSupervise = this.getById(id);
        } catch (final Exception e) {
            log.error("根据id查询coc_worker_order_supervise数据失败!异常信息：{}", e.getMessage(), e);
            throw new ServerException("根据Id查询工单督办信息失败!");
        }
        return cocWorkOrderSupervise;
    }
    
    @Override
    public boolean updateByPrimaryKey(CocWorkOrderSupervise record) {
        boolean result = false;
        try {
            result = this.updateById(record);
        } catch (final Exception e) {
            log.error("coc_worker_order_supervise数据更新失败!异常信息：{}", e.getMessage(), e);
            throw new ServerException("更新工单督办信息失败!");
        }
        return result;
    }
    
    @Override
    public int insertBatch(List<CocWorkOrderSupervise> cocWorkOrderSuperviseList) {
        int result = 0;
        try {
            result = cocWorkOrderSuperviseMapper.insertBatch(cocWorkOrderSuperviseList);
        } catch (final Exception e) {
            log.error("coc_worker_order_supervise批量保存失败!异常信息：{}", e.getMessage(), e);
            throw new ServerException("批量保存工单督办信息失败!");
        }
        return result;
    }
    
    @Override
    public int updateBatch(List<CocWorkOrderSupervise> cocWorkOrderSuperviseList) {
        int result = 0;
        try {
            result = cocWorkOrderSuperviseMapper.updateBatch(cocWorkOrderSuperviseList);
        } catch (final Exception e) {
            log.error("coc_worker_order_supervise批量更新失败!异常信息：{}", e.getMessage(), e);
            throw new ServerException("批量更新工单督办信息失败!");
        }
        return result;
    }
    
    @Override
    public List<CocWorkOrderSupervise> selectAll() {
        List<CocWorkOrderSupervise> result = Lists.newArrayList();
        QueryWrapper<CocWorkOrderSupervise> queryWrapper = new QueryWrapper<>();
        try {
            result = list(queryWrapper);
        } catch (final Exception e) {
            log.error("查询全量coc_work_order_supervise数据失败!异常信息：{}，具体信息：{}", e.getMessage(), e);
            throw new ServerException("查询全量工单督办信息失败!");
        }
        return result;
    }
    
    @Override
    public List<CocWorkOrderSupervise> selectByWorkOrderNoList(List<String> workOrderNoList) {
        List<CocWorkOrderSupervise> result = Lists.newArrayList();
        QueryWrapper<CocWorkOrderSupervise> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("work_order_no", workOrderNoList);
        try {
            result = list(queryWrapper);
        } catch (final Exception e) {
            log.error("根据工单号列表查询coc_work_order_supervise数据失败!异常信息：{}，具体信息：{}", e.getMessage(), e);
            throw new ServerException("根据工单号列表查询工单督办信息失败!");
        }
        return result;
    }
    
}
