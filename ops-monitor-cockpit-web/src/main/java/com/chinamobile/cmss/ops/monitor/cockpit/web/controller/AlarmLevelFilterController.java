/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.AlarmLevelFilterDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmLevelFilter;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.AlarmLevelFilterService;

import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmLevelFilterStatisticVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 告警等级过滤器控制器
 */
@RestController
@RequestMapping("/alarmLevelFilter")
public class AlarmLevelFilterController {
    
    @Autowired
    private AlarmLevelFilterService alarmLevelFilterService;
    
    /**
     * 新增告警等级过滤器
     * @param filterName f
     * @param filterId   f
     * @param enable     e
     * @return a
     */
    @PostMapping("/add")
    public boolean addAlarmLevelFilter(@RequestParam("filterName") String filterName, @RequestParam("filterId") String filterId,
                                       @RequestParam("enable") Short enable) {
        return alarmLevelFilterService.add(filterName, filterId, enable);
    }
    
    /**
     * 删除告警等级过滤器
     * @param id id
     * @throws IOException io
     */
    @DeleteMapping("/del")
    public void deleteAlarmLevelFilter(@RequestParam("id") Integer id) {
        alarmLevelFilterService.delete(id);
    }
    
    /**
     * 查询告警等级过滤器
     * @return lst
     */
    @GetMapping("/list")
    public List<AlarmLevelFilter> alarmLevelFilterList() {
        return alarmLevelFilterService.getAlarmLevelFilterList();
    }
    
    /**
     * 修改告警等级过滤器
     * @param filterDTO f
     * @return a
     */
    @PutMapping("/update")
    public boolean updateAlarmLevelFilter(@Validated @RequestBody AlarmLevelFilterDTO filterDTO) {
        return alarmLevelFilterService.update(filterDTO);
    }
    
    /**
     * 启用告警等级过滤器
     * @param id     告警等级过滤器ID
     * @param enable a
     * @return 是否成功
     */
    @PutMapping("/enable")
    public boolean enableAlarmLevelFilter(@RequestParam("id") Integer id, @RequestParam("enable") Short enable) {
        return alarmLevelFilterService.updateStatus(id, enable);
    }
    
    /**
     * 调整顺序
     * @param sortOrder s
     * @return f
     */
    @PutMapping("/order")
    public boolean updateOrder(@RequestBody Map<Integer, Integer> sortOrder) {
        return alarmLevelFilterService.updateOrder(sortOrder);
    }
    
    /**
     * 查询告警等级过滤器的各个告警数量
     * @param startTime 发生时间
     * @param endTime   发生时间
     * @param id        id
     * @return AlarmLevelFilterStatisticVo
     */
    @GetMapping("/statisticAlarmCount")
    public AlarmLevelFilterStatisticVo alarmTypeFilterCountVo(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime,
                                                              @RequestParam(required = false, name = "id") Integer id) {
        return alarmLevelFilterService.getAlarmLevelFilterStatistic(startTime, endTime, id);
    }
    
}