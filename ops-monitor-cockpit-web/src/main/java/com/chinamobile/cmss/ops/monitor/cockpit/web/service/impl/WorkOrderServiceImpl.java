/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chinamobile.cmss.ops.monitor.cockpit.common.constant.MessageConstant;
import com.chinamobile.cmss.ops.monitor.cockpit.common.constant.NumberConstant;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ServerException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.AESGCMUtils;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.dto.DataEngineQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.DataEngineResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.WorkOrderNumStatistic;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.WorkOrderOverview;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.service.DataEngineService;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.dto.CloudSpaceMessageSendDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.dto.ShortMessageSendDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.pojo.CloudSpaceMessageResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.pojo.ShortMessageResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.service.MessageService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dao.CocWorkOrderSuperviseDAO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.WorkOrderSuperviseDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.WorkOrderSuperviseResultDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.CocWorkOrderSupervise;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.WorkOrderService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderClassifySuperviseVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderSuperviseItemVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderSuperviseResultVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderSuperviseVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderNumVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 工单服务实现类
 */
@Service
@Slf4j
public class WorkOrderServiceImpl implements WorkOrderService {
    
    private static final int ONE_DAY_SECONDS = 86400;
    
    @Autowired
    @Qualifier("cockpitThreadPool")
    private Executor threadPool;
    
    @Autowired
    private DataEngineService dataEngineService;
    
    @Autowired
    private MessageService messageService;
    
    @Autowired
    private CocWorkOrderSuperviseDAO cocWorkOrderSuperviseDAO;
    
    @Value("${work.order.supervise.compensate}")
    private Boolean workOrderSuperviseCompensate;
    
    @Override
    public WorkOrderSuperviseVO querySuperviseList() {
        
        // 从数据治理查询当日的故障工单概览数据列表
        List<WorkOrderOverview> faultWorkOrderOverviewList = queryFaultWorkOrderOverviewList();
        
        // 从数据治理查询当日的事件工单概览数据列表
        List<WorkOrderOverview> incidentWorkOrderOverviewList = queryIncidentWorkOrderOverviewList();
        
        // 将故障工单和事件工单合并
        List<WorkOrderOverview> workOrderOverviewList = Lists.newArrayList();
        
        if (CollectionUtil.isNotEmpty(faultWorkOrderOverviewList)) {
            workOrderOverviewList.addAll(faultWorkOrderOverviewList);
        }
        if (CollectionUtil.isNotEmpty(incidentWorkOrderOverviewList)) {
            workOrderOverviewList.addAll(incidentWorkOrderOverviewList);
        }
        
        // 声明最终返回VO
        WorkOrderSuperviseVO workOrderSuperviseVO = new WorkOrderSuperviseVO();
        
        if (CollectionUtil.isNotEmpty(workOrderOverviewList)) {
            // 先按照createTime，将所有工单进行创建时间的降序排序
            // 降序排序（最新的在前），null 值排在最后
            workOrderOverviewList.sort(Comparator.comparing(
                    WorkOrderOverview::getCreateTime,
                    Comparator.nullsLast(Comparator.reverseOrder())));
            
            // 查询督办数据库表
            List<CocWorkOrderSupervise> cocWorkOrderSuperviseList = cocWorkOrderSuperviseDAO.selectAll();
            
            // 遍历一次，按照总计/即将超时/已超时组装各自的VO，注意手机号需要AESGCM加密
            workOrderSuperviseVO = buildResultSuperviseVO(workOrderOverviewList, cocWorkOrderSuperviseList);
            
        } else {
            // 构造全部督办列表/即将超时督办列表/已超时督办列表（数目均为0）
            WorkOrderClassifySuperviseVO totalSuperviseVO = WorkOrderClassifySuperviseVO.builder()
                    .workOrderSuperviseItemVOList(Lists.newArrayList()).workOrderNum(NumberConstant.ZERO).build();
            WorkOrderClassifySuperviseVO approachTimeoutSuperviseVO = WorkOrderClassifySuperviseVO.builder()
                    .workOrderSuperviseItemVOList(Lists.newArrayList()).workOrderNum(NumberConstant.ZERO).build();
            WorkOrderClassifySuperviseVO timeoutSuperviseVO = WorkOrderClassifySuperviseVO.builder()
                    .workOrderSuperviseItemVOList(Lists.newArrayList()).workOrderNum(NumberConstant.ZERO).build();
            workOrderSuperviseVO.setTotalSuperviseVO(totalSuperviseVO);
            workOrderSuperviseVO.setApproachTimeoutSuperviseVO(approachTimeoutSuperviseVO);
            workOrderSuperviseVO.setTimeoutSuperviseVO(timeoutSuperviseVO);
        }
        
        return workOrderSuperviseVO;
    }
    
    @Override
    public WorkOrderNumVO queryOperatingChangeWorkOrderNum() {
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.systemDefault());
        
        Instant now = Instant.now();
        
        StringBuilder querySQLBuilder = new StringBuilder();
        querySQLBuilder.append("select count(distinct cell1) workOrderNum from 3_ods_ccops_work_order_change_rt_foc where createTime BETWEEN '");
        querySQLBuilder.append(formatter.format(now.minusSeconds(ONE_DAY_SECONDS)));
        querySQLBuilder.append("' AND '");
        querySQLBuilder.append(now);
        querySQLBuilder.append("' and changeState = '实施变更' limit 1000");
        String querySQL = querySQLBuilder.toString();
        log.info("请求数据治理平台的正在实施中的变更工单数目，最终的查询SQL：{}", querySQL);
        DataEngineQueryDTO dataEngineQueryDTO = DataEngineQueryDTO.builder().sql(querySQL).preferStorage("doris").build();
        log.info("开始请求数据治理平台当日正在实施中的变更工单数目");
        DataEngineResponse<WorkOrderNumStatistic> workOrderNumStatisticResponse = dataEngineService.getWorkOrderNum(dataEngineQueryDTO);
        log.info("请求数据治理平台正在实施中的变更工单数目返回结果:{}", workOrderNumStatisticResponse);
        Integer workOrderNum = workOrderNumStatisticResponse.getData().getList().get(0).getWorkOrderNum();
        return WorkOrderNumVO.builder().workOrderNum(workOrderNum).build();
    }
    
    @Override
    public List<WorkOrderSuperviseResultVO> batchSupervise(List<WorkOrderSuperviseDTO> superviseDTOList) {
        
        // 筛选有效的需要督办的工单（处理人的手机号不能为空）
        List<WorkOrderSuperviseDTO> effectiveSuperviseDTOList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(superviseDTOList)) {
            if (superviseDTOList.size() > 50) {
                throw new ServerException("督办工单个数不能超过50");
            }
            for (WorkOrderSuperviseDTO superviseDTO : superviseDTOList) {
                if (StrUtil.isNotBlank(superviseDTO.getProcessorPhone())) {
                    // 解密手机号
                    superviseDTO.setProcessorPhone(AESGCMUtils.decryptText(superviseDTO.getProcessorPhone()));
                    effectiveSuperviseDTOList.add(superviseDTO);
                }
            }
        }
        
        // 定义返回结果
        List<WorkOrderSuperviseResultVO> superviseResultVOList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(effectiveSuperviseDTOList)) {
            // 筛选有效督办的工单号列表
            List<String> effectiveWorkOrderNoList = effectiveSuperviseDTOList.stream()
                    .map(WorkOrderSuperviseDTO::getWorkOrderNo)
                    .collect(Collectors.toList());
            
            // CompleteFuture进行多线程并发请求云空间，汇总返回结果
            List<CompletableFuture<WorkOrderSuperviseResultDTO>> futures = effectiveSuperviseDTOList.stream()
                    .map(superviseDTO -> CompletableFuture.supplyAsync(
                            () -> superviseSingleWorkOrderWithExceptionHandling(superviseDTO),
                            threadPool))
                    .collect(Collectors.toList());
            
            // 2. 合并所有Future
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0]));
            
            // 3. 获取结果列表（阻塞直到所有任务完成）
            CompletableFuture<List<WorkOrderSuperviseResultDTO>> resultsFuture = allFutures.thenApply(v -> futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList()));
            
            List<WorkOrderSuperviseResultDTO> superviseResultDTOList = resultsFuture.join();
            
            List<CocWorkOrderSupervise> existingSuperviseList = cocWorkOrderSuperviseDAO.selectByWorkOrderNoList(effectiveWorkOrderNoList);
            // 构建数据库已有的工单号和督办情况的映射
            Map<String, CocWorkOrderSupervise> existingSuperviseMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(existingSuperviseList)) {
                for (CocWorkOrderSupervise existingSupervise : existingSuperviseList) {
                    existingSuperviseMap.put(existingSupervise.getWorkOrderNo(), existingSupervise);
                }
            }
            
            // 对于每个工单而言，如果处理人云空间（短信补偿）发送成功，则认为督办成功，则督办次数+1,督办时间更新
            List<CocWorkOrderSupervise> updateWorkOrderSuperviseList = Lists.newArrayList();
            List<CocWorkOrderSupervise> insertWorkOrderSuperviseList = Lists.newArrayList();
            Date currentTime = new Date();
            for (WorkOrderSuperviseResultDTO superviseResultDTO : superviseResultDTOList) {
                if (MessageConstant.UserSendResult.SUCCESS.equals(superviseResultDTO.getUserSendResult()) ||
                        MessageConstant.UserSendResult.COMPENSATE.equals(superviseResultDTO.getUserSendResult())) {
                    // 如果督办表已有工单，则更新，如果督办表没有工单，则新增
                    if (existingSuperviseMap.containsKey(superviseResultDTO.getWorkOrderNo())) {
                        CocWorkOrderSupervise workOrderSupervise = existingSuperviseMap.get(superviseResultDTO.getWorkOrderNo());
                        Integer lastestSuperviseNum = workOrderSupervise.getSuperviseNum() + 1;
                        workOrderSupervise.setSuperviseNum(lastestSuperviseNum);
                        workOrderSupervise.setLastSuperviseTime(currentTime);
                        updateWorkOrderSuperviseList.add(workOrderSupervise);
                        superviseResultVOList.add(WorkOrderSuperviseResultVO.builder().workOrderNo(superviseResultDTO.getWorkOrderNo())
                                .superviseNum(lastestSuperviseNum).lastSuperviseTime(currentTime).build());
                    } else {
                        CocWorkOrderSupervise workOrderSupervise = CocWorkOrderSupervise.builder().id(UUID.randomUUID().toString())
                                .workOrderNo(superviseResultDTO.getWorkOrderNo())
                                .superviseNum(NumberConstant.ONE).lastSuperviseTime(currentTime).build();
                        insertWorkOrderSuperviseList.add(workOrderSupervise);
                        superviseResultVOList.add(WorkOrderSuperviseResultVO.builder().workOrderNo(superviseResultDTO.getWorkOrderNo())
                                .superviseNum(NumberConstant.ONE).lastSuperviseTime(currentTime).build());
                    }
                }
            }
            
            // 数据库更新，插入
            if (CollectionUtil.isNotEmpty(insertWorkOrderSuperviseList)) {
                cocWorkOrderSuperviseDAO.insertBatch(insertWorkOrderSuperviseList);
            }
            if (CollectionUtil.isNotEmpty(updateWorkOrderSuperviseList)) {
                cocWorkOrderSuperviseDAO.updateBatch(updateWorkOrderSuperviseList);
            }
        }
        return superviseResultVOList;
    }
    
    /**
     * 单个工单的督办，返回是否督办成功
     * @param workOrderSuperviseDTO 督办DTO
     * @return WorkOrderSuperviseResultDTO
     */
    private WorkOrderSuperviseResultDTO superviseSingleWorkOrder(WorkOrderSuperviseDTO workOrderSuperviseDTO) {
        
        // 构建发送内容
        StringBuilder sendContentSBuilder = new StringBuilder();
        sendContentSBuilder.append("您好，您有以下工单待认领/即将超时/已超时，请尽快处理：");
        sendContentSBuilder.append("\n");
        sendContentSBuilder.append("工单");
        sendContentSBuilder.append(workOrderSuperviseDTO.getWorkOrderNo());
        sendContentSBuilder.append("，");
        sendContentSBuilder.append(workOrderSuperviseDTO.getFaultLevel());
        sendContentSBuilder.append("，所属资源池：");
        sendContentSBuilder.append(workOrderSuperviseDTO.getResourcePool());
        sendContentSBuilder.append("，工单标题：");
        sendContentSBuilder.append(workOrderSuperviseDTO.getWorkOrderTitle());
        String sendContent = sendContentSBuilder.toString();
        log.info("即将发往云空间的内容:{}", sendContent);
        Integer userSendResult = MessageConstant.UserSendResult.FAIL;
        // 发送自有人员云空间
        CloudSpaceMessageSendDTO innerCloudSpaceSendDTO = CloudSpaceMessageSendDTO.builder()
                .categoryName(MessageConstant.CloudSpace.FaultNotification.CATEGORY_NAME)
                .content(sendContent).userMobiles(workOrderSuperviseDTO.getProcessorPhone())
                .message(MessageConstant.CloudSpace.FaultNotification.MESSAGE)
                .sender(MessageConstant.CloudSpace.FaultNotification.SENDER)
                .sendUserType(MessageConstant.CloudSpace.SendUserType.INTERNAL)
                .title(MessageConstant.CloudSpace.FaultNotification.TITLE).build();
        log.info("发往内部人员云空间，请求:{}", JSONUtil.toJsonStr(innerCloudSpaceSendDTO));
        CloudSpaceMessageResponse innerCloudSpaceMessageResponse = messageService.sendCloudSpaceMessage(innerCloudSpaceSendDTO);
        log.info("发往内部人员云空间，响应:{}", JSONUtil.toJsonStr(innerCloudSpaceMessageResponse));
        if (MessageConstant.CloudSpace.SendResult.SUCCESS_CODE.equals(innerCloudSpaceMessageResponse.getStatus())
                && StrUtil.isBlank(innerCloudSpaceMessageResponse.getSegmentFailMobiles())) {
            // 如果为成功的响应码以及失败手机号为空，则说明发送成功
            userSendResult = MessageConstant.UserSendResult.SUCCESS;
        } else {
            // 发送外协云空间
            CloudSpaceMessageSendDTO outerCloudSpaceSendDTO = CloudSpaceMessageSendDTO.builder()
                    .categoryName(MessageConstant.CloudSpace.FaultNotification.CATEGORY_NAME)
                    .content(sendContent).userMobiles(workOrderSuperviseDTO.getProcessorPhone())
                    .message(MessageConstant.CloudSpace.FaultNotification.MESSAGE)
                    .sender(MessageConstant.CloudSpace.FaultNotification.SENDER)
                    .sendUserType(MessageConstant.CloudSpace.SendUserType.OUTSOURCE)
                    .title(MessageConstant.CloudSpace.FaultNotification.TITLE).build();
            log.info("发往外协人员云空间，请求:{}", JSONUtil.toJsonStr(outerCloudSpaceSendDTO));
            CloudSpaceMessageResponse outerCloudSpaceMessageResponse = messageService.sendCloudSpaceMessage(outerCloudSpaceSendDTO);
            log.info("发往外协人员云空间，响应:{}", JSONUtil.toJsonStr(outerCloudSpaceMessageResponse));
            if (MessageConstant.CloudSpace.SendResult.SUCCESS_CODE.equals(outerCloudSpaceMessageResponse.getStatus())
                    && StrUtil.isBlank(outerCloudSpaceMessageResponse.getSegmentFailMobiles())) {
                // 如果为成功的响应码以及失败手机号为空，则说明发送成功
                userSendResult = MessageConstant.UserSendResult.SUCCESS;
            } else {
                if (workOrderSuperviseCompensate) {
                    // 如果业务侧需要补偿，发送短信补偿，更新发送结果
                    ShortMessageSendDTO shortMessageSendDTO = ShortMessageSendDTO.builder().mobiles(Arrays.asList(workOrderSuperviseDTO.getProcessorPhone()))
                            .notifyDes(sendContent).build();
                    log.info("发送短信，请求:{}", JSONUtil.toJsonStr(shortMessageSendDTO));
                    ShortMessageResponse shortMessageResponse = messageService.sendShortMessage(shortMessageSendDTO);
                    log.info("发送短信，响应:{}", JSONUtil.toJsonStr(shortMessageResponse));
                    if (MessageConstant.Message.SUCCESS_CODE.equals(shortMessageResponse.getCode())) {
                        userSendResult = MessageConstant.UserSendResult.SUCCESS;
                    }
                }
            }
        }
        return WorkOrderSuperviseResultDTO.builder().workOrderNo(workOrderSuperviseDTO.getWorkOrderNo())
                .userSendResult(userSendResult).build();
    }
    
    /**
     * 带异常处理的督办方法
     * @param workOrderSuperviseDTO 督办DTO
     * @return WorkOrderSuperviseResultDTO
     */
    private WorkOrderSuperviseResultDTO superviseSingleWorkOrderWithExceptionHandling(WorkOrderSuperviseDTO workOrderSuperviseDTO) {
        try {
            return superviseSingleWorkOrder(workOrderSuperviseDTO);
        } catch (Exception e) {
            // 异常时返回失败结果
            return WorkOrderSuperviseResultDTO.builder().workOrderNo(workOrderSuperviseDTO.getWorkOrderNo())
                    .userSendResult(MessageConstant.UserSendResult.FAIL).build();
        }
    }
    
    /**
     * 构建最终返回的督办VO
     * @param workOrderOverviewList     从数据治理获取的工单（故障工单/事件工单）的概览列表
     * @param cocWorkOrderSuperviseList 工单督办信息列表
     * @return WorkOrderSuperviseVO
     */
    private WorkOrderSuperviseVO buildResultSuperviseVO(List<WorkOrderOverview> workOrderOverviewList,
                                                        List<CocWorkOrderSupervise> cocWorkOrderSuperviseList) {
        
        Map<String, Integer> workOrderNoAndSuperviseNumMap = Maps.newHashMap();
        Map<String, Date> workOrderNoAndLastSuperviseTimeMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(cocWorkOrderSuperviseList)) {
            for (CocWorkOrderSupervise cocWorkOrderSupervise : cocWorkOrderSuperviseList) {
                workOrderNoAndSuperviseNumMap.put(cocWorkOrderSupervise.getWorkOrderNo(), cocWorkOrderSupervise.getSuperviseNum());
                workOrderNoAndLastSuperviseTimeMap.put(cocWorkOrderSupervise.getWorkOrderNo(), cocWorkOrderSupervise.getLastSuperviseTime());
            }
        }
        
        Date currentTime = new Date();
        List<WorkOrderSuperviseItemVO> totalSuperviseItemVOList = Lists.newArrayList();
        List<WorkOrderSuperviseItemVO> approachTimeoutSuperviseItemVOList = Lists.newArrayList();
        List<WorkOrderSuperviseItemVO> timeoutSuperviseItemVOList = Lists.newArrayList();
        
        for (WorkOrderOverview workOrderOverview : workOrderOverviewList) {
            // 抽取即将超时和已超时的工单
            Date dealTime = workOrderOverview.getDealTime();
            if (Objects.nonNull(dealTime)) {
                WorkOrderSuperviseItemVO timeoutSuperviseItemVO = WorkOrderSuperviseItemVO.builder().workOrderNo(workOrderOverview.getWorkOrderNo())
                        .workOrderTitle(workOrderOverview.getWorkOrderTitle()).faultLevel(workOrderOverview.getFaultLevel())
                        .createTime(workOrderOverview.getCreateTime()).dealTime(workOrderOverview.getDealTime()).state(workOrderOverview.getState())
                        .resourcePool(workOrderOverview.getResourcePool()).processor(workOrderOverview.getProcessor())
                        .processorPhone(StrUtil.isNotBlank(workOrderOverview.getProcessorPhone()) ? AESGCMUtils.encryptText(workOrderOverview.getProcessorPhone()) : null)
                        .superviseNum(workOrderNoAndSuperviseNumMap.getOrDefault(workOrderOverview.getWorkOrderNo(), NumberConstant.ZERO))
                        .lastSuperviseTime(workOrderNoAndLastSuperviseTimeMap.get(workOrderOverview.getWorkOrderNo()))
                        .build();
                if (currentTime.after(dealTime)) {
                    // 当前时间 > 环节办理截止时间，已超时
                    totalSuperviseItemVOList.add(timeoutSuperviseItemVO);
                    timeoutSuperviseItemVOList.add(timeoutSuperviseItemVO);
                } else {
                    // 判断环节办理截止时间-当前时间是否小于30min，如果符合，则即将超时
                    long diffInMillis = dealTime.getTime() - currentTime.getTime();
                    // 30 分钟的毫秒数
                    long thirtyMinutesInMillis = 30 * 60 * 1000;
                    if (diffInMillis < thirtyMinutesInMillis) {
                        totalSuperviseItemVOList.add(timeoutSuperviseItemVO);
                        approachTimeoutSuperviseItemVOList.add(timeoutSuperviseItemVO);
                    }
                }
            }
        }
        
        WorkOrderClassifySuperviseVO totalSuperviseVO = WorkOrderClassifySuperviseVO.builder().workOrderSuperviseItemVOList(totalSuperviseItemVOList)
                .workOrderNum(totalSuperviseItemVOList.size()).build();
        WorkOrderClassifySuperviseVO approachTimeoutSuperviseVO = WorkOrderClassifySuperviseVO.builder().workOrderSuperviseItemVOList(approachTimeoutSuperviseItemVOList)
                .workOrderNum(approachTimeoutSuperviseItemVOList.size()).build();
        WorkOrderClassifySuperviseVO timeoutSuperviseVO = WorkOrderClassifySuperviseVO.builder().workOrderSuperviseItemVOList(timeoutSuperviseItemVOList)
                .workOrderNum(timeoutSuperviseItemVOList.size()).build();
        
        return WorkOrderSuperviseVO.builder().totalSuperviseVO(totalSuperviseVO).approachTimeoutSuperviseVO(approachTimeoutSuperviseVO)
                .timeoutSuperviseVO(timeoutSuperviseVO).build();
    }
    
    /**
     * 从数据治理查询当日的事件工单概览数据列表
     * @return List
     */
    private List<WorkOrderOverview> queryIncidentWorkOrderOverviewList() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.systemDefault());
        Instant now = Instant.now();
        StringBuilder querySQLBuilder = new StringBuilder();
        querySQLBuilder.append("select cell1,cell14,fault_level,dealtime,resource_pool,processor,create_time,state,processor_phone");
        querySQLBuilder.append(" from 3_ods_ccops_work_order_incident_rt where create_time BETWEEN '");
        
        querySQLBuilder.append(formatter.format(now.minusSeconds(ONE_DAY_SECONDS)));
        querySQLBuilder.append("' AND '");
        querySQLBuilder.append(now);
        querySQLBuilder.append("' and cell1 != '' and cell1 is not null");
        querySQLBuilder.append(" and state= '前端处理-二线' limit 1000");
        String querySQL = querySQLBuilder.toString();
        log.info("请求数据治理平台事件工单列表，最终的查询SQL：{}", querySQL);
        DataEngineQueryDTO dataEngineQueryDTO = DataEngineQueryDTO.builder().sql(querySQL).preferStorage("doris").build();
        log.info("开始请求数据治理平台当日事件工单列表");
        DataEngineResponse<WorkOrderOverview> workOrderOverviewResponse = dataEngineService.getSuperviseWorkOrderList(dataEngineQueryDTO);
        log.info("请求数据治理平台事件工单返回结果:{}", workOrderOverviewResponse);
        return workOrderOverviewResponse.getData().getList();
    }
    
    /**
     * 从数据治理查询当日的故障工单概览数据列表
     * @return List
     */
    private List<WorkOrderOverview> queryFaultWorkOrderOverviewList() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.systemDefault());
        Instant now = Instant.now();
        
        StringBuilder querySQLBuilder = new StringBuilder();
        querySQLBuilder.append("select cell1,cell14,fault_level,dealtime,resource_pool,processor,create_time,state,processor_phone from 3_ods_ccops_work_order_fault_rt where create_time BETWEEN '");
        querySQLBuilder.append(formatter.format(now.minusSeconds(ONE_DAY_SECONDS)));
        querySQLBuilder.append("' AND '");
        querySQLBuilder.append(now);
        querySQLBuilder.append("' and state= '二线处理' limit 1000");
        String querySQL = querySQLBuilder.toString();
        log.info("请求数据治理平台故障工单列表，最终的查询SQL：{}", querySQL);
        DataEngineQueryDTO dataEngineQueryDTO = DataEngineQueryDTO.builder().sql(querySQL).preferStorage("doris").build();
        log.info("开始请求数据治理平台当日故障工单列表");
        DataEngineResponse<WorkOrderOverview> workOrderOverviewResponse = dataEngineService.getSuperviseWorkOrderList(dataEngineQueryDTO);
        log.info("请求数据治理平台故障工单返回结果:{}", workOrderOverviewResponse);
        return workOrderOverviewResponse.getData().getList();
    }
}
