/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.vo;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Describe
 */
@Data
@Builder
public class MajorDutyVO {
    
    /**
     * 值班模板名称
     */
    private String dutyTemplateName;
    
    /**
     * 值班长
     */
    private String dutyLeader;
    
    /**
     * 值班长手机号
     */
    private String dutyLeaderPhone;
    
    /**
     * 专业值班信息
     */
    private List<MajorDutyPersonVO> majorDutyPersonVOList;
    
    @Data
    public static class MajorDutyPersonVO {
        
        /**
         * 专业名称
         */
        private String majorName;
        
        /**
         * 值班人员
         */
        private String dutyPerson;
        
        /**
         * 手机号
         */
        private String dutyPersonPhone;
        
        /**
         * 值班人员应到数量
         */
        private Integer expectedDutyCount;
        
        /**
         * 执勤日期
         */
        private String dutyDate;
        
        /**
         * 班次开始时间 00:00:0
         */
        private String startTime;
        
        /**
         * 班次结束时间 24:00:00
         */
        private String endTime;
        
        public MajorDutyPersonVO() {
        }
        
        public MajorDutyPersonVO(String dutyPerson, String dutyPersonPhone, Integer expectedDutyCount, String startTime, String endTime, String dutyDate) {
            this.dutyPerson = dutyPerson;
            this.dutyPersonPhone = dutyPersonPhone;
            this.expectedDutyCount = expectedDutyCount;
            this.startTime = startTime;
            this.endTime = endTime;
            this.dutyDate = dutyDate;
        }
    }
}
