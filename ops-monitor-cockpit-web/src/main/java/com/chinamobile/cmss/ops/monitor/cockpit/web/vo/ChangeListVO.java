/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Describe
 */
@Data
public class ChangeListVO {
    
    /**
     * 总数
     */
    private Long total;
    
    private List<ChangeEntity> changeEntity;
    
    @Data
    public static class ChangeEntity {
        
        /**
         * 变更单号
         */
        private String changeId;
        
        /**
         * 工单标题（来自字段 cell9）
         */
        private String cell9;
        
        /**
         * 变更状态（示例值：“变更实施”表示正在进行）
         */
        private String changeState;
        
        /**
         * 申请部门
         */
        private String creatorDept;
        
        /**
         * 变更等级（风险等级）
         */
        private String riskLevel;
        
        /**
         * 所属应用系统
         */
        private String businessSystem;
        
        /**
         * 发布类型
         */
        private String publishType;
        
        /**
         * 变更实施人
         */
        @JsonProperty("implementer")
        private String implementer;
    }
    
}
