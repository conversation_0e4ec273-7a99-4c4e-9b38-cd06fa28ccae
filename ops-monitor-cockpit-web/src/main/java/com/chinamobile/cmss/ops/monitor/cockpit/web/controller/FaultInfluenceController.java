/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.web.service.FaultInfluenceService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.FaultLevelAggregateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 故障影响面控制器
 */
@RestController
@RequestMapping("/fault/influence")
public class FaultInfluenceController {
    
    @Autowired
    private FaultInfluenceService faultInfluenceService;
    
    /**
     * 查询驾驶舱的工单系统故障等级统计信息
     * @return 故障等级统计信息
     */
    @GetMapping("/queryAggregateFaultLevel")
    public FaultLevelAggregateVO queryAggregateFaultLevel() {
        return faultInfluenceService.queryAggregateFaultLevel();
    }
}
