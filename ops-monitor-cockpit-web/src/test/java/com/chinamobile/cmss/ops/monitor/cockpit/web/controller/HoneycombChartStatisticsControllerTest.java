/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.HoneycombChartListQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.HoneycombChartStatisticsDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.HoneycombChartStatisticsService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.BkOperatorTypeVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.BkRegionVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.ChangeListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.FaultListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.HoneycombChartOverviewVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * HoneycombChartStatisticsController 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class HoneycombChartStatisticsControllerTest {
    
    @Mock
    private HoneycombChartStatisticsService honeycombChartStatisticsService;
    
    @InjectMocks
    private HoneycombChartStatisticsController honeycombChartStatisticsController;
    
    private MockMvc mockMvc;
    
    private ObjectMapper objectMapper;
    
    private BkOperatorTypeVO mockOperatorTypeVO;
    
    private BkRegionVO mockRegionVO;
    
    private HoneycombChartOverviewVO mockOverviewVO;
    
    private AlarmListVO mockAlarmListVO;
    
    private FaultListVO mockFaultListVO;
    
    private ChangeListVO mockChangeListVO;
    
    private HoneycombChartStatisticsDTO mockStatisticsDTO;
    
    private HoneycombChartListQueryDTO mockListQueryDTO;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(honeycombChartStatisticsController).build();
        objectMapper = new ObjectMapper();
        
        // 初始化资源池类型VO
        mockOperatorTypeVO = BkOperatorTypeVO.builder().operatorTypeId("CLOUD").operatorTypeName("云资源池").build();
        
        // 初始化资源池VO
        mockRegionVO = BkRegionVO.builder().regionCode("REGION001").regionName("华东资源池").operationType("CLOUD")
                .province("上海").build();
        
        // 初始化蜂窝图总览VO
        mockOverviewVO = new HoneycombChartOverviewVO();
        mockOverviewVO.setTotal(10L);
        
        // 初始化告警列表VO
        mockAlarmListVO = new AlarmListVO();
        mockAlarmListVO.setTotal(5L);
        
        // 初始化故障列表VO
        mockFaultListVO = new FaultListVO();
        mockFaultListVO.setTotal(3L);
        
        // 初始化变更列表VO
        mockChangeListVO = new ChangeListVO();
        mockChangeListVO.setTotal(8L);
        
        // 初始化统计DTO
        mockStatisticsDTO = new HoneycombChartStatisticsDTO();
        mockStatisticsDTO.setOperationType("CLOUD");
        mockStatisticsDTO.setOverviewType(Arrays.asList("ALARM", "FAULT"));
        mockStatisticsDTO.setPageSize(66);
        mockStatisticsDTO.setPageNum(1);
        
        // 初始化列表查询DTO
        mockListQueryDTO = new HoneycombChartListQueryDTO();
        mockListQueryDTO.setRegionCode("REGION001");
        mockListQueryDTO.setRegionName("华东资源池");
        mockListQueryDTO.setStartDate("2025-01-01");
        mockListQueryDTO.setEndDate("2025-01-07");
        mockListQueryDTO.setPageSize(10);
        mockListQueryDTO.setPageNum(1);
    }
    
    /**
     * 测试获取资源池类型列表 - 成功情况
     */
    @Test
    void testGetOperationTypeSuccess() throws Exception {
        List<BkOperatorTypeVO> mockTypeList = Arrays.asList(mockOperatorTypeVO);
        
        // 模拟服务层返回资源池类型列表
        when(honeycombChartStatisticsService.getOperationTypeFromCmdb()).thenReturn(mockTypeList);
        
        // 执行测试
        mockMvc.perform(get("/honeycomb-chart/statistics/operation-type")).andExpect(status().isOk())
                .andExpect(jsonPath("$[0].operatorTypeId").value("CLOUD"))
                .andExpect(jsonPath("$[0].operatorTypeName").value("云资源池"));
        
        // 验证服务层方法被调用
        verify(honeycombChartStatisticsService, times(1)).getOperationTypeFromCmdb();
    }
    
    /**
     * 测试获取资源池类型列表 - 空列表情况
     */
    @Test
    void testGetOperationTypeEmptyList() throws Exception {
        // 模拟服务层返回空列表
        when(honeycombChartStatisticsService.getOperationTypeFromCmdb()).thenReturn(Arrays.asList());
        
        // 执行测试
        mockMvc.perform(get("/honeycomb-chart/statistics/operation-type")).andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray()).andExpect(jsonPath("$").isEmpty());
        
        // 验证服务层方法被调用
        verify(honeycombChartStatisticsService, times(1)).getOperationTypeFromCmdb();
    }
    
    /**
     * 测试获取资源池列表 - 成功情况（带参数）
     */
    @Test
    void testGetRegionWithOperationTypeSuccess() throws Exception {
        List<BkRegionVO> mockRegionList = Arrays.asList(mockRegionVO);
        
        // 模拟服务层返回资源池列表
        when(honeycombChartStatisticsService.getRegionFromCmdb(anyString())).thenReturn(mockRegionList);
        
        // 执行测试
        mockMvc.perform(get("/honeycomb-chart/statistics/region").param("operationType", "CLOUD"))
                .andExpect(status().isOk()).andExpect(jsonPath("$[0].regionCode").value("REGION001"))
                .andExpect(jsonPath("$[0].regionName").value("华东资源池"))
                .andExpect(jsonPath("$[0].operationType").value("CLOUD"));
        
        // 验证服务层方法被调用
        verify(honeycombChartStatisticsService, times(1)).getRegionFromCmdb("CLOUD");
    }
    
    /**
     * 测试获取资源池列表 - 无参数情况
     */
    @Test
    void testGetRegionWithoutOperationTypeSuccess() throws Exception {
        List<BkRegionVO> mockRegionList = Arrays.asList(mockRegionVO);
        
        // 模拟服务层返回资源池列表
        when(honeycombChartStatisticsService.getRegionFromCmdb(isNull())).thenReturn(mockRegionList);
        
        // 执行测试
        mockMvc.perform(get("/honeycomb-chart/statistics/region")).andExpect(status().isOk())
                .andExpect(jsonPath("$[0].regionCode").value("REGION001"))
                .andExpect(jsonPath("$[0].regionName").value("华东资源池"));
        
        // 验证服务层方法被调用
        verify(honeycombChartStatisticsService, times(1)).getRegionFromCmdb(null);
    }
    
    /**
     * 测试蜂窝图总览 - 成功情况
     */
    @Test
    void testHoneycombChartOverviewSuccess() throws Exception {
        // 模拟服务层返回蜂窝图总览
        when(honeycombChartStatisticsService.getHoneycombChartOverview(any(HoneycombChartStatisticsDTO.class)))
                .thenReturn(mockOverviewVO);
        
        // 执行测试
        mockMvc.perform(post("/honeycomb-chart/statistics/overview").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockStatisticsDTO))).andExpect(status().isOk())
                .andExpect(jsonPath("$.total").value(10));
        
        // 验证服务层方法被调用
        verify(honeycombChartStatisticsService, times(1))
                .getHoneycombChartOverview(any(HoneycombChartStatisticsDTO.class));
    }
    
    /**
     * 测试告警列表 - 成功情况
     */
    @Test
    void testGetAlarmListSuccess() throws Exception {
        // 模拟服务层返回告警列表
        when(honeycombChartStatisticsService.getAlarmByRegion(any(HoneycombChartListQueryDTO.class)))
                .thenReturn(mockAlarmListVO);
        
        // 执行测试
        mockMvc.perform(post("/honeycomb-chart/statistics/alarm/list").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockListQueryDTO))).andExpect(status().isOk())
                .andExpect(jsonPath("$.total").value(5));
        
        // 验证服务层方法被调用
        verify(honeycombChartStatisticsService, times(1)).getAlarmByRegion(any(HoneycombChartListQueryDTO.class));
    }
    
    /**
     * 测试故障列表 - 成功情况
     */
    @Test
    void testGetFaultListSuccess() throws Exception {
        // 模拟服务层返回故障列表
        when(honeycombChartStatisticsService.getFaultByRegion(any(HoneycombChartListQueryDTO.class)))
                .thenReturn(mockFaultListVO);
        
        // 执行测试
        mockMvc.perform(post("/honeycomb-chart/statistics/fault/list").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockListQueryDTO))).andExpect(status().isOk())
                .andExpect(jsonPath("$.total").value(3));
        
        // 验证服务层方法被调用
        verify(honeycombChartStatisticsService, times(1)).getFaultByRegion(any(HoneycombChartListQueryDTO.class));
    }
    
    /**
     * 测试安全攻击告警列表 - 成功情况
     */
    @Test
    void testGetSecurityAttacksAlarmListSuccess() throws Exception {
        // 模拟服务层返回安全攻击告警列表
        when(honeycombChartStatisticsService.getSecurityAttacksAlarmByRegion(any(HoneycombChartListQueryDTO.class)))
                .thenReturn(mockAlarmListVO);
        
        // 执行测试
        mockMvc.perform(post("/honeycomb-chart/statistics/security-attacks-alarm/list")
                .contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockListQueryDTO)))
                .andExpect(status().isOk()).andExpect(jsonPath("$.total").value(5));
        
        // 验证服务层方法被调用
        verify(honeycombChartStatisticsService, times(1))
                .getSecurityAttacksAlarmByRegion(any(HoneycombChartListQueryDTO.class));
    }
    
    /**
     * 测试变更列表 - 成功情况
     */
    @Test
    void testGetChangeListSuccess() throws Exception {
        // 模拟服务层返回变更列表
        when(honeycombChartStatisticsService.getChangeByRegion(any(HoneycombChartListQueryDTO.class)))
                .thenReturn(mockChangeListVO);
        
        // 执行测试
        mockMvc.perform(post("/honeycomb-chart/statistics/change/list").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockListQueryDTO))).andExpect(status().isOk())
                .andExpect(jsonPath("$.total").value(8));
        
        // 验证服务层方法被调用
        verify(honeycombChartStatisticsService, times(1)).getChangeByRegion(any(HoneycombChartListQueryDTO.class));
    }
    
    /**
     * 测试蜂窝图总览 - 验证失败情况（pageSize小于1）
     */
    @Test
    void testHoneycombChartOverviewValidationFailure() throws Exception {
        // 创建无效的DTO（pageSize小于1）
        HoneycombChartStatisticsDTO invalidDTO = new HoneycombChartStatisticsDTO();
        invalidDTO.setOperationType("CLOUD");
        invalidDTO.setPageSize(0);
        invalidDTO.setPageNum(1);
        
        // 执行测试，期望返回400错误（验证失败）
        mockMvc.perform(post("/honeycomb-chart/statistics/overview").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidDTO))).andExpect(status().isBadRequest());
        
        // 验证服务层方法没有被调用
        verify(honeycombChartStatisticsService, never())
                .getHoneycombChartOverview(any(HoneycombChartStatisticsDTO.class));
    }
}
