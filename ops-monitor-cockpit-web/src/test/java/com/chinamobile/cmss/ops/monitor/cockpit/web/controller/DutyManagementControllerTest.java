/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.DutyAttendanceDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.DutyManagementService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.DutyAttendanceVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.MajorDutyVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * DutyManagementController 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class DutyManagementControllerTest {
    
    @Mock
    private DutyManagementService dutyManagementService;
    
    @InjectMocks
    private DutyManagementController dutyManagementController;
    
    private MockMvc mockMvc;
    
    private ObjectMapper objectMapper;
    
    private MajorDutyVO mockMajorDutyVO;
    
    private DutyAttendanceVO mockDutyAttendanceVO;
    
    private DutyAttendanceDTO mockDutyAttendanceDTO;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(dutyManagementController)
                .defaultResponseCharacterEncoding(StandardCharsets.UTF_8).build();
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        
        // 初始化专业值班信息
        MajorDutyVO.MajorDutyPersonVO personVO = new MajorDutyVO.MajorDutyPersonVO();
        personVO.setMajorName("网络专业");
        personVO.setDutyPerson("张三");
        personVO.setDutyPersonPhone("13800138000");
        personVO.setExpectedDutyCount(2);
        personVO.setDutyDate("2025-01-07");
        personVO.setStartTime("09:00:00");
        personVO.setEndTime("18:00:00");
        
        mockMajorDutyVO = MajorDutyVO.builder().dutyTemplateName("一组值班模板").dutyLeader("李四")
                .dutyLeaderPhone("13900139000").majorDutyPersonVOList(Arrays.asList(personVO)).build();
        
        // 初始化考勤信息
        mockDutyAttendanceVO = new DutyAttendanceVO();
        mockDutyAttendanceVO.setId(1L);
        mockDutyAttendanceVO.setDutyDate("2025-01-07");
        mockDutyAttendanceVO.setTeamTemplateName("一组值班模板");
        mockDutyAttendanceVO.setMajor("网络专业");
        mockDutyAttendanceVO.setStartTime("09:00:00");
        mockDutyAttendanceVO.setEndTime("18:00:00");
        mockDutyAttendanceVO.setExpectedCount(2);
        mockDutyAttendanceVO.setActualCount(2);
        mockDutyAttendanceVO.setModefier("admin");
        mockDutyAttendanceVO.setUpdateTime("2025-01-07 10:00:00");
        
        // 初始化考勤DTO
        mockDutyAttendanceDTO = new DutyAttendanceDTO();
        mockDutyAttendanceDTO.setId(1L);
        mockDutyAttendanceDTO.setDutyDate("2025-01-07");
        mockDutyAttendanceDTO.setTeamTemplateName("一组值班模板");
        mockDutyAttendanceDTO.setMajor("网络专业");
        mockDutyAttendanceDTO.setStartTime("09:00:00");
        mockDutyAttendanceDTO.setEndTime("18:00:00");
        mockDutyAttendanceDTO.setExpectedCount(2);
        mockDutyAttendanceDTO.setActualCount(2);
        mockDutyAttendanceDTO.setModefier("admin");
        mockDutyAttendanceDTO.setUpdateTime("2025-01-07 10:00:00");
    }
    
    /**
     * 测试获取当天专业值班信息 - 成功情况
     */
    @Test
    void testGetTodayMajorDutySuccess() throws Exception {
        List<MajorDutyVO> mockDutyList = Arrays.asList(mockMajorDutyVO);
        
        // 模拟服务层返回值班信息
        when(dutyManagementService.getTodayMajorDuty()).thenReturn(mockDutyList);
        
        // 执行测试
        mockMvc.perform(get("/dutyManagement/duties/today/major")).andExpect(status().isOk())
                .andExpect(jsonPath("$[0].dutyTemplateName").value("一组值班模板"))
                .andExpect(jsonPath("$[0].dutyLeader").value("李四"))
                .andExpect(jsonPath("$[0].dutyLeaderPhone").value("13900139000"))
                .andExpect(jsonPath("$[0].majorDutyPersonVOList[0].majorName").value("网络专业"))
                .andExpect(jsonPath("$[0].majorDutyPersonVOList[0].dutyPerson").value("张三"))
                .andExpect(jsonPath("$[0].majorDutyPersonVOList[0].dutyPersonPhone").value("13800138000"))
                .andExpect(jsonPath("$[0].majorDutyPersonVOList[0].expectedDutyCount").value(2));
        
        // 验证服务层方法被调用
        verify(dutyManagementService, times(1)).getTodayMajorDuty();
    }
    
    /**
     * 测试获取当天专业值班信息 - 空列表情况
     */
    @Test
    void testGetTodayMajorDutyEmptyList() throws Exception {
        // 模拟服务层返回空列表
        when(dutyManagementService.getTodayMajorDuty()).thenReturn(Arrays.asList());
        
        // 执行测试
        mockMvc.perform(get("/dutyManagement/duties/today/major")).andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray()).andExpect(jsonPath("$").isEmpty());
        
        // 验证服务层方法被调用
        verify(dutyManagementService, times(1)).getTodayMajorDuty();
    }
    
    /**
     * 测试查询通告 - 成功情况
     */
    @Test
    void testGetNoticeSuccess() throws Exception {
        String mockNotice = "今日值班通告：请各专业值班人员按时到岗。";
        
        // 模拟服务层返回通告内容
        when(dutyManagementService.getNotice()).thenReturn(mockNotice);
        
        // 执行测试
        mockMvc.perform(get("/dutyManagement/notice/msg").characterEncoding(StandardCharsets.UTF_8)
                .accept(MediaType.TEXT_PLAIN_VALUE + ";charset=UTF-8")).andExpect(status().isOk())
                .andExpect(content().string(mockNotice));
        
        // 验证服务层方法被调用
        verify(dutyManagementService, times(1)).getNotice();
    }
    
    /**
     * 测试查询通告 - 空内容情况
     */
    @Test
    void testGetNoticeEmptyContent() throws Exception {
        // 模拟服务层返回空内容
        when(dutyManagementService.getNotice()).thenReturn("");
        
        // 执行测试
        mockMvc.perform(get("/dutyManagement/notice/msg")).andExpect(status().isOk()).andExpect(content().string(""));
        
        // 验证服务层方法被调用
        verify(dutyManagementService, times(1)).getNotice();
    }
    
    /**
     * 测试保存新通告 - 成功情况
     */
    @Test
    void testSaveNoticeSuccess() throws Exception {
        Map<String, String> noticeParam = new HashMap<>();
        noticeParam.put("content", "新的值班通告内容");
        
        // 模拟服务层保存操作（void方法）
        doNothing().when(dutyManagementService).saveNotice(anyString());
        
        // 执行测试
        mockMvc.perform(post("/dutyManagement/save/notice").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(noticeParam))).andExpect(status().isOk());
        
        // 验证服务层方法被调用
        verify(dutyManagementService, times(1)).saveNotice("新的值班通告内容");
    }
    
    /**
     * 测试保存新通告 - 空内容情况
     */
    @Test
    void testSaveNoticeEmptyContent() throws Exception {
        Map<String, String> noticeParam = new HashMap<>();
        noticeParam.put("content", "");
        
        // 模拟服务层保存操作（void方法）
        doNothing().when(dutyManagementService).saveNotice(anyString());
        
        // 执行测试
        mockMvc.perform(post("/dutyManagement/save/notice").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(noticeParam))).andExpect(status().isOk());
        
        // 验证服务层方法被调用
        verify(dutyManagementService, times(1)).saveNotice("");
    }
    
    /**
     * 测试查询考勤人数 - 成功情况
     */
    @Test
    void testGetDutyAttendanceSuccess() throws Exception {
        List<DutyAttendanceVO> mockAttendanceList = Arrays.asList(mockDutyAttendanceVO);
        
        // 模拟服务层返回考勤信息
        when(dutyManagementService.getDutyAttendance()).thenReturn(mockAttendanceList);
        
        // 执行测试
        mockMvc.perform(get("/dutyManagement/get/duty/attendance")).andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id").value(1)).andExpect(jsonPath("$[0].dutyDate").value("2025-01-07"))
                .andExpect(jsonPath("$[0].teamTemplateName").value("一组值班模板"))
                .andExpect(jsonPath("$[0].major").value("网络专业")).andExpect(jsonPath("$[0].expectedCount").value(2))
                .andExpect(jsonPath("$[0].actualCount").value(2));
        
        // 验证服务层方法被调用
        verify(dutyManagementService, times(1)).getDutyAttendance();
    }
    
    /**
     * 测试查询考勤人数 - 空列表情况
     */
    @Test
    void testGetDutyAttendanceEmptyList() throws Exception {
        // 模拟服务层返回空列表
        when(dutyManagementService.getDutyAttendance()).thenReturn(Arrays.asList());
        
        // 执行测试
        mockMvc.perform(get("/dutyManagement/get/duty/attendance")).andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray()).andExpect(jsonPath("$").isEmpty());
        
        // 验证服务层方法被调用
        verify(dutyManagementService, times(1)).getDutyAttendance();
    }
    
    /**
     * 测试修改考勤人数 - 成功情况
     */
    @Test
    void testSaveDutyAttendanceSuccess() throws Exception {
        // 模拟服务层保存操作（void方法）
        doNothing().when(dutyManagementService).saveDutyAttendance(any(DutyAttendanceDTO.class));
        
        // 执行测试
        mockMvc.perform(put("/dutyManagement/save/duty/attendance").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockDutyAttendanceDTO))).andExpect(status().isOk());
        
        // 验证服务层方法被调用
        verify(dutyManagementService, times(1)).saveDutyAttendance(any(DutyAttendanceDTO.class));
    }
    
    /**
     * 测试修改考勤人数 - 验证失败情况（ID为空）
     */
    @Test
    void testSaveDutyAttendanceValidationFailure() throws Exception {
        // 创建无效的DTO（ID为空）
        DutyAttendanceDTO invalidDTO = new DutyAttendanceDTO();
        invalidDTO.setId(null);
        invalidDTO.setDutyDate("2025-01-07");
        invalidDTO.setTeamTemplateName("一组值班模板");
        invalidDTO.setMajor("网络专业");
        invalidDTO.setExpectedCount(2);
        invalidDTO.setActualCount(2);
        
        // 执行测试，期望返回400错误（验证失败）
        mockMvc.perform(put("/dutyManagement/save/duty/attendance").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidDTO))).andExpect(status().isBadRequest());
        
        // 验证服务层方法没有被调用
        verify(dutyManagementService, never()).saveDutyAttendance(any(DutyAttendanceDTO.class));
    }
}
