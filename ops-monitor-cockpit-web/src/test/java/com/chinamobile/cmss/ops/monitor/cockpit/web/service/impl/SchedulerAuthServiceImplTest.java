/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.AESGCMBase64KeyInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.SchedulerResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.service.SchedulerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * SchedulerAuthServiceImpl 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class SchedulerAuthServiceImplTest {
    
    @Mock
    private SchedulerService schedulerService;
    
    @InjectMocks
    private SchedulerAuthServiceImpl schedulerAuthService;
    
    private SchedulerResponse<AESGCMBase64KeyInfos> mockKeyResponse;
    
    private AESGCMBase64KeyInfos mockKeyInfos;
    
    @BeforeEach
    void setUp() {
        // 初始化密钥信息
        mockKeyInfos = new AESGCMBase64KeyInfos();
        mockKeyInfos.setInteractAuth("dGVzdC1rZXktMTIzNDU2Nzg5MA==");
        
        // 初始化调度响应
        mockKeyResponse = new SchedulerResponse<>();
        mockKeyResponse.setData(mockKeyInfos);
    }
    
    /**
     * 测试查询AES GCM Base64密钥 - 正常情况
     */
    @Test
    void testQueryAESGCMBase64Key() {
        // 模拟 schedulerService 的 queryAESGCMBase64Key 方法
        when(schedulerService.queryAESGCMBase64Key()).thenReturn(mockKeyResponse);
        
        // 执行测试
        AESGCMBase64KeyInfos result = schedulerAuthService.queryAESGCMBase64Key();
        
        // 验证结果
        assertNotNull(result);
        assertEquals("dGVzdC1rZXktMTIzNDU2Nzg5MA==", result.getInteractAuth());
        
        // 验证调用次数
        verify(schedulerService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询AES GCM Base64密钥 - null数据
     */
    @Test
    void testQueryAESGCMBase64KeyWithNullData() {
        // 创建null数据响应
        SchedulerResponse<AESGCMBase64KeyInfos> nullResponse = new SchedulerResponse<>();
        nullResponse.setData(null);
        
        // 模拟 schedulerService 的 queryAESGCMBase64Key 方法
        when(schedulerService.queryAESGCMBase64Key()).thenReturn(nullResponse);
        
        // 执行测试
        AESGCMBase64KeyInfos result = schedulerAuthService.queryAESGCMBase64Key();
        
        // 验证结果
        assertNull(result);
        
        // 验证调用次数
        verify(schedulerService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询AES GCM Base64密钥 - 空密钥信息
     */
    @Test
    void testQueryAESGCMBase64KeyWithEmptyKeyInfos() {
        // 创建空密钥信息
        AESGCMBase64KeyInfos emptyKeyInfos = new AESGCMBase64KeyInfos();
        emptyKeyInfos.setInteractAuth("");
        
        SchedulerResponse<AESGCMBase64KeyInfos> emptyResponse = new SchedulerResponse<>();
        emptyResponse.setData(emptyKeyInfos);
        
        // 模拟 schedulerService 的 queryAESGCMBase64Key 方法
        when(schedulerService.queryAESGCMBase64Key()).thenReturn(emptyResponse);
        
        // 执行测试
        AESGCMBase64KeyInfos result = schedulerAuthService.queryAESGCMBase64Key();
        
        // 验证结果
        assertNotNull(result);
        assertEquals("", result.getInteractAuth());
        
        // 验证调用次数
        verify(schedulerService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询AES GCM Base64密钥 - null密钥字段
     */
    @Test
    void testQueryAESGCMBase64KeyWithNullKeyFields() {
        // 创建null密钥字段的密钥信息
        AESGCMBase64KeyInfos nullFieldsKeyInfos = new AESGCMBase64KeyInfos();
        nullFieldsKeyInfos.setInteractAuth(null);
        
        SchedulerResponse<AESGCMBase64KeyInfos> nullFieldsResponse = new SchedulerResponse<>();
        nullFieldsResponse.setData(nullFieldsKeyInfos);
        
        // 模拟 schedulerService 的 queryAESGCMBase64Key 方法
        when(schedulerService.queryAESGCMBase64Key()).thenReturn(nullFieldsResponse);
        
        // 执行测试
        AESGCMBase64KeyInfos result = schedulerAuthService.queryAESGCMBase64Key();
        
        // 验证结果
        assertNotNull(result);
        assertNull(result.getInteractAuth());
        
        // 验证调用次数
        verify(schedulerService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询AES GCM Base64密钥 - 长密钥信息
     */
    @Test
    void testQueryAESGCMBase64KeyWithLongKeyInfos() {
        // 创建长密钥信息
        String longKey = "dGVzdC1sb25nLWtleS0xMjM0NTY3ODkwYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXo=";
        
        AESGCMBase64KeyInfos longKeyInfos = new AESGCMBase64KeyInfos();
        longKeyInfos.setInteractAuth(longKey);
        
        SchedulerResponse<AESGCMBase64KeyInfos> longKeyResponse = new SchedulerResponse<>();
        longKeyResponse.setData(longKeyInfos);
        
        // 模拟 schedulerService 的 queryAESGCMBase64Key 方法
        when(schedulerService.queryAESGCMBase64Key()).thenReturn(longKeyResponse);
        
        // 执行测试
        AESGCMBase64KeyInfos result = schedulerAuthService.queryAESGCMBase64Key();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(longKey, result.getInteractAuth());
        
        // 验证调用次数
        verify(schedulerService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询AES GCM Base64密钥 - 特殊字符密钥信息
     */
    @Test
    void testQueryAESGCMBase64KeyWithSpecialCharacters() {
        // 创建包含特殊字符的密钥信息
        String specialKey = "dGVzdC1zcGVjaWFsLWtleS0hQCMkJV4mKigpXys=";
        
        AESGCMBase64KeyInfos specialKeyInfos = new AESGCMBase64KeyInfos();
        specialKeyInfos.setInteractAuth(specialKey);
        
        SchedulerResponse<AESGCMBase64KeyInfos> specialKeyResponse = new SchedulerResponse<>();
        specialKeyResponse.setData(specialKeyInfos);
        
        // 模拟 schedulerService 的 queryAESGCMBase64Key 方法
        when(schedulerService.queryAESGCMBase64Key()).thenReturn(specialKeyResponse);
        
        // 执行测试
        AESGCMBase64KeyInfos result = schedulerAuthService.queryAESGCMBase64Key();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(specialKey, result.getInteractAuth());
        
        // 验证调用次数
        verify(schedulerService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询AES GCM Base64密钥 - 验证服务调用
     */
    @Test
    void testQueryAESGCMBase64KeyServiceCall() {
        // 模拟 schedulerService 的 queryAESGCMBase64Key 方法
        when(schedulerService.queryAESGCMBase64Key()).thenReturn(mockKeyResponse);
        
        // 执行测试
        AESGCMBase64KeyInfos result = schedulerAuthService.queryAESGCMBase64Key();
        
        // 验证结果不为null
        assertNotNull(result);
        
        // 验证服务方法被正确调用
        verify(schedulerService, times(1)).queryAESGCMBase64Key();
        
        // 验证返回的是响应中的数据部分
        assertEquals(mockKeyInfos, result);
    }
    
    /**
     * 测试查询AES GCM Base64密钥 - 多次调用
     */
    @Test
    void testQueryAESGCMBase64KeyMultipleCalls() {
        // 模拟 schedulerService 的 queryAESGCMBase64Key 方法
        when(schedulerService.queryAESGCMBase64Key()).thenReturn(mockKeyResponse);
        
        // 执行多次测试
        AESGCMBase64KeyInfos result1 = schedulerAuthService.queryAESGCMBase64Key();
        AESGCMBase64KeyInfos result2 = schedulerAuthService.queryAESGCMBase64Key();
        AESGCMBase64KeyInfos result3 = schedulerAuthService.queryAESGCMBase64Key();
        
        // 验证结果
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotNull(result3);
        
        assertEquals(mockKeyInfos.getInteractAuth(), result1.getInteractAuth());
        assertEquals(mockKeyInfos.getInteractAuth(), result2.getInteractAuth());
        assertEquals(mockKeyInfos.getInteractAuth(), result3.getInteractAuth());
        
        // 验证调用次数
        verify(schedulerService, times(3)).queryAESGCMBase64Key();
    }
}
