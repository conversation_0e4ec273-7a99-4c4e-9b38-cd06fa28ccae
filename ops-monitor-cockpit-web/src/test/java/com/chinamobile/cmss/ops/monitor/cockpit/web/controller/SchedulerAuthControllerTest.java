/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.AESGCMBase64KeyInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.SchedulerAuthService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * SchedulerAuthController 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class SchedulerAuthControllerTest {
    
    @Mock
    private SchedulerAuthService schedulerAuthService;
    
    @InjectMocks
    private SchedulerAuthController schedulerAuthController;
    
    private MockMvc mockMvc;
    
    private ObjectMapper objectMapper;
    
    private AESGCMBase64KeyInfos mockKeyInfos;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(schedulerAuthController).build();
        objectMapper = new ObjectMapper();
        
        // 初始化密钥信息
        mockKeyInfos = new AESGCMBase64KeyInfos();
        mockKeyInfos.setInteractAuth("dGVzdC1rZXktMTIzNDU2Nzg5MA==");
    }
    
    /**
     * 测试查询调度密钥 - 成功情况
     */
    @Test
    void testQueryAESGCMBase64KeySuccess() throws Exception {
        // 模拟服务层返回密钥信息
        when(schedulerAuthService.queryAESGCMBase64Key()).thenReturn(mockKeyInfos);
        
        // 执行测试
        mockMvc.perform(get("/scheduler/query/auth")).andExpect(status().isOk())
                .andExpect(jsonPath("$.interactAuth").value("dGVzdC1rZXktMTIzNDU2Nzg5MA=="));
        
        // 验证服务层方法被调用
        verify(schedulerAuthService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询调度密钥 - 空密钥情况
     */
    @Test
    void testQueryAESGCMBase64KeyEmptyKey() throws Exception {
        // 创建空密钥信息
        AESGCMBase64KeyInfos emptyKeyInfos = new AESGCMBase64KeyInfos();
        emptyKeyInfos.setInteractAuth("");
        
        // 模拟服务层返回空密钥信息
        when(schedulerAuthService.queryAESGCMBase64Key()).thenReturn(emptyKeyInfos);
        
        // 执行测试
        mockMvc.perform(get("/scheduler/query/auth")).andExpect(status().isOk())
                .andExpect(jsonPath("$.interactAuth").value(""));
        
        // 验证服务层方法被调用
        verify(schedulerAuthService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询调度密钥 - null密钥情况
     */
    @Test
    void testQueryAESGCMBase64KeyNullKey() throws Exception {
        // 创建null密钥信息
        AESGCMBase64KeyInfos nullKeyInfos = new AESGCMBase64KeyInfos();
        nullKeyInfos.setInteractAuth(null);
        
        // 模拟服务层返回null密钥信息
        when(schedulerAuthService.queryAESGCMBase64Key()).thenReturn(nullKeyInfos);
        
        // 执行测试
        mockMvc.perform(get("/scheduler/query/auth")).andExpect(status().isOk())
                .andExpect(jsonPath("$.interactAuth").doesNotExist());
        
        // 验证服务层方法被调用
        verify(schedulerAuthService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询调度密钥 - 长密钥情况
     */
    @Test
    void testQueryAESGCMBase64KeyLongKey() throws Exception {
        // 创建长密钥信息
        String longKey = "dGVzdC1sb25nLWtleS0xMjM0NTY3ODkwYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXo=";
        AESGCMBase64KeyInfos longKeyInfos = new AESGCMBase64KeyInfos();
        longKeyInfos.setInteractAuth(longKey);
        
        // 模拟服务层返回长密钥信息
        when(schedulerAuthService.queryAESGCMBase64Key()).thenReturn(longKeyInfos);
        
        // 执行测试
        mockMvc.perform(get("/scheduler/query/auth")).andExpect(status().isOk())
                .andExpect(jsonPath("$.interactAuth").value(longKey));
        
        // 验证服务层方法被调用
        verify(schedulerAuthService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询调度密钥 - 服务层返回null情况
     */
    @Test
    void testQueryAESGCMBase64KeyServiceReturnsNull() throws Exception {
        // 模拟服务层返回null
        when(schedulerAuthService.queryAESGCMBase64Key()).thenReturn(null);
        
        // 执行测试
        mockMvc.perform(get("/scheduler/query/auth")).andExpect(status().isOk()).andExpect(content().string(""));
        
        // 验证服务层方法被调用
        verify(schedulerAuthService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询调度密钥 - 多次调用情况
     */
    @Test
    void testQueryAESGCMBase64KeyMultipleCalls() throws Exception {
        // 模拟服务层返回密钥信息
        when(schedulerAuthService.queryAESGCMBase64Key()).thenReturn(mockKeyInfos);
        
        // 执行多次测试
        mockMvc.perform(get("/scheduler/query/auth")).andExpect(status().isOk())
                .andExpect(jsonPath("$.interactAuth").value("dGVzdC1rZXktMTIzNDU2Nzg5MA=="));
        
        mockMvc.perform(get("/scheduler/query/auth")).andExpect(status().isOk())
                .andExpect(jsonPath("$.interactAuth").value("dGVzdC1rZXktMTIzNDU2Nzg5MA=="));
        
        mockMvc.perform(get("/scheduler/query/auth")).andExpect(status().isOk())
                .andExpect(jsonPath("$.interactAuth").value("dGVzdC1rZXktMTIzNDU2Nzg5MA=="));
        
        // 验证服务层方法被调用3次
        verify(schedulerAuthService, times(3)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询调度密钥 - 验证HTTP方法
     */
    @Test
    void testQueryAESGCMBase64KeyHttpMethod() throws Exception {
        // 模拟服务层返回密钥信息
        when(schedulerAuthService.queryAESGCMBase64Key()).thenReturn(mockKeyInfos);
        
        // 测试GET方法
        mockMvc.perform(get("/scheduler/query/auth")).andExpect(status().isOk());
        
        // 测试POST方法应该返回405 Method Not Allowed
        mockMvc.perform(post("/scheduler/query/auth")).andExpect(status().isMethodNotAllowed());
        
        // 测试PUT方法应该返回405 Method Not Allowed
        mockMvc.perform(put("/scheduler/query/auth")).andExpect(status().isMethodNotAllowed());
        
        // 测试DELETE方法应该返回405 Method Not Allowed
        mockMvc.perform(delete("/scheduler/query/auth")).andExpect(status().isMethodNotAllowed());
        
        // 验证服务层方法只被GET请求调用了1次
        verify(schedulerAuthService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询调度密钥 - 验证响应内容类型
     */
    @Test
    void testQueryAESGCMBase64KeyContentType() throws Exception {
        // 模拟服务层返回密钥信息
        when(schedulerAuthService.queryAESGCMBase64Key()).thenReturn(mockKeyInfos);
        
        // 执行测试并验证响应内容类型
        mockMvc.perform(get("/scheduler/query/auth")).andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.interactAuth").value("dGVzdC1rZXktMTIzNDU2Nzg5MA=="));
        
        // 验证服务层方法被调用
        verify(schedulerAuthService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询调度密钥 - 验证请求路径
     */
    @Test
    void testQueryAESGCMBase64KeyRequestPath() throws Exception {
        // 模拟服务层返回密钥信息
        when(schedulerAuthService.queryAESGCMBase64Key()).thenReturn(mockKeyInfos);
        
        // 测试正确的路径
        mockMvc.perform(get("/scheduler/query/auth")).andExpect(status().isOk());
        
        // 测试错误的路径应该返回404
        mockMvc.perform(get("/scheduler/query/auth/wrong")).andExpect(status().isNotFound());
        
        mockMvc.perform(get("/scheduler/auth")).andExpect(status().isNotFound());
        
        mockMvc.perform(get("/query/auth")).andExpect(status().isNotFound());
        
        // 验证服务层方法只被正确路径调用了1次
        verify(schedulerAuthService, times(1)).queryAESGCMBase64Key();
    }
    
    /**
     * 测试查询调度密钥 - 特殊字符密钥情况
     */
    @Test
    void testQueryAESGCMBase64KeySpecialCharacterKey() throws Exception {
        // 创建包含特殊字符的密钥信息
        String specialKey = "dGVzdC1zcGVjaWFsLWtleS0rLz0=";
        AESGCMBase64KeyInfos specialKeyInfos = new AESGCMBase64KeyInfos();
        specialKeyInfos.setInteractAuth(specialKey);
        
        // 模拟服务层返回特殊字符密钥信息
        when(schedulerAuthService.queryAESGCMBase64Key()).thenReturn(specialKeyInfos);
        
        // 执行测试
        mockMvc.perform(get("/scheduler/query/auth")).andExpect(status().isOk())
                .andExpect(jsonPath("$.interactAuth").value(specialKey));
        
        // 验证服务层方法被调用
        verify(schedulerAuthService, times(1)).queryAESGCMBase64Key();
    }
}
