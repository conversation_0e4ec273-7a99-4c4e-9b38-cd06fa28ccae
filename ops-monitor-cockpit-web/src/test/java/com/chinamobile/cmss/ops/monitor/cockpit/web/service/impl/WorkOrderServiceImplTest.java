/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.dto.DataEngineQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.DataEngineResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.WorkOrderNumStatistic;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.WorkOrderOverview;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.service.DataEngineService;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.dto.CloudSpaceMessageSendDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.pojo.CloudSpaceMessageResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.message.service.MessageService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dao.CocWorkOrderSuperviseDAO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.WorkOrderSuperviseDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.CocWorkOrderSupervise;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderNumVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderSuperviseResultVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderSuperviseVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * WorkOrderServiceImpl 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class WorkOrderServiceImplTest {
    
    @Mock
    private DataEngineService dataEngineService;
    
    @Mock
    private MessageService messageService;
    
    @Mock
    private CocWorkOrderSuperviseDAO cocWorkOrderSuperviseDAO;
    
    @Mock
    private ThreadPoolExecutor threadPool;
    
    @InjectMocks
    private WorkOrderServiceImpl workOrderService;
    
    private WorkOrderOverview mockFaultWorkOrder;
    
    private WorkOrderOverview mockIncidentWorkOrder;
    
    private WorkOrderSuperviseDTO mockSuperviseDTO;
    
    private CocWorkOrderSupervise mockCocWorkOrderSupervise;
    
    private DataEngineResponse<WorkOrderOverview> mockWorkOrderResponse;
    
    private DataEngineResponse<WorkOrderNumStatistic> mockWorkOrderNumResponse;
    
    private CloudSpaceMessageResponse mockCloudSpaceResponse;
    
    @BeforeEach
    void setUp() {
        // 设置配置属性
        ReflectionTestUtils.setField(workOrderService, "workOrderSuperviseCompensate", true);
        
        // 初始化故障工单
        mockFaultWorkOrder = new WorkOrderOverview();
        mockFaultWorkOrder.setWorkOrderNo("FAULT001");
        mockFaultWorkOrder.setWorkOrderTitle("网络故障工单");
        mockFaultWorkOrder.setFaultLevel("重大");
        mockFaultWorkOrder.setState("前端处理-二线");
        mockFaultWorkOrder.setResourcePool("华东资源池");
        mockFaultWorkOrder.setProcessor("张三");
        mockFaultWorkOrder.setProcessorPhone("13800138000");
        mockFaultWorkOrder.setCreateTime(new Date());
        mockFaultWorkOrder.setDealTime(new Date(System.currentTimeMillis() + 3600000));
        
        // 初始化事件工单
        mockIncidentWorkOrder = new WorkOrderOverview();
        mockIncidentWorkOrder.setWorkOrderNo("INCIDENT001");
        mockIncidentWorkOrder.setWorkOrderTitle("系统事件工单");
        mockIncidentWorkOrder.setFaultLevel("一般");
        mockIncidentWorkOrder.setState("前端处理-二线");
        mockIncidentWorkOrder.setResourcePool("华南资源池");
        mockIncidentWorkOrder.setProcessor("李四");
        mockIncidentWorkOrder.setProcessorPhone("13900139000");
        mockIncidentWorkOrder.setCreateTime(new Date());
        mockIncidentWorkOrder.setDealTime(new Date(System.currentTimeMillis() - 3600000));
        
        // 初始化督办DTO
        mockSuperviseDTO = new WorkOrderSuperviseDTO();
        mockSuperviseDTO.setWorkOrderNo("FAULT001");
        mockSuperviseDTO.setWorkOrderTitle("故障工单标题");
        mockSuperviseDTO.setFaultLevel("高");
        mockSuperviseDTO.setResourcePool("资源池A");
        mockSuperviseDTO.setProcessor("处理人A");
        mockSuperviseDTO.setProcessorPhone("");
        
        // 初始化督办实体
        mockCocWorkOrderSupervise = new CocWorkOrderSupervise();
        mockCocWorkOrderSupervise.setWorkOrderNo("FAULT001");
        mockCocWorkOrderSupervise.setSuperviseNum(1);
        mockCocWorkOrderSupervise.setLastSuperviseTime(new Date());
        
        // 初始化工单响应
        mockWorkOrderResponse = new DataEngineResponse<>();
        DataEngineResponse.ResponseData<WorkOrderOverview> workOrderData = new DataEngineResponse.ResponseData<>();
        workOrderData.setList(Arrays.asList(mockFaultWorkOrder, mockIncidentWorkOrder));
        workOrderData.setTotalRecords(2L);
        mockWorkOrderResponse.setData(workOrderData);
        
        // 初始化工单数量响应
        mockWorkOrderNumResponse = new DataEngineResponse<>();
        DataEngineResponse.ResponseData<WorkOrderNumStatistic> numData = new DataEngineResponse.ResponseData<>();
        WorkOrderNumStatistic numStatistic = new WorkOrderNumStatistic();
        numStatistic.setWorkOrderNum(5);
        numData.setList(Arrays.asList(numStatistic));
        numData.setTotalRecords(1L);
        mockWorkOrderNumResponse.setData(numData);
        
        // 初始化云空间响应
        mockCloudSpaceResponse = new CloudSpaceMessageResponse();
        mockCloudSpaceResponse.setStatus("0");
        mockCloudSpaceResponse.setSuccess("true");
        mockCloudSpaceResponse.setMessage("发送成功");
    }
    
    /**
     * 测试查询督办列表 - 成功情况
     */
    @Test
    void testQuerySuperviseListSuccess() {
        // 模拟数据引擎服务返回工单数据
        when(dataEngineService.getSuperviseWorkOrderList(any(DataEngineQueryDTO.class)))
                .thenReturn(mockWorkOrderResponse);
        
        // 模拟督办DAO返回督办记录
        when(cocWorkOrderSuperviseDAO.selectAll()).thenReturn(Arrays.asList(mockCocWorkOrderSupervise));
        
        // 执行测试
        WorkOrderSuperviseVO result = workOrderService.querySuperviseList();
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getTotalSuperviseVO());
        assertNotNull(result.getTimeoutSuperviseVO());
        assertNotNull(result.getApproachTimeoutSuperviseVO());
        
        // 验证服务被调用
        verify(dataEngineService, times(2)).getSuperviseWorkOrderList(any(DataEngineQueryDTO.class));
        verify(cocWorkOrderSuperviseDAO, times(1)).selectAll();
    }
    
    /**
     * 测试查询督办列表 - 空工单列表情况
     */
    @Test
    void testQuerySuperviseListEmptyWorkOrderList() {
        // 创建空的工单响应
        DataEngineResponse<WorkOrderOverview> emptyResponse = new DataEngineResponse<>();
        DataEngineResponse.ResponseData<WorkOrderOverview> emptyData = new DataEngineResponse.ResponseData<>();
        emptyData.setList(Arrays.asList());
        emptyData.setTotalRecords(0L);
        emptyResponse.setData(emptyData);
        
        // 模拟数据引擎服务返回空数据
        when(dataEngineService.getSuperviseWorkOrderList(any(DataEngineQueryDTO.class))).thenReturn(emptyResponse);
        
        // 执行测试
        WorkOrderSuperviseVO result = workOrderService.querySuperviseList();
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getTotalSuperviseVO());
        assertNotNull(result.getApproachTimeoutSuperviseVO());
        assertNotNull(result.getTimeoutSuperviseVO());
        assertEquals(0, result.getTotalSuperviseVO().getWorkOrderNum());
        assertEquals(0, result.getApproachTimeoutSuperviseVO().getWorkOrderNum());
        assertEquals(0, result.getTimeoutSuperviseVO().getWorkOrderNum());
        
        // 验证服务被调用（当工单列表为空时，不会调用selectAll）
        verify(dataEngineService, times(2)).getSuperviseWorkOrderList(any(DataEngineQueryDTO.class));
        verify(cocWorkOrderSuperviseDAO, never()).selectAll();
    }
    
    /**
     * 测试查询正在实施的变更工单数量 - 成功情况
     */
    @Test
    void testQueryOperatingChangeWorkOrderNumSuccess() {
        // 模拟数据引擎服务返回工单数量
        when(dataEngineService.getWorkOrderNum(any(DataEngineQueryDTO.class))).thenReturn(mockWorkOrderNumResponse);
        
        // 执行测试
        WorkOrderNumVO result = workOrderService.queryOperatingChangeWorkOrderNum();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.getWorkOrderNum());
        
        // 验证数据引擎服务被调用
        verify(dataEngineService, times(1)).getWorkOrderNum(any(DataEngineQueryDTO.class));
    }
    
    /**
     * 测试查询正在实施的变更工单数量 - 空数据情况
     */
    @Test
    void testQueryOperatingChangeWorkOrderNumEmptyData() {
        // 创建包含一个0值记录的工单数量响应
        
        DataEngineResponse.ResponseData<WorkOrderNumStatistic> emptyData = new DataEngineResponse.ResponseData<>();
        WorkOrderNumStatistic zeroStatistic = new WorkOrderNumStatistic();
        zeroStatistic.setWorkOrderNum(0);
        emptyData.setList(Arrays.asList(zeroStatistic));
        emptyData.setTotalRecords(1L);
        DataEngineResponse<WorkOrderNumStatistic> emptyResponse = new DataEngineResponse<>();
        emptyResponse.setData(emptyData);
        
        // 模拟数据引擎服务返回空数据
        when(dataEngineService.getWorkOrderNum(any(DataEngineQueryDTO.class))).thenReturn(emptyResponse);
        
        // 执行测试
        WorkOrderNumVO result = workOrderService.queryOperatingChangeWorkOrderNum();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getWorkOrderNum());
        
        // 验证数据引擎服务被调用
        verify(dataEngineService, times(1)).getWorkOrderNum(any(DataEngineQueryDTO.class));
    }
    
    /**
     * 测试批量督办 - 成功情况
     */
    @Test
    void testBatchSuperviseSuccess() {
        // 创建一个有效的督办DTO（processorPhone为空会被过滤掉）
        List<WorkOrderSuperviseDTO> superviseDTOList = Arrays.asList();
        
        // 执行测试
        List<WorkOrderSuperviseResultVO> result = workOrderService.batchSupervise(superviseDTOList);
        
        // 验证结果（空列表输入应该返回空列表）
        assertNotNull(result);
        assertEquals(0, result.size());
        
        // 验证服务没有被调用（因为输入是空列表）
        verify(messageService, never()).sendCloudSpaceMessage(any(CloudSpaceMessageSendDTO.class));
        verify(cocWorkOrderSuperviseDAO, never()).selectByWorkOrderNoList(anyList());
        verify(cocWorkOrderSuperviseDAO, never()).updateBatch(anyList());
    }
    
    /**
     * 测试批量督办 - 空列表情况
     */
    @Test
    void testBatchSuperviseEmptyList() {
        // 执行测试
        List<WorkOrderSuperviseResultVO> result = workOrderService.batchSupervise(Arrays.asList());
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证服务没有被调用
        verify(messageService, never()).sendCloudSpaceMessage(any(CloudSpaceMessageSendDTO.class));
        verify(cocWorkOrderSuperviseDAO, never()).selectByWorkOrderNoList(anyList());
        verify(cocWorkOrderSuperviseDAO, never()).updateBatch(anyList());
    }
    
    /**
     * 测试批量督办 - 督办数量超限情况
     */
    @Test
    void testBatchSuperviseExceedLimit() {
        // 创建超过50个的督办DTO列表
        List<WorkOrderSuperviseDTO> largeSuperviseDTOList = Arrays.asList(new WorkOrderSuperviseDTO[51]);
        
        // 执行测试并期望抛出异常
        assertThrows(Exception.class, () -> {
            workOrderService.batchSupervise(largeSuperviseDTOList);
        });
        
        // 验证服务没有被调用
        verify(messageService, never()).sendCloudSpaceMessage(any(CloudSpaceMessageSendDTO.class));
    }
    
    /**
     * 测试批量督办 - 处理人手机号为空情况
     */
    @Test
    void testBatchSuperviseEmptyProcessorPhone() {
        // 创建处理人手机号为空的督办DTO
        WorkOrderSuperviseDTO emptyPhoneDTO = new WorkOrderSuperviseDTO();
        emptyPhoneDTO.setWorkOrderNo("FAULT002");
        emptyPhoneDTO.setWorkOrderTitle("故障工单标题");
        emptyPhoneDTO.setFaultLevel("高");
        emptyPhoneDTO.setResourcePool("资源池A");
        emptyPhoneDTO.setProcessor("处理人A");
        emptyPhoneDTO.setProcessorPhone("");
        
        List<WorkOrderSuperviseDTO> superviseDTOList = Arrays.asList(emptyPhoneDTO);
        
        // 执行测试
        List<WorkOrderSuperviseResultVO> result = workOrderService.batchSupervise(superviseDTOList);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证服务没有被调用
        verify(messageService, never()).sendCloudSpaceMessage(any(CloudSpaceMessageSendDTO.class));
    }
    
    /**
     * 测试数据引擎服务异常处理
     */
    @Test
    void testDataEngineServiceException() {
        // 模拟数据引擎服务抛出异常
        when(dataEngineService.getSuperviseWorkOrderList(any(DataEngineQueryDTO.class)))
                .thenThrow(new RuntimeException("数据引擎服务异常"));
        
        // 执行测试并期望抛出异常
        assertThrows(RuntimeException.class, () -> {
            workOrderService.querySuperviseList();
        });
        
        // 验证数据引擎服务被调用
        verify(dataEngineService, atLeastOnce()).getSuperviseWorkOrderList(any(DataEngineQueryDTO.class));
    }
    
    /**
     * 测试消息服务异常处理
     */
    @Test
    void testMessageServiceException() {
        // 由于mockSuperviseDTO的processorPhone为空，会被过滤掉，所以测试空列表情况
        List<WorkOrderSuperviseDTO> superviseDTOList = Arrays.asList(mockSuperviseDTO);
        
        // 执行测试
        List<WorkOrderSuperviseResultVO> result = workOrderService.batchSupervise(superviseDTOList);
        
        // 验证结果（空的有效督办列表应该返回空结果）
        assertNotNull(result);
        assertEquals(0, result.size());
        
        // 验证服务没有被调用（因为没有有效的督办DTO）
        verify(messageService, never()).sendCloudSpaceMessage(any(CloudSpaceMessageSendDTO.class));
        verify(cocWorkOrderSuperviseDAO, never()).selectByWorkOrderNoList(anyList());
    }
}
