/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.service.AlarmService;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.DateDutySelectByDayRangeResponseInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.DutyTemplateInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.SchedulerResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.service.SchedulerService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.DutyAttendanceDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.mapper.DutyManagementMapper;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.DutyAttendanceVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.MajorDutyVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.NoticeVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * DutyManagementServiceImpl 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class DutyManagementServiceImplTest {
    
    @Mock
    private SchedulerService schedulerService;
    
    @Mock
    private DutyManagementMapper managementMapper;
    
    @Mock
    private AlarmService alarmService;
    
    @Mock
    private DutyManagementMapper dutyManagementMapper;
    
    @InjectMocks
    private DutyManagementServiceImpl dutyManagementService;
    
    private SchedulerResponse<List<DutyTemplateInfos>> mockTemplateResponse;
    
    private DutyTemplateInfos mockTemplateInfo;
    
    private NoticeVO mockNoticeVO;
    
    private DutyAttendanceVO mockAttendanceVO;
    
    private DutyAttendanceDTO mockAttendanceDTO;
    
    @BeforeEach
    void setUp() {
        // 设置配置属性
        List<String> dutyTemplateList = Arrays.asList("template1", "template2");
        ReflectionTestUtils.setField(dutyManagementService, "dutyTemplateList", dutyTemplateList);
        
        // 初始化值班模板信息
        mockTemplateInfo = DutyTemplateInfos.builder().id("template1").name("一组值班模板").versionCode(1).disable(0).build();
        
        // 初始化调度响应
        mockTemplateResponse = new SchedulerResponse<>();
        mockTemplateResponse.setData(Arrays.asList(mockTemplateInfo));
        
        // 初始化通知VO
        mockNoticeVO = new NoticeVO();
        mockNoticeVO.setId(1);
        mockNoticeVO.setContent("今日值班通知：请各位值班人员按时到岗");
        
        // 初始化考勤VO
        mockAttendanceVO = new DutyAttendanceVO();
        mockAttendanceVO.setTeamTemplateName("一组值班模板");
        mockAttendanceVO.setMajor("网络专业");
        mockAttendanceVO.setExpectedCount(3);
        mockAttendanceVO.setActualCount(2);
        mockAttendanceVO.setStartTime("08:00:00");
        mockAttendanceVO.setEndTime("20:00:00");
        
        // 初始化考勤DTO
        mockAttendanceDTO = new DutyAttendanceDTO();
        mockAttendanceDTO.setTeamTemplateName("一组值班模板");
        mockAttendanceDTO.setMajor("网络专业");
        mockAttendanceDTO.setActualCount(3);
        mockAttendanceDTO.setModefier("testuser");
    }
    
    /**
     * 测试获取当天专业值班信息 - 成功情况
     */
    @Test
    void testGetTodayMajorDutySuccess() {
        // 模拟调度服务返回值班模板列表
        when(schedulerService.querySchedulingTemplateList(anyInt(), isNull(), isNull()))
                .thenReturn(mockTemplateResponse);
        
        // 模拟selectByDayRange方法返回空响应，避免NullPointerException
        SchedulerResponse<DateDutySelectByDayRangeResponseInfos> emptyResponse = new SchedulerResponse<>();
        emptyResponse.setData(new DateDutySelectByDayRangeResponseInfos());
        when(schedulerService.selectByDayRange(any())).thenReturn(emptyResponse);
        
        // 执行测试
        List<MajorDutyVO> result = dutyManagementService.getTodayMajorDuty();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证调度服务被调用
        verify(schedulerService, times(1)).querySchedulingTemplateList(0, null, null);
    }
    
    /**
     * 测试获取当天专业值班信息 - 空模板列表情况
     */
    @Test
    void testGetTodayMajorDutyEmptyTemplateList() {
        // 创建空的调度响应
        SchedulerResponse<List<DutyTemplateInfos>> emptyResponse = new SchedulerResponse<>();
        emptyResponse.setData(Arrays.asList());
        
        // 模拟调度服务返回空列表
        when(schedulerService.querySchedulingTemplateList(anyInt(), isNull(), isNull())).thenReturn(emptyResponse);
        
        // 执行测试
        List<MajorDutyVO> result = dutyManagementService.getTodayMajorDuty();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证调度服务被调用
        verify(schedulerService, times(1)).querySchedulingTemplateList(0, null, null);
    }
    
    /**
     * 测试获取当天专业值班信息 - null数据情况
     */
    @Test
    void testGetTodayMajorDutyNullData() {
        // 创建null数据的调度响应
        SchedulerResponse<List<DutyTemplateInfos>> nullResponse = new SchedulerResponse<>();
        nullResponse.setData(null);
        
        // 模拟调度服务返回null数据
        when(schedulerService.querySchedulingTemplateList(anyInt(), isNull(), isNull())).thenReturn(nullResponse);
        
        // 执行测试
        List<MajorDutyVO> result = dutyManagementService.getTodayMajorDuty();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证调度服务被调用
        verify(schedulerService, times(1)).querySchedulingTemplateList(0, null, null);
    }
    
    /**
     * 测试获取公告 - 成功情况
     */
    @Test
    void testGetNoticeSuccess() {
        // 模拟mapper返回公告列表
        when(managementMapper.findNotice()).thenReturn(Arrays.asList(mockNoticeVO));
        
        // 执行测试
        String result = dutyManagementService.getNotice();
        
        // 验证结果
        assertNotNull(result);
        assertEquals("今日值班通知：请各位值班人员按时到岗", result);
        
        // 验证mapper被调用
        verify(managementMapper, times(1)).findNotice();
    }
    
    /**
     * 测试获取公告 - 空列表情况
     */
    @Test
    void testGetNoticeEmptyList() {
        // 模拟mapper返回空列表
        when(managementMapper.findNotice()).thenReturn(Arrays.asList());
        
        // 模拟告警服务返回
        AlarmResponse<Map<String, Integer[]>> mockAlarmResponse = new AlarmResponse<>();
        AlarmResponse.Meta meta = new AlarmResponse.Meta();
        meta.setCode("20000");
        mockAlarmResponse.setMeta(meta);
        
        Map<String, Integer[]> alarmData = new HashMap<>();
        alarmData.put("alarm_level_count", new Integer[]{5, 3});
        mockAlarmResponse.setData(alarmData);
        
        when(alarmService.getAlarmNum(any())).thenReturn(mockAlarmResponse);
        
        // 执行测试
        String result = dutyManagementService.getNotice();
        
        // 验证结果
        assertNotNull(result);
        assertEquals("近24小时内未清除的重大重要告警总数：8", result);
        
        // 验证mapper被调用
        verify(managementMapper, times(1)).findNotice();
        verify(alarmService, times(1)).getAlarmNum(any());
    }
    
    /**
     * 测试获取公告 - null情况
     */
    @Test
    void testGetNoticeNullList() {
        // 模拟mapper返回null
        when(managementMapper.findNotice()).thenReturn(null);
        
        // 模拟告警服务返回
        AlarmResponse<Map<String, Integer[]>> mockAlarmResponse = new AlarmResponse<>();
        AlarmResponse.Meta meta = new AlarmResponse.Meta();
        meta.setCode("20000");
        mockAlarmResponse.setMeta(meta);
        
        Map<String, Integer[]> alarmData = new HashMap<>();
        alarmData.put("alarm_level_count", new Integer[]{2, 1});
        mockAlarmResponse.setData(alarmData);
        
        when(alarmService.getAlarmNum(any())).thenReturn(mockAlarmResponse);
        
        // 执行测试
        String result = dutyManagementService.getNotice();
        
        // 验证结果
        assertNotNull(result);
        assertEquals("近24小时内未清除的重大重要告警总数：3", result);
        
        // 验证mapper被调用
        verify(managementMapper, times(1)).findNotice();
        verify(alarmService, times(1)).getAlarmNum(any());
    }
    
    /**
     * 测试保存通告 - 更新已存在的通告
     */
    @Test
    void testSaveNoticeUpdateExisting() {
        String newContent = "新的值班通知内容";
        
        // 模拟mapper返回已存在的公告
        when(managementMapper.findNotice()).thenReturn(Arrays.asList(mockNoticeVO));
        
        // 执行测试
        dutyManagementService.saveNotice(newContent);
        
        // 验证mapper方法被调用
        verify(managementMapper, times(1)).findNotice();
        verify(managementMapper, times(1)).updateNotice(newContent);
        verify(managementMapper, never()).insertNotice(any(NoticeVO.class));
    }
    
    /**
     * 测试保存通告 - 插入新通告
     */
    @Test
    void testSaveNoticeInsertNew() {
        String newContent = "新的值班通知内容";
        
        // 模拟mapper返回空列表（不存在公告）
        when(managementMapper.findNotice()).thenReturn(Arrays.asList());
        
        // 执行测试
        dutyManagementService.saveNotice(newContent);
        
        // 验证mapper方法被调用
        verify(managementMapper, times(1)).findNotice();
        verify(managementMapper, never()).updateNotice(anyString());
        verify(managementMapper, times(1)).insertNotice(any(NoticeVO.class));
    }
    
    /**
     * 测试查询考勤人数 - 成功情况
     */
    @Test
    void testGetDutyAttendanceSuccess() {
        // 模拟mapper返回考勤列表
        when(dutyManagementMapper.findDutyAttendance(anyString(), anyString()))
                .thenReturn(Arrays.asList(mockAttendanceVO));
        
        // 执行测试
        List<DutyAttendanceVO> result = dutyManagementService.getDutyAttendance();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("一组值班模板", result.get(0).getTeamTemplateName());
        assertEquals("网络专业", result.get(0).getMajor());
        assertEquals(3, result.get(0).getExpectedCount());
        assertEquals(2, result.get(0).getActualCount());
        
        // 验证mapper被调用
        verify(dutyManagementMapper, times(1)).findDutyAttendance(anyString(), anyString());
    }
    
    /**
     * 测试查询考勤人数 - 空列表情况
     */
    @Test
    void testGetDutyAttendanceEmptyList() {
        // 模拟mapper返回空列表
        when(dutyManagementMapper.findDutyAttendance(anyString(), anyString())).thenReturn(Arrays.asList());
        
        // 执行测试
        List<DutyAttendanceVO> result = dutyManagementService.getDutyAttendance();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证mapper被调用
        verify(dutyManagementMapper, times(1)).findDutyAttendance(anyString(), anyString());
    }
    
    /**
     * 测试修改考勤人数 - 成功情况
     */
    @Test
    void testSaveDutyAttendanceSuccess() {
        // 执行测试
        dutyManagementService.saveDutyAttendance(mockAttendanceDTO);
        
        // 验证mapper被调用
        verify(dutyManagementMapper, times(1)).updateDutyAttendance(mockAttendanceDTO);
        
        // 验证DTO中的修改者被设置（UserUtils.getUserName()在测试环境中返回null，所以修改者会是null）
        // 这是正常的测试环境行为
    }
    
    /**
     * 测试修改考勤人数 - null DTO情况
     */
    @Test
    void testSaveDutyAttendanceNullDTO() {
        // 执行测试，期望抛出NullPointerException
        assertThrows(NullPointerException.class, () -> {
            dutyManagementService.saveDutyAttendance(null);
        });
        
        // 验证mapper没有被调用，因为在设置modifier时就抛出了异常
        verify(dutyManagementMapper, never()).updateDutyAttendance(any());
    }
    
    /**
     * 测试初始化考勤信息表 - 成功情况
     */
    @Test
    void testInitDutyAttendanceSuccess() {
        // 创建测试数据
        MajorDutyVO.MajorDutyPersonVO personVO = new MajorDutyVO.MajorDutyPersonVO("张三", "13800138001", 3, "08:00:00",
                "20:00:00", null);
        personVO.setMajorName("网络专业");
        
        MajorDutyVO majorDutyVO = MajorDutyVO.builder().dutyTemplateName("一组值班模板").dutyLeader("张三")
                .dutyLeaderPhone("13800138000").majorDutyPersonVOList(Arrays.asList(personVO)).build();
        
        List<MajorDutyVO> majorDutyList = Arrays.asList(majorDutyVO);
        
        // 模拟mapper返回空列表（不存在记录）
        when(managementMapper.findDutyTemplateExit(any())).thenReturn(Arrays.asList());
        
        // 执行测试
        dutyManagementService.initDutyAttendance(majorDutyList);
        
        // 验证mapper方法被调用
        verify(managementMapper, times(1)).findDutyTemplateExit(any());
        verify(managementMapper, times(1)).insertDutyTemplate(any());
    }
    
    /**
     * 测试初始化考勤信息表 - 空列表情况
     */
    @Test
    void testInitDutyAttendanceEmptyList() {
        // 执行测试
        dutyManagementService.initDutyAttendance(Arrays.asList());
        
        // 验证mapper方法没有被调用
        verify(managementMapper, never()).findDutyTemplateExit(any());
        verify(managementMapper, never()).insertDutyTemplate(any());
    }
    
    /**
     * 测试初始化考勤信息表 - null列表情况
     */
    @Test
    void testInitDutyAttendanceNullList() {
        // 执行测试
        dutyManagementService.initDutyAttendance(null);
        
        // 验证mapper方法没有被调用
        verify(managementMapper, never()).findDutyTemplateExit(any());
        verify(managementMapper, never()).insertDutyTemplate(any());
    }
}
