/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.web.service.FaultInfluenceService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.FaultLevelAggregateVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * FaultInfluenceController 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class FaultInfluenceControllerTest {
    
    @Mock
    private FaultInfluenceService faultInfluenceService;
    
    @InjectMocks
    private FaultInfluenceController faultInfluenceController;
    
    private MockMvc mockMvc;
    
    private ObjectMapper objectMapper;
    
    private FaultLevelAggregateVO mockFaultLevelAggregateVO;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(faultInfluenceController).build();
        objectMapper = new ObjectMapper();
        
        // 初始化故障等级统计VO
        mockFaultLevelAggregateVO = FaultLevelAggregateVO.builder().generalEvent(15).normalFault(10).seriousFault(8)
                .majorFault(5).criticalFault(2).build();
    }
    
    /**
     * 测试查询驾驶舱的工单系统故障等级统计信息 - 正常情况
     */
    @Test
    void testQueryAggregateFaultLevel() throws Exception {
        // 模拟 faultInfluenceService 的 queryAggregateFaultLevel 方法
        when(faultInfluenceService.queryAggregateFaultLevel()).thenReturn(mockFaultLevelAggregateVO);
        
        // 执行测试
        mockMvc.perform(get("/fault/influence/queryAggregateFaultLevel").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.generalEvent").value(15)).andExpect(jsonPath("$.normalFault").value(10))
                .andExpect(jsonPath("$.seriousFault").value(8)).andExpect(jsonPath("$.majorFault").value(5))
                .andExpect(jsonPath("$.criticalFault").value(2));
        
        // 验证调用次数
        verify(faultInfluenceService, times(1)).queryAggregateFaultLevel();
    }
    
    /**
     * 测试查询驾驶舱的工单系统故障等级统计信息 - 零值情况
     */
    @Test
    void testQueryAggregateFaultLevelWithZeroValues() throws Exception {
        // 创建零值的故障等级统计VO
        FaultLevelAggregateVO zeroFaultLevelVO = FaultLevelAggregateVO.builder().generalEvent(0).normalFault(0)
                .seriousFault(0).majorFault(0).criticalFault(0).build();
        
        // 模拟 faultInfluenceService 的 queryAggregateFaultLevel 方法
        when(faultInfluenceService.queryAggregateFaultLevel()).thenReturn(zeroFaultLevelVO);
        
        // 执行测试
        mockMvc.perform(get("/fault/influence/queryAggregateFaultLevel").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.generalEvent").value(0)).andExpect(jsonPath("$.normalFault").value(0))
                .andExpect(jsonPath("$.seriousFault").value(0)).andExpect(jsonPath("$.majorFault").value(0))
                .andExpect(jsonPath("$.criticalFault").value(0));
        
        // 验证调用次数
        verify(faultInfluenceService, times(1)).queryAggregateFaultLevel();
    }
    
    /**
     * 测试查询驾驶舱的工单系统故障等级统计信息 - 大数值情况
     */
    @Test
    void testQueryAggregateFaultLevelWithLargeValues() throws Exception {
        // 创建大数值的故障等级统计VO
        FaultLevelAggregateVO largeFaultLevelVO = FaultLevelAggregateVO.builder().generalEvent(999999)
                .normalFault(888888).seriousFault(777777).majorFault(666666).criticalFault(555555).build();
        
        // 模拟 faultInfluenceService 的 queryAggregateFaultLevel 方法
        when(faultInfluenceService.queryAggregateFaultLevel()).thenReturn(largeFaultLevelVO);
        
        // 执行测试
        mockMvc.perform(get("/fault/influence/queryAggregateFaultLevel").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.generalEvent").value(999999)).andExpect(jsonPath("$.normalFault").value(888888))
                .andExpect(jsonPath("$.seriousFault").value(777777)).andExpect(jsonPath("$.majorFault").value(666666))
                .andExpect(jsonPath("$.criticalFault").value(555555));
        
        // 验证调用次数
        verify(faultInfluenceService, times(1)).queryAggregateFaultLevel();
    }
    
    /**
     * 测试查询驾驶舱的工单系统故障等级统计信息 - null结果
     */
    @Test
    void testQueryAggregateFaultLevelWithNullResult() throws Exception {
        // 模拟 faultInfluenceService 的 queryAggregateFaultLevel 方法返回null
        when(faultInfluenceService.queryAggregateFaultLevel()).thenReturn(null);
        
        // 执行测试
        mockMvc.perform(get("/fault/influence/queryAggregateFaultLevel").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        
        // 验证调用次数
        verify(faultInfluenceService, times(1)).queryAggregateFaultLevel();
    }
    
    /**
     * 测试查询驾驶舱的工单系统故障等级统计信息 - 负数值情况
     */
    @Test
    void testQueryAggregateFaultLevelWithNegativeValues() throws Exception {
        // 创建负数值的故障等级统计VO
        FaultLevelAggregateVO negativeFaultLevelVO = FaultLevelAggregateVO.builder().generalEvent(-1).normalFault(-2)
                .seriousFault(-3).majorFault(-4).criticalFault(-5).build();
        
        // 模拟 faultInfluenceService 的 queryAggregateFaultLevel 方法
        when(faultInfluenceService.queryAggregateFaultLevel()).thenReturn(negativeFaultLevelVO);
        
        // 执行测试
        mockMvc.perform(get("/fault/influence/queryAggregateFaultLevel").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.generalEvent").value(-1)).andExpect(jsonPath("$.normalFault").value(-2))
                .andExpect(jsonPath("$.seriousFault").value(-3)).andExpect(jsonPath("$.majorFault").value(-4))
                .andExpect(jsonPath("$.criticalFault").value(-5));
        
        // 验证调用次数
        verify(faultInfluenceService, times(1)).queryAggregateFaultLevel();
    }
    
    /**
     * 测试查询驾驶舱的工单系统故障等级统计信息 - 混合数值情况
     */
    @Test
    void testQueryAggregateFaultLevelWithMixedValues() throws Exception {
        // 创建混合数值的故障等级统计VO
        FaultLevelAggregateVO mixedFaultLevelVO = FaultLevelAggregateVO.builder().generalEvent(100).normalFault(0)
                .seriousFault(50).majorFault(1).criticalFault(999).build();
        
        // 模拟 faultInfluenceService 的 queryAggregateFaultLevel 方法
        when(faultInfluenceService.queryAggregateFaultLevel()).thenReturn(mixedFaultLevelVO);
        
        // 执行测试
        mockMvc.perform(get("/fault/influence/queryAggregateFaultLevel").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.generalEvent").value(100)).andExpect(jsonPath("$.normalFault").value(0))
                .andExpect(jsonPath("$.seriousFault").value(50)).andExpect(jsonPath("$.majorFault").value(1))
                .andExpect(jsonPath("$.criticalFault").value(999));
        
        // 验证调用次数
        verify(faultInfluenceService, times(1)).queryAggregateFaultLevel();
    }
    
    /**
     * 测试查询驾驶舱的工单系统故障等级统计信息 - 验证HTTP方法
     */
    @Test
    void testQueryAggregateFaultLevelHttpMethod() throws Exception {
        // 模拟 faultInfluenceService 的 queryAggregateFaultLevel 方法
        when(faultInfluenceService.queryAggregateFaultLevel()).thenReturn(mockFaultLevelAggregateVO);
        
        // 执行测试 - 验证GET方法
        mockMvc.perform(get("/fault/influence/queryAggregateFaultLevel")).andExpect(status().isOk());
        
        // 验证调用次数
        verify(faultInfluenceService, times(1)).queryAggregateFaultLevel();
    }
    
    /**
     * 测试查询驾驶舱的工单系统故障等级统计信息 - 验证响应格式
     */
    @Test
    void testQueryAggregateFaultLevelResponseFormat() throws Exception {
        // 模拟 faultInfluenceService 的 queryAggregateFaultLevel 方法
        when(faultInfluenceService.queryAggregateFaultLevel()).thenReturn(mockFaultLevelAggregateVO);
        
        // 执行测试
        mockMvc.perform(get("/fault/influence/queryAggregateFaultLevel").accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").exists()).andExpect(jsonPath("$.generalEvent").exists())
                .andExpect(jsonPath("$.normalFault").exists()).andExpect(jsonPath("$.seriousFault").exists())
                .andExpect(jsonPath("$.majorFault").exists()).andExpect(jsonPath("$.criticalFault").exists());
        
        // 验证调用次数
        verify(faultInfluenceService, times(1)).queryAggregateFaultLevel();
    }
}
