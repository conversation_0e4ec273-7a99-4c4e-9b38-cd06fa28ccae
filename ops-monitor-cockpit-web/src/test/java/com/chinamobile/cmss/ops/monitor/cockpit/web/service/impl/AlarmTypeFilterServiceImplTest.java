/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ParamInvalidException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.UserUtils;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.dto.AlarmTypeFilterStatisticDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.service.AlarmService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.AlarmTypeFilterDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmTypeFilter;
import com.chinamobile.cmss.ops.monitor.cockpit.web.mapper.AlarmTypeFilterMapper;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmTypeFilterCountVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AlarmTypeFilterServiceImpl 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class AlarmTypeFilterServiceImplTest {
    
    @Mock
    private AlarmTypeFilterMapper alarmTypeFilterMapper;
    
    @Mock
    private AlarmService alarmService;
    
    @InjectMocks
    private AlarmTypeFilterServiceImpl alarmTypeFilterService;
    
    private AlarmTypeFilter mockAlarmTypeFilter;
    
    private List<AlarmTypeFilter> mockAlarmTypeFilterList;
    
    private MockMultipartFile mockAvatarFile;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockAlarmTypeFilter = new AlarmTypeFilter();
        mockAlarmTypeFilter.setId(1L);
        mockAlarmTypeFilter.setFilterName("测试类型过滤器");
        mockAlarmTypeFilter.setFilterId("test-type-filter-id");
        mockAlarmTypeFilter.setEnable((short) 1);
        mockAlarmTypeFilter.setSortOrder(0);
        mockAlarmTypeFilter.setCreateTime(LocalDateTime.now());
        mockAlarmTypeFilter.setCreateBy("testUser");
        mockAlarmTypeFilter.setExtension("png");
        mockAlarmTypeFilter.setPicture(new byte[]{1, 2, 3, 4});
        
        mockAlarmTypeFilterList = new ArrayList<>();
        mockAlarmTypeFilterList.add(mockAlarmTypeFilter);
        
        // 创建模拟的 MultipartFile
        mockAvatarFile = new MockMultipartFile("avatar", "test-avatar.png", "image/png",
                "test image content".getBytes());
    }
    
    /**
     * 测试添加告警类型过滤器
     */
    @Test
    void testAdd() throws IOException {
        // 模拟 UserUtils.getUserName() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getUserName).thenReturn("testUser");
            
            // 模拟 mapper 的 saveFilter 方法返回 true
            when(alarmTypeFilterMapper.saveFilter(any(AlarmTypeFilter.class))).thenReturn(true);
            
            // 执行测试
            boolean result = alarmTypeFilterService.add("测试类型过滤器", "test-type-filter-id", (short) 1, mockAvatarFile);
            
            // 验证结果
            assertTrue(result);
            verify(alarmTypeFilterMapper, times(1)).saveFilter(any(AlarmTypeFilter.class));
        }
    }
    
    /**
     * 测试添加告警类型过滤器 - 过滤器ID为空
     */
    @Test
    void testAddWithEmptyFilterId() {
        // 执行测试并验证异常
        assertThrows(ParamInvalidException.class, () -> {
            alarmTypeFilterService.add("测试类型过滤器", "", (short) 1, mockAvatarFile);
        });
        
        // 验证 mapper 的 saveFilter 方法没有被调用
        verify(alarmTypeFilterMapper, never()).saveFilter(any(AlarmTypeFilter.class));
    }
    
    /**
     * 测试更新告警类型过滤器
     */
    @Test
    void testUpdate() throws IOException {
        // 模拟 UserUtils.getUserName() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getUserName).thenReturn("testUser");
            
            // 模拟 mapper 的 getById 方法返回 mockAlarmTypeFilter
            when(alarmTypeFilterMapper.getById(1)).thenReturn(mockAlarmTypeFilter);
            // 模拟 mapper 的 updateFilterById 方法返回 true
            when(alarmTypeFilterMapper.updateFilterById(any(AlarmTypeFilterDTO.class))).thenReturn(true);
            
            // 执行测试
            boolean result = alarmTypeFilterService.update(1, "测试类型过滤器", "test-type-filter-id", (short) 1,
                    mockAvatarFile);
            
            // 验证结果
            assertTrue(result);
            verify(alarmTypeFilterMapper, times(1)).updateFilterById(any(AlarmTypeFilterDTO.class));
        }
    }
    
    /**
     * 测试删除告警类型过滤器
     */
    @Test
    void testDelete() {
        // 执行测试
        alarmTypeFilterService.delete(1);
        
        // 验证 mapper 的 removeById 方法被调用
        verify(alarmTypeFilterMapper, times(1)).removeById(1);
    }
    
    /**
     * 测试删除告警类型过滤器 - ID为null
     */
    @Test
    void testDeleteWithNullId() {
        // 执行测试
        alarmTypeFilterService.delete(null);
        
        // 验证 mapper 的 removeById 方法没有被调用
        verify(alarmTypeFilterMapper, never()).removeById(any());
    }
    
    /**
     * 测试更新告警类型过滤器状态
     */
    @Test
    void testUpdateStatus() {
        // 模拟 UserUtils.getUserName() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getUserName).thenReturn("testUser");
            
            // 模拟 mapper 的 getById 方法返回 mockAlarmTypeFilter
            when(alarmTypeFilterMapper.getById(1)).thenReturn(mockAlarmTypeFilter);
            // 模拟 mapper 的 updateStatus 方法返回 true
            when(alarmTypeFilterMapper.updateStatus(any(AlarmTypeFilter.class))).thenReturn(true);
            
            // 执行测试
            boolean result = alarmTypeFilterService.updateStatus(1, (short) 0);
            
            // 验证结果
            assertTrue(result);
            verify(alarmTypeFilterMapper, times(1)).getById(1);
            verify(alarmTypeFilterMapper, times(1)).updateStatus(any(AlarmTypeFilter.class));
        }
    }
    
    /**
     * 测试更新告警类型过滤器状态 - 过滤器不存在
     */
    @Test
    void testUpdateStatusNonExistingFilter() {
        // 模拟 mapper 的 getById 方法返回 null
        when(alarmTypeFilterMapper.getById(1)).thenReturn(null);
        
        // 执行测试
        boolean result = alarmTypeFilterService.updateStatus(1, (short) 0);
        
        // 验证结果
        assertFalse(result);
        verify(alarmTypeFilterMapper, times(1)).getById(1);
        verify(alarmTypeFilterMapper, never()).updateStatus(any(AlarmTypeFilter.class));
    }
    
    /**
     * 测试获取告警类型过滤器列表
     */
    @Test
    void testGetAlarmTypeFilterList() {
        // 模拟 mapper 的 getAlarmTypeFilterList 方法返回 mockAlarmTypeFilterList
        when(alarmTypeFilterMapper.getAlarmTypeFilterList()).thenReturn(mockAlarmTypeFilterList);
        
        // 执行测试
        List<AlarmTypeFilter> result = alarmTypeFilterService.getAlarmTypeFilterList();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockAlarmTypeFilter.getId(), result.get(0).getId());
        assertEquals(mockAlarmTypeFilter.getFilterName(), result.get(0).getFilterName());
        verify(alarmTypeFilterMapper, times(1)).getAlarmTypeFilterList();
    }
    
    /**
     * 测试更新告警类型过滤器排序
     */
    @Test
    void testUpdateOrder() {
        // 创建排序映射
        Map<Integer, Integer> sortOrder = new HashMap<>();
        sortOrder.put(1, 0);
        sortOrder.put(2, 1);
        
        // 模拟 mapper 的 updateOrder 方法返回 true
        when(alarmTypeFilterMapper.updateOrder(sortOrder)).thenReturn(true);
        
        // 执行测试
        boolean result = alarmTypeFilterService.updateOrder(sortOrder);
        
        // 验证结果
        assertTrue(result);
        verify(alarmTypeFilterMapper, times(1)).updateOrder(sortOrder);
    }
    
    /**
     * 测试更新告警类型过滤器排序 - 空映射
     */
    @Test
    void testUpdateOrderWithEmptyMap() {
        // 创建空排序映射
        Map<Integer, Integer> sortOrder = new HashMap<>();
        
        // 执行测试
        boolean result = alarmTypeFilterService.updateOrder(sortOrder);
        
        // 验证结果
        assertFalse(result);
        verify(alarmTypeFilterMapper, never()).updateOrder(any());
    }
    
    /**
     * 测试获取重大告警数量
     */
    @Test
    void testGetMajorAlarmCount() {
        // 创建模拟的 AlarmTypeFilterCountVo 列表
        AlarmTypeFilterCountVo mockCountVo = new AlarmTypeFilterCountVo();
        mockCountVo.setId(1);
        mockCountVo.setFilterName("测试类型过滤器");
        mockCountVo.setFilterId("test-type-filter-id");
        List<AlarmTypeFilterCountVo> mockAlarmTypeFilterCountVoList = new ArrayList<>();
        mockAlarmTypeFilterCountVoList.add(mockCountVo);
        
        // 模拟 mapper 的 getEnableAlarmTypeFilterList 方法返回 mockAlarmTypeFilterCountVoList
        when(alarmTypeFilterMapper.getEnableAlarmTypeFilterList()).thenReturn(mockAlarmTypeFilterCountVoList);
        
        // 创建模拟的 AlarmResponse
        AlarmResponse<Map<String, Integer>> mockResponse = new AlarmResponse<>();
        AlarmResponse.Meta meta = new AlarmResponse.Meta();
        meta.setCode("20000");
        meta.setMessage("success");
        mockResponse.setMeta(meta);
        
        Map<String, Integer> alarmCountMap = new HashMap<>();
        alarmCountMap.put("test-type-filter-id", 5);
        mockResponse.setData(alarmCountMap);
        
        // 模拟 alarmService 的 getMajorAlarmCountByFilter 方法返回 mockResponse
        when(alarmService.getMajorAlarmCountByFilter(any(AlarmTypeFilterStatisticDTO.class))).thenReturn(mockResponse);
        
        // 执行测试
        List<AlarmTypeFilterCountVo> result = alarmTypeFilterService.getMajorAlarmCount("2023-01-01", "2023-01-31");
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getId());
        assertEquals("测试类型过滤器", result.get(0).getFilterName());
        assertEquals("test-type-filter-id", result.get(0).getFilterId());
        assertEquals(5, result.get(0).getMajorAlarmCount());
        verify(alarmTypeFilterMapper, times(1)).getEnableAlarmTypeFilterList();
        verify(alarmService, times(1)).getMajorAlarmCountByFilter(any(AlarmTypeFilterStatisticDTO.class));
    }
}
