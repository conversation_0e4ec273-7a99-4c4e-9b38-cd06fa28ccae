/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.AlarmLevelFilterDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmLevelFilter;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.AlarmLevelFilterService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmLevelFilterStatisticVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * AlarmLevelFilterController 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class AlarmLevelFilterControllerTest {
    
    @Mock
    private AlarmLevelFilterService alarmLevelFilterService;
    
    @InjectMocks
    private AlarmLevelFilterController alarmLevelFilterController;
    
    private MockMvc mockMvc;
    
    private ObjectMapper objectMapper;
    
    private AlarmLevelFilter mockAlarmLevelFilter;
    
    private AlarmLevelFilterDTO mockAlarmLevelFilterDTO;
    
    private AlarmLevelFilterStatisticVo mockStatisticVo;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(alarmLevelFilterController).build();
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        
        // 初始化告警等级过滤器实体
        mockAlarmLevelFilter = new AlarmLevelFilter();
        mockAlarmLevelFilter.setId(1L);
        mockAlarmLevelFilter.setFilterName("重大告警过滤器");
        mockAlarmLevelFilter.setFilterId("FILTER001");
        mockAlarmLevelFilter.setEnable((short) 1);
        mockAlarmLevelFilter.setSortOrder(1);
        mockAlarmLevelFilter.setCreateTime(LocalDateTime.now());
        mockAlarmLevelFilter.setUpdateTime(LocalDateTime.now());
        mockAlarmLevelFilter.setCreateBy("admin");
        mockAlarmLevelFilter.setUpdateBy("admin");
        
        // 初始化告警等级过滤器DTO
        mockAlarmLevelFilterDTO = new AlarmLevelFilterDTO();
        mockAlarmLevelFilterDTO.setId(1);
        mockAlarmLevelFilterDTO.setFilterName("重大告警过滤器");
        mockAlarmLevelFilterDTO.setFilterId("FILTER001");
        mockAlarmLevelFilterDTO.setEnable((short) 1);
        mockAlarmLevelFilterDTO.setUpdateTime(LocalDateTime.now());
        mockAlarmLevelFilterDTO.setUpdateBy("admin");
        
        // 初始化统计VO
        mockStatisticVo = new AlarmLevelFilterStatisticVo();
        mockStatisticVo.setFilterId("FILTER001");
        mockStatisticVo.setMajorAlarmCount(10);
        mockStatisticVo.setImportantAlarmCount(20);
        mockStatisticVo.setSecondARYAlarmCount(30);
    }
    
    /**
     * 测试新增告警等级过滤器 - 成功情况
     */
    @Test
    void testAddAlarmLevelFilterSuccess() throws Exception {
        // 模拟服务层返回成功
        when(alarmLevelFilterService.add(anyString(), anyString(), any(Short.class))).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(post("/alarmLevelFilter/add").param("filterName", "重大告警过滤器").param("filterId", "FILTER001")
                .param("enable", "1")).andExpect(status().isOk()).andExpect(content().string("true"));
        
        // 验证服务层方法被调用
        verify(alarmLevelFilterService, times(1)).add("重大告警过滤器", "FILTER001", (short) 1);
    }
    
    /**
     * 测试新增告警等级过滤器 - 失败情况
     */
    @Test
    void testAddAlarmLevelFilterFailure() throws Exception {
        // 模拟服务层返回失败
        when(alarmLevelFilterService.add(anyString(), anyString(), any(Short.class))).thenReturn(false);
        
        // 执行测试
        mockMvc.perform(post("/alarmLevelFilter/add").param("filterName", "重大告警过滤器").param("filterId", "FILTER001")
                .param("enable", "1")).andExpect(status().isOk()).andExpect(content().string("false"));
        
        // 验证服务层方法被调用
        verify(alarmLevelFilterService, times(1)).add("重大告警过滤器", "FILTER001", (short) 1);
    }
    
    /**
     * 测试删除告警等级过滤器 - 成功情况
     */
    @Test
    void testDeleteAlarmLevelFilterSuccess() throws Exception {
        // 模拟服务层删除操作（void方法）
        doNothing().when(alarmLevelFilterService).delete(anyInt());
        
        // 执行测试
        mockMvc.perform(delete("/alarmLevelFilter/del").param("id", "1")).andExpect(status().isOk());
        
        // 验证服务层方法被调用
        verify(alarmLevelFilterService, times(1)).delete(1);
    }
    
    /**
     * 测试查询告警等级过滤器列表 - 成功情况
     */
    @Test
    void testAlarmLevelFilterListSuccess() throws Exception {
        List<AlarmLevelFilter> mockFilterList = Arrays.asList(mockAlarmLevelFilter);
        
        // 模拟服务层返回过滤器列表
        when(alarmLevelFilterService.getAlarmLevelFilterList()).thenReturn(mockFilterList);
        
        // 执行测试
        mockMvc.perform(get("/alarmLevelFilter/list")).andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id").value(1)).andExpect(jsonPath("$[0].filterName").value("重大告警过滤器"))
                .andExpect(jsonPath("$[0].filterId").value("FILTER001")).andExpect(jsonPath("$[0].enable").value(1));
        
        // 验证服务层方法被调用
        verify(alarmLevelFilterService, times(1)).getAlarmLevelFilterList();
    }
    
    /**
     * 测试查询告警等级过滤器列表 - 空列表情况
     */
    @Test
    void testAlarmLevelFilterListEmptyList() throws Exception {
        // 模拟服务层返回空列表
        when(alarmLevelFilterService.getAlarmLevelFilterList()).thenReturn(Arrays.asList());
        
        // 执行测试
        mockMvc.perform(get("/alarmLevelFilter/list")).andExpect(status().isOk()).andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isEmpty());
        
        // 验证服务层方法被调用
        verify(alarmLevelFilterService, times(1)).getAlarmLevelFilterList();
    }
    
    /**
     * 测试修改告警等级过滤器 - 成功情况
     */
    @Test
    void testUpdateAlarmLevelFilterSuccess() throws Exception {
        // 模拟服务层返回成功
        when(alarmLevelFilterService.update(any(AlarmLevelFilterDTO.class))).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(put("/alarmLevelFilter/update").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockAlarmLevelFilterDTO))).andExpect(status().isOk())
                .andExpect(content().string("true"));
        
        // 验证服务层方法被调用
        verify(alarmLevelFilterService, times(1)).update(any(AlarmLevelFilterDTO.class));
    }
    
    /**
     * 测试修改告警等级过滤器 - 失败情况
     */
    @Test
    void testUpdateAlarmLevelFilterFailure() throws Exception {
        // 模拟服务层返回失败
        when(alarmLevelFilterService.update(any(AlarmLevelFilterDTO.class))).thenReturn(false);
        
        // 执行测试
        mockMvc.perform(put("/alarmLevelFilter/update").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockAlarmLevelFilterDTO))).andExpect(status().isOk())
                .andExpect(content().string("false"));
        
        // 验证服务层方法被调用
        verify(alarmLevelFilterService, times(1)).update(any(AlarmLevelFilterDTO.class));
    }
    
    /**
     * 测试启用告警等级过滤器 - 成功情况
     */
    @Test
    void testEnableAlarmLevelFilterSuccess() throws Exception {
        // 模拟服务层返回成功
        when(alarmLevelFilterService.updateStatus(anyInt(), any(Short.class))).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(put("/alarmLevelFilter/enable").param("id", "1").param("enable", "1"))
                .andExpect(status().isOk()).andExpect(content().string("true"));
        
        // 验证服务层方法被调用
        verify(alarmLevelFilterService, times(1)).updateStatus(1, (short) 1);
    }
    
    /**
     * 测试调整顺序 - 成功情况
     */
    @Test
    void testUpdateOrderSuccess() throws Exception {
        Map<Integer, Integer> sortOrder = new HashMap<>();
        sortOrder.put(1, 1);
        sortOrder.put(2, 2);
        
        // 模拟服务层返回成功
        when(alarmLevelFilterService.updateOrder(any(Map.class))).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(put("/alarmLevelFilter/order").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(sortOrder))).andExpect(status().isOk())
                .andExpect(content().string("true"));
        
        // 验证服务层方法被调用
        verify(alarmLevelFilterService, times(1)).updateOrder(any(Map.class));
    }
    
    /**
     * 测试查询告警等级过滤器统计信息 - 成功情况
     */
    @Test
    void testAlarmTypeFilterCountVoSuccess() throws Exception {
        // 模拟服务层返回统计信息
        when(alarmLevelFilterService.getAlarmLevelFilterStatistic(anyString(), anyString(), anyInt()))
                .thenReturn(mockStatisticVo);
        
        // 执行测试
        mockMvc.perform(get("/alarmLevelFilter/statisticAlarmCount").param("startTime", "2025-01-01 00:00:00")
                .param("endTime", "2025-01-31 23:59:59").param("id", "1")).andExpect(status().isOk())
                .andExpect(jsonPath("$.filterId").value("FILTER001")).andExpect(jsonPath("$.majorAlarmCount").value(10))
                .andExpect(jsonPath("$.importantAlarmCount").value(20))
                .andExpect(jsonPath("$.secondARYAlarmCount").value(30));
        
        // 验证服务层方法被调用
        verify(alarmLevelFilterService, times(1)).getAlarmLevelFilterStatistic("2025-01-01 00:00:00",
                "2025-01-31 23:59:59", 1);
    }
    
    /**
     * 测试查询告警等级过滤器统计信息 - 无ID参数情况
     */
    @Test
    void testAlarmTypeFilterCountVoWithoutId() throws Exception {
        // 模拟服务层返回统计信息
        when(alarmLevelFilterService.getAlarmLevelFilterStatistic(anyString(), anyString(), eq(null)))
                .thenReturn(mockStatisticVo);
        
        // 执行测试
        mockMvc.perform(get("/alarmLevelFilter/statisticAlarmCount").param("startTime", "2025-01-01 00:00:00")
                .param("endTime", "2025-01-31 23:59:59")).andExpect(status().isOk());
        
        // 验证服务层方法被调用
        verify(alarmLevelFilterService, times(1)).getAlarmLevelFilterStatistic("2025-01-01 00:00:00",
                "2025-01-31 23:59:59", null);
    }
}
