/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.CockpitFaultSceneDetail;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.MsgAuditTaskNum;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.SchedulerResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.service.SchedulerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * FaultSceneServiceImpl 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class FaultSceneServiceImplTest {
    
    @Mock
    private SchedulerService schedulerService;
    
    @InjectMocks
    private FaultSceneServiceImpl faultSceneService;
    
    private List<CockpitFaultSceneDetail> mockFaultSceneDetailList;
    
    private SchedulerResponse<List<CockpitFaultSceneDetail>> mockSceneListResponse;
    
    private SchedulerResponse<MsgAuditTaskNum> mockAuditTaskResponse;
    
    private MsgAuditTaskNum mockMsgAuditTaskNum;
    
    @BeforeEach
    void setUp() {
        // 初始化故障作战室详情列表
        mockFaultSceneDetailList = new ArrayList<>();
        
        CockpitFaultSceneDetail detail1 = new CockpitFaultSceneDetail();
        detail1.setSceneNo("FS001");
        detail1.setSceneName("测试故障作战室1");
        detail1.setRelatedOrderNo("WO001;WO002;WO003");
        mockFaultSceneDetailList.add(detail1);
        
        CockpitFaultSceneDetail detail2 = new CockpitFaultSceneDetail();
        detail2.setSceneNo("FS002");
        detail2.setSceneName("测试故障作战室2");
        detail2.setRelatedOrderNo("WO004");
        mockFaultSceneDetailList.add(detail2);
        
        CockpitFaultSceneDetail detail3 = new CockpitFaultSceneDetail();
        detail3.setSceneNo("FS003");
        detail3.setSceneName("测试故障作战室3");
        detail3.setRelatedOrderNo("");
        mockFaultSceneDetailList.add(detail3);
        
        // 初始化故障作战室列表响应
        mockSceneListResponse = new SchedulerResponse<>();
        mockSceneListResponse.setData(mockFaultSceneDetailList);
        
        // 初始化消息审批任务数目
        mockMsgAuditTaskNum = new MsgAuditTaskNum();
        mockMsgAuditTaskNum.setTodoTaskNum(3);
        mockMsgAuditTaskNum.setDoneTaskNum(2);
        
        // 初始化消息审批任务响应
        mockAuditTaskResponse = new SchedulerResponse<>();
        mockAuditTaskResponse.setData(mockMsgAuditTaskNum);
    }
    
    /**
     * 测试查询故障作战室列表 - 正常情况
     */
    @Test
    void testQuerySceneListForCockpit() {
        // 模拟 schedulerService 的 querySceneListForCockpit 方法
        when(schedulerService.querySceneListForCockpit()).thenReturn(mockSceneListResponse);
        
        // 执行测试
        List<CockpitFaultSceneDetail> result = faultSceneService.querySceneListForCockpit();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 验证第一个工单号被正确处理（只保留第一个）
        assertEquals("WO001", result.get(0).getRelatedOrderNo());
        
        // 验证第二个工单号（只有一个工单号）
        assertEquals("WO004", result.get(1).getRelatedOrderNo());
        
        // 验证第三个工单号（空字符串）
        assertEquals("", result.get(2).getRelatedOrderNo());
        
        // 验证调用次数
        verify(schedulerService, times(1)).querySceneListForCockpit();
    }
    
    /**
     * 测试查询故障作战室列表 - 空列表
     */
    @Test
    void testQuerySceneListForCockpitWithEmptyList() {
        // 创建空列表响应
        SchedulerResponse<List<CockpitFaultSceneDetail>> emptyResponse = new SchedulerResponse<>();
        emptyResponse.setData(new ArrayList<>());
        
        // 模拟 schedulerService 的 querySceneListForCockpit 方法
        when(schedulerService.querySceneListForCockpit()).thenReturn(emptyResponse);
        
        // 执行测试
        List<CockpitFaultSceneDetail> result = faultSceneService.querySceneListForCockpit();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
        
        // 验证调用次数
        verify(schedulerService, times(1)).querySceneListForCockpit();
    }
    
    /**
     * 测试查询故障作战室列表 - null数据
     */
    @Test
    void testQuerySceneListForCockpitWithNullData() {
        // 创建null数据响应
        SchedulerResponse<List<CockpitFaultSceneDetail>> nullResponse = new SchedulerResponse<>();
        nullResponse.setData(null);
        
        // 模拟 schedulerService 的 querySceneListForCockpit 方法
        when(schedulerService.querySceneListForCockpit()).thenReturn(nullResponse);
        
        // 执行测试
        List<CockpitFaultSceneDetail> result = faultSceneService.querySceneListForCockpit();
        
        // 验证结果
        assertNull(result);
        
        // 验证调用次数
        verify(schedulerService, times(1)).querySceneListForCockpit();
    }
    
    /**
     * 测试查询故障作战室列表 - 工单号为null
     */
    @Test
    void testQuerySceneListForCockpitWithNullOrderNo() {
        
        CockpitFaultSceneDetail detail = new CockpitFaultSceneDetail();
        detail.setSceneNo("FS001");
        detail.setSceneName("测试故障作战室");
        detail.setRelatedOrderNo(null);
        // 创建包含null工单号的详情
        List<CockpitFaultSceneDetail> detailsWithNull = new ArrayList<>();
        detailsWithNull.add(detail);
        
        SchedulerResponse<List<CockpitFaultSceneDetail>> responseWithNull = new SchedulerResponse<>();
        responseWithNull.setData(detailsWithNull);
        
        // 模拟 schedulerService 的 querySceneListForCockpit 方法
        when(schedulerService.querySceneListForCockpit()).thenReturn(responseWithNull);
        
        // 执行测试
        List<CockpitFaultSceneDetail> result = faultSceneService.querySceneListForCockpit();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get(0).getRelatedOrderNo());
        
        // 验证调用次数
        verify(schedulerService, times(1)).querySceneListForCockpit();
    }
    
    /**
     * 测试查询消息审批任务数目 - 正常情况
     */
    @Test
    void testQueryMsgAuditTaskNum() {
        // 模拟 schedulerService 的 queryAuditTaskNum 方法
        when(schedulerService.queryAuditTaskNum()).thenReturn(mockAuditTaskResponse);
        
        // 执行测试
        MsgAuditTaskNum result = faultSceneService.queryMsgAuditTaskNum();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(3), result.getTodoTaskNum());
        assertEquals(Integer.valueOf(2), result.getDoneTaskNum());
        
        // 验证调用次数
        verify(schedulerService, times(1)).queryAuditTaskNum();
    }
    
    /**
     * 测试查询消息审批任务数目 - null数据
     */
    @Test
    void testQueryMsgAuditTaskNumWithNullData() {
        // 创建null数据响应
        SchedulerResponse<MsgAuditTaskNum> nullResponse = new SchedulerResponse<>();
        nullResponse.setData(null);
        
        // 模拟 schedulerService 的 queryAuditTaskNum 方法
        when(schedulerService.queryAuditTaskNum()).thenReturn(nullResponse);
        
        // 执行测试
        MsgAuditTaskNum result = faultSceneService.queryMsgAuditTaskNum();
        
        // 验证结果
        assertNull(result);
        
        // 验证调用次数
        verify(schedulerService, times(1)).queryAuditTaskNum();
    }
}
