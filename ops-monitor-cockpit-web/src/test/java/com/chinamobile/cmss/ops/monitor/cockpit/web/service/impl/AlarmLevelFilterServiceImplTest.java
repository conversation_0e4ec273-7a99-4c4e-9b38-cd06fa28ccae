/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ParamInvalidException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ServerException;
import com.chinamobile.cmss.ops.monitor.cockpit.common.utils.UserUtils;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmLevelFilterStatisticInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.service.AlarmService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.AlarmLevelFilterDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmLevelFilter;
import com.chinamobile.cmss.ops.monitor.cockpit.web.mapper.AlarmLevelFilterMapper;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmLevelFilterStatisticVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AlarmLevelFilterServiceImpl 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class AlarmLevelFilterServiceImplTest {
    
    @Mock
    private AlarmLevelFilterMapper alarmLevelFilterMapper;
    
    @Mock
    private AlarmService alarmService;
    
    @InjectMocks
    private AlarmLevelFilterServiceImpl alarmLevelFilterService;
    
    private AlarmLevelFilter mockAlarmLevelFilter;
    
    private AlarmLevelFilterDTO mockAlarmLevelFilterDTO;
    
    private List<AlarmLevelFilter> mockAlarmLevelFilterList;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockAlarmLevelFilter = new AlarmLevelFilter();
        mockAlarmLevelFilter.setId(1L);
        mockAlarmLevelFilter.setFilterName("测试过滤器");
        mockAlarmLevelFilter.setFilterId("test-filter-id");
        mockAlarmLevelFilter.setEnable((short) 1);
        mockAlarmLevelFilter.setSortOrder(0);
        mockAlarmLevelFilter.setCreateTime(LocalDateTime.now());
        mockAlarmLevelFilter.setCreateBy("testUser");
        
        mockAlarmLevelFilterDTO = new AlarmLevelFilterDTO();
        mockAlarmLevelFilterDTO.setId(1);
        mockAlarmLevelFilterDTO.setFilterName("测试过滤器");
        mockAlarmLevelFilterDTO.setFilterId("test-filter-id");
        mockAlarmLevelFilterDTO.setEnable((short) 1);
        
        mockAlarmLevelFilterList = new ArrayList<>();
        mockAlarmLevelFilterList.add(mockAlarmLevelFilter);
    }
    
    /**
     * 测试添加告警等级过滤器
     */
    @Test
    void testAdd() {
        // 模拟 UserUtils.getUserName() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getUserName).thenReturn("testUser");
            
            // 模拟 mapper 的 saveFilter 方法返回 true
            when(alarmLevelFilterMapper.saveFilter(any(AlarmLevelFilter.class))).thenReturn(true);
            
            // 执行测试
            boolean result = alarmLevelFilterService.add("测试过滤器", "test-filter-id", (short) 1);
            
            // 验证结果
            assertTrue(result);
            verify(alarmLevelFilterMapper, times(1)).saveFilter(any(AlarmLevelFilter.class));
        }
    }
    
    /**
     * 测试添加告警等级过滤器 - 过滤器ID为空
     */
    @Test
    void testAddWithEmptyFilterId() {
        // 执行测试并验证异常
        assertThrows(ParamInvalidException.class, () -> {
            alarmLevelFilterService.add("测试过滤器", "", (short) 1);
        });
        
        // 验证 mapper 的 saveFilter 方法没有被调用
        verify(alarmLevelFilterMapper, never()).saveFilter(any(AlarmLevelFilter.class));
    }
    
    /**
     * 测试更新告警等级过滤器
     */
    @Test
    void testUpdate() {
        // 模拟 UserUtils.getUserName() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getUserName).thenReturn("testUser");
            
            // 模拟 mapper 的 getById 方法返回 mockAlarmLevelFilter
            when(alarmLevelFilterMapper.getById(mockAlarmLevelFilterDTO.getId())).thenReturn(mockAlarmLevelFilter);
            // 模拟 mapper 的 updateFilterById 方法返回 true
            when(alarmLevelFilterMapper.updateFilterById(any(AlarmLevelFilterDTO.class))).thenReturn(true);
            
            // 执行测试
            boolean result = alarmLevelFilterService.update(mockAlarmLevelFilterDTO);
            
            // 验证结果
            assertTrue(result);
            verify(alarmLevelFilterMapper, times(1)).getById(mockAlarmLevelFilterDTO.getId());
            verify(alarmLevelFilterMapper, times(1)).updateFilterById(any(AlarmLevelFilterDTO.class));
        }
    }
    
    /**
     * 测试更新告警等级过滤器 - 过滤器不存在
     */
    @Test
    void testUpdateNonExistingFilter() {
        // 模拟 mapper 的 getById 方法返回 null
        when(alarmLevelFilterMapper.getById(mockAlarmLevelFilterDTO.getId())).thenReturn(null);
        
        // 执行测试
        boolean result = alarmLevelFilterService.update(mockAlarmLevelFilterDTO);
        
        // 验证结果
        assertFalse(result);
        verify(alarmLevelFilterMapper, times(1)).getById(mockAlarmLevelFilterDTO.getId());
        verify(alarmLevelFilterMapper, never()).updateFilterById(any(AlarmLevelFilterDTO.class));
    }
    
    /**
     * 测试删除告警等级过滤器
     */
    @Test
    void testDelete() {
        // 执行测试
        alarmLevelFilterService.delete(1);
        
        // 验证 mapper 的 removeById 方法被调用
        verify(alarmLevelFilterMapper, times(1)).removeById(1);
    }
    
    /**
     * 测试删除告警等级过滤器 - ID为null
     */
    @Test
    void testDeleteWithNullId() {
        // 执行测试
        alarmLevelFilterService.delete(null);
        
        // 验证 mapper 的 removeById 方法没有被调用
        verify(alarmLevelFilterMapper, never()).removeById(any());
    }
    
    /**
     * 测试更新告警等级过滤器状态
     */
    @Test
    void testUpdateStatus() {
        // 模拟 UserUtils.getUserName() 方法
        try (MockedStatic<UserUtils> mockedUserUtils = Mockito.mockStatic(UserUtils.class)) {
            mockedUserUtils.when(UserUtils::getUserName).thenReturn("testUser");
            
            // 模拟 mapper 的 getById 方法返回 mockAlarmLevelFilter
            when(alarmLevelFilterMapper.getById(1)).thenReturn(mockAlarmLevelFilter);
            // 模拟 mapper 的 updateStatus 方法返回 true
            when(alarmLevelFilterMapper.updateStatus(any(AlarmLevelFilter.class))).thenReturn(true);
            
            // 执行测试
            boolean result = alarmLevelFilterService.updateStatus(1, (short) 0);
            
            // 验证结果
            assertTrue(result);
            verify(alarmLevelFilterMapper, times(1)).getById(1);
            verify(alarmLevelFilterMapper, times(1)).updateStatus(any(AlarmLevelFilter.class));
        }
    }
    
    /**
     * 测试更新告警等级过滤器状态 - 过滤器不存在
     */
    @Test
    void testUpdateStatusNonExistingFilter() {
        // 模拟 mapper 的 getById 方法返回 null
        when(alarmLevelFilterMapper.getById(1)).thenReturn(null);
        
        // 执行测试
        boolean result = alarmLevelFilterService.updateStatus(1, (short) 0);
        
        // 验证结果
        assertFalse(result);
        verify(alarmLevelFilterMapper, times(1)).getById(1);
        verify(alarmLevelFilterMapper, never()).updateStatus(any(AlarmLevelFilter.class));
    }
    
    /**
     * 测试获取告警等级过滤器列表
     */
    @Test
    void testGetAlarmLevelFilterList() {
        // 模拟 mapper 的 getAlarmLevelFilterList 方法返回 mockAlarmLevelFilterList
        when(alarmLevelFilterMapper.getAlarmLevelFilterList()).thenReturn(mockAlarmLevelFilterList);
        
        // 执行测试
        List<AlarmLevelFilter> result = alarmLevelFilterService.getAlarmLevelFilterList();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockAlarmLevelFilter.getId(), result.get(0).getId());
        assertEquals(mockAlarmLevelFilter.getFilterName(), result.get(0).getFilterName());
        verify(alarmLevelFilterMapper, times(1)).getAlarmLevelFilterList();
    }
    
    /**
     * 测试更新告警等级过滤器排序
     */
    @Test
    void testUpdateOrder() {
        // 创建排序映射
        Map<Integer, Integer> sortOrder = new HashMap<>();
        sortOrder.put(1, 0);
        sortOrder.put(2, 1);
        
        // 模拟 mapper 的 updateOrder 方法返回 true
        when(alarmLevelFilterMapper.updateOrder(sortOrder)).thenReturn(true);
        
        // 执行测试
        boolean result = alarmLevelFilterService.updateOrder(sortOrder);
        
        // 验证结果
        assertTrue(result);
        verify(alarmLevelFilterMapper, times(1)).updateOrder(sortOrder);
    }
    
    /**
     * 测试更新告警等级过滤器排序 - 空映射
     */
    @Test
    void testUpdateOrderWithEmptyMap() {
        // 创建空排序映射
        Map<Integer, Integer> sortOrder = new HashMap<>();
        
        // 执行测试
        boolean result = alarmLevelFilterService.updateOrder(sortOrder);
        
        // 验证结果
        assertFalse(result);
        verify(alarmLevelFilterMapper, never()).updateOrder(any());
    }
    
    /**
     * 测试获取告警等级过滤器统计信息
     */
    @Test
    void testGetAlarmLevelFilterStatistic() {
        // 模拟 mapper 的 getById 方法返回 mockAlarmLevelFilter
        when(alarmLevelFilterMapper.getById(1)).thenReturn(mockAlarmLevelFilter);
        
        // 创建模拟的 AlarmResponse
        AlarmResponse<AlarmLevelFilterStatisticInfos> mockResponse = new AlarmResponse<>();
        AlarmResponse.Meta meta = new AlarmResponse.Meta();
        meta.setCode("20000");
        meta.setMessage("success");
        mockResponse.setMeta(meta);
        
        AlarmLevelFilterStatisticInfos statisticInfos = new AlarmLevelFilterStatisticInfos();
        statisticInfos.setFilterId("test-filter-id");
        statisticInfos.setMajorAlarmCount(5);
        statisticInfos.setImportantAlarmCount(10);
        mockResponse.setData(statisticInfos);
        
        // 模拟 alarmService 的 getLevelFilterStatistic 方法返回 mockResponse
        when(alarmService.getLevelFilterStatistic("2023-01-01", "2023-01-31", "test-filter-id"))
                .thenReturn(mockResponse);
        
        // 执行测试
        AlarmLevelFilterStatisticVo result = alarmLevelFilterService.getAlarmLevelFilterStatistic("2023-01-01",
                "2023-01-31", 1);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getId());
        assertEquals("测试过滤器", result.getFilterName());
        assertEquals("test-filter-id", result.getFilterId());
        assertEquals(5, result.getMajorAlarmCount());
        assertEquals(10, result.getImportantAlarmCount());
        verify(alarmLevelFilterMapper, times(1)).getById(1);
        verify(alarmService, times(1)).getLevelFilterStatistic("2023-01-01", "2023-01-31", "test-filter-id");
    }
    
    /**
     * 测试获取告警等级过滤器统计信息 - 接口返回错误
     */
    @Test
    void testGetAlarmLevelFilterStatisticWithApiError() {
        // 模拟 mapper 的 getById 方法返回 mockAlarmLevelFilter
        when(alarmLevelFilterMapper.getById(1)).thenReturn(mockAlarmLevelFilter);
        
        // 创建模拟的 AlarmResponse 带错误码
        AlarmResponse<AlarmLevelFilterStatisticInfos> mockResponse = new AlarmResponse<>();
        AlarmResponse.Meta meta = new AlarmResponse.Meta();
        meta.setCode("50000");
        meta.setMessage("error");
        mockResponse.setMeta(meta);
        
        // 模拟 alarmService 的 getLevelFilterStatistic 方法返回 mockResponse
        when(alarmService.getLevelFilterStatistic("2023-01-01", "2023-01-31", "test-filter-id"))
                .thenReturn(mockResponse);
        
        // 执行测试并验证异常
        assertThrows(ServerException.class, () -> {
            alarmLevelFilterService.getAlarmLevelFilterStatistic("2023-01-01", "2023-01-31", 1);
        });
        
        verify(alarmLevelFilterMapper, times(1)).getById(1);
        verify(alarmService, times(1)).getLevelFilterStatistic("2023-01-01", "2023-01-31", "test-filter-id");
    }
}
