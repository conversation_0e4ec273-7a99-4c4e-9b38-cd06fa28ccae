/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.AlarmResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.pojo.FaultScenePageListInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.alarm.service.AlarmService;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto.SearchInstDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.dto.SearchObjectAttributeDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.CmdbResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.SearchInstInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.pojo.SearchObjectAttributeInfos;
import com.chinamobile.cmss.ops.monitor.cockpit.external.cmdb.service.CmdbService;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.dto.DataEngineQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.pojo.DataEngineResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.dataengine.service.DataEngineService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.HoneycombChartListQueryDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.HoneycombChartStatisticsDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.BkOperatorTypeVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.BkRegionVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.ChangeListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.FaultListVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.HoneycombChartOverviewVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * HoneycombChartStatisticsServiceImpl 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public final class HoneycombChartStatisticsServiceImplTest {
    
    // 测试常量
    private static final int TEST_OVERVIEW_TYPE = 66;
    
    private static final int TEST_PAGE_SIZE = 10;
    
    private static final long TEST_ALARM_COUNT = 10L;
    
    private static final long TEST_FAULT_COUNT = 5L;
    
    private static final long TEST_SECURITY_COUNT = 3L;
    
    private static final long TEST_CHANGE_COUNT = 8L;
    
    private static final long TEST_TOTAL_COUNT = 100L;
    
    @Mock
    private CmdbService cmdbService;
    
    @Mock
    private AlarmService alarmService;
    
    @Mock
    private DataEngineService dataEngineService;
    
    @InjectMocks
    private HoneycombChartStatisticsServiceImpl honeycombChartStatisticsService;
    
    private BkOperatorTypeVO mockOperatorTypeVO;
    
    private BkRegionVO mockRegionVO;
    
    private HoneycombChartStatisticsDTO mockStatisticsDTO;
    
    private HoneycombChartListQueryDTO mockListQueryDTO;
    
    private HoneycombChartOverviewVO mockOverviewVO;
    
    private AlarmListVO mockAlarmListVO;
    
    private FaultListVO mockFaultListVO;
    
    private ChangeListVO mockChangeListVO;
    
    private DataEngineResponse<Object> mockDataEngineResponse;
    
    /**
     * 测试前的初始化设置
     */
    @BeforeEach
    void setUp() {
        // 设置配置属性
        ReflectionTestUtils.setField(honeycombChartStatisticsService, "workorderFaultRtTable",
                "3_ods_ccops_work_order_fault_rt");
        ReflectionTestUtils.setField(honeycombChartStatisticsService, "workorderChangeRtFocTable",
                "3_ods_ccops_work_order_change_rt_foc");
        
        // 初始化资源池类型VO
        mockOperatorTypeVO = BkOperatorTypeVO.builder().operatorTypeId("CLOUD").operatorTypeName("云资源池").build();
        
        // 初始化资源池VO
        mockRegionVO = BkRegionVO.builder().regionCode("REGION001").regionName("华东资源池").operationType("CLOUD")
                .province("上海").build();
        
        // 初始化统计DTO
        mockStatisticsDTO = new HoneycombChartStatisticsDTO();
        mockStatisticsDTO.setOperationType("CLOUD");
        mockStatisticsDTO.setOverviewType(Arrays.asList("ALARM", "FAULT"));
        mockStatisticsDTO.setPageSize(TEST_OVERVIEW_TYPE);
        mockStatisticsDTO.setPageNum(1);
        
        // 初始化列表查询DTO
        mockListQueryDTO = new HoneycombChartListQueryDTO();
        mockListQueryDTO.setRegionCode("REGION001");
        mockListQueryDTO.setRegionName("华东资源池");
        mockListQueryDTO.setStartDate("2025-01-01");
        mockListQueryDTO.setEndDate("2025-01-07");
        mockListQueryDTO.setPageSize(TEST_PAGE_SIZE);
        mockListQueryDTO.setPageNum(1);
        
        // 初始化蜂窝图总览VO
        mockOverviewVO = new HoneycombChartOverviewVO();
        mockOverviewVO.setTotal(TEST_ALARM_COUNT);
        
        // 初始化告警列表VO
        mockAlarmListVO = new AlarmListVO();
        mockAlarmListVO.setTotal(TEST_FAULT_COUNT);
        
        // 初始化故障列表VO
        mockFaultListVO = new FaultListVO();
        mockFaultListVO.setTotal(TEST_SECURITY_COUNT);
        
        // 初始化变更列表VO
        mockChangeListVO = new ChangeListVO();
        mockChangeListVO.setTotal(TEST_CHANGE_COUNT);
        
        // 初始化数据引擎响应
        mockDataEngineResponse = new DataEngineResponse<>();
        DataEngineResponse.ResponseData<Object> data = new DataEngineResponse.ResponseData<>();
        data.setTotalRecords(TEST_TOTAL_COUNT);
        data.setList(Arrays.asList());
        mockDataEngineResponse.setData(data);
    }
    
    /**
     * 测试获取资源池类型列表 - 成功情况
     */
    @Test
    void testGetOperationTypeFromCmdbSuccess() {
        // 创建 mock CMDB 响应数据
        SearchObjectAttributeInfos.TypeEnums typeEnum = new SearchObjectAttributeInfos.TypeEnums();
        typeEnum.setId("CLOUD");
        typeEnum.setName("云资源池");
        
        SearchObjectAttributeInfos attributeInfo = new SearchObjectAttributeInfos();
        attributeInfo.setOption(Arrays.asList(typeEnum));
        
        CmdbResponse<List<SearchObjectAttributeInfos>> mockCmdbResponse = new CmdbResponse<>();
        mockCmdbResponse.setResult(true);
        mockCmdbResponse.setData(Arrays.asList(attributeInfo));
        
        // 模拟 CMDB 服务调用
        when(cmdbService.searchObjectAttribute(any(SearchObjectAttributeDTO.class))).thenReturn(mockCmdbResponse);
        
        // 执行测试
        List<BkOperatorTypeVO> result = honeycombChartStatisticsService.getOperationTypeFromCmdb();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("CLOUD", result.get(0).getOperatorTypeId());
        assertEquals("云资源池", result.get(0).getOperatorTypeName());
        
        // 验证 CMDB 服务被调用
        verify(cmdbService, times(1)).searchObjectAttribute(any(SearchObjectAttributeDTO.class));
    }
    
    /**
     * 测试获取资源池类型列表 - 空列表情况
     */
    @Test
    void testGetOperationTypeFromCmdbEmptyList() {
        // 创建空的 mock CMDB 响应数据
        CmdbResponse<List<SearchObjectAttributeInfos>> mockCmdbResponse = new CmdbResponse<>();
        mockCmdbResponse.setResult(true);
        mockCmdbResponse.setData(Arrays.asList());
        
        // 模拟 CMDB 服务调用
        when(cmdbService.searchObjectAttribute(any(SearchObjectAttributeDTO.class))).thenReturn(mockCmdbResponse);
        
        // 执行测试
        List<BkOperatorTypeVO> result = honeycombChartStatisticsService.getOperationTypeFromCmdb();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证 CMDB 服务被调用
        verify(cmdbService, times(1)).searchObjectAttribute(any(SearchObjectAttributeDTO.class));
    }
    
    /**
     * 测试获取资源池列表 - 成功情况（带参数）
     */
    @Test
    void testGetRegionFromCmdbWithOperationTypeSuccess() {
        // 创建 mock CMDB 响应数据
        SearchInstInfos.SearchInstInfo instInfo = new SearchInstInfos.SearchInstInfo();
        instInfo.setBkInstName("华东资源池");
        instInfo.setCode("REGION001");
        instInfo.setProvince("江苏");
        instInfo.setOperationType("CLOUD");
        
        SearchInstInfos searchInstInfos = new SearchInstInfos();
        searchInstInfos.setInfo(Arrays.asList(instInfo));
        
        CmdbResponse<SearchInstInfos> mockCmdbResponse = new CmdbResponse<>();
        mockCmdbResponse.setResult(true);
        mockCmdbResponse.setData(searchInstInfos);
        
        // 模拟 CMDB 服务调用
        when(cmdbService.searchInst(any(SearchInstDTO.class))).thenReturn(mockCmdbResponse);
        
        // 执行测试
        List<BkRegionVO> result = honeycombChartStatisticsService.getRegionFromCmdb("CLOUD");
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("REGION001", result.get(0).getRegionCode());
        assertEquals("华东资源池", result.get(0).getRegionName());
        assertEquals("CLOUD", result.get(0).getOperationType());
        
        // 验证 CMDB 服务被调用
        verify(cmdbService, times(1)).searchInst(any(SearchInstDTO.class));
    }
    
    /**
     * 测试获取资源池列表 - 无参数情况
     */
    @Test
    void testGetRegionFromCmdbWithoutOperationTypeSuccess() {
        // 创建 mock CMDB 响应数据
        SearchInstInfos.SearchInstInfo instInfo = new SearchInstInfos.SearchInstInfo();
        instInfo.setBkInstName("华东资源池");
        instInfo.setCode("REGION001");
        instInfo.setProvince("江苏");
        instInfo.setOperationType("CLOUD");
        
        SearchInstInfos searchInstInfos = new SearchInstInfos();
        searchInstInfos.setInfo(Arrays.asList(instInfo));
        
        CmdbResponse<SearchInstInfos> mockCmdbResponse = new CmdbResponse<>();
        mockCmdbResponse.setResult(true);
        mockCmdbResponse.setData(searchInstInfos);
        
        // 模拟 CMDB 服务调用
        when(cmdbService.searchInst(any(SearchInstDTO.class))).thenReturn(mockCmdbResponse);
        
        // 执行测试
        List<BkRegionVO> result = honeycombChartStatisticsService.getRegionFromCmdb(null);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("REGION001", result.get(0).getRegionCode());
        assertEquals("华东资源池", result.get(0).getRegionName());
        
        // 验证 CMDB 服务被调用
        verify(cmdbService, times(1)).searchInst(any(SearchInstDTO.class));
    }
    
    /**
     * 测试蜂窝图总览 - 空总览类型情况
     */
    @Test
    void testGetHoneycombChartOverviewEmptyOverviewType() {
        // 创建空总览类型的DTO
        HoneycombChartStatisticsDTO emptyTypeDTO = new HoneycombChartStatisticsDTO();
        emptyTypeDTO.setOperationType("CLOUD");
        emptyTypeDTO.setOverviewType(Arrays.asList());
        emptyTypeDTO.setPageSize(66);
        emptyTypeDTO.setPageNum(1);
        
        // 创建 mock CMDB 响应数据
        SearchInstInfos.SearchInstInfo instInfo = new SearchInstInfos.SearchInstInfo();
        instInfo.setBkInstName("华东资源池");
        instInfo.setCode("REGION001");
        instInfo.setProvince("江苏");
        instInfo.setOperationType("CLOUD");
        
        SearchInstInfos searchInstInfos = new SearchInstInfos();
        searchInstInfos.setInfo(Arrays.asList(instInfo));
        
        CmdbResponse<SearchInstInfos> mockCmdbResponse = new CmdbResponse<>();
        mockCmdbResponse.setResult(true);
        mockCmdbResponse.setData(searchInstInfos);
        
        // 模拟 CMDB 服务调用
        when(cmdbService.searchInst(any(SearchInstDTO.class))).thenReturn(mockCmdbResponse);
        
        // 模拟数据引擎服务返回数据
        when(dataEngineService.requestDataEngine(any(DataEngineQueryDTO.class))).thenReturn(mockDataEngineResponse);
        
        // 执行测试
        HoneycombChartOverviewVO result = honeycombChartStatisticsService.getHoneycombChartOverview(emptyTypeDTO);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证服务被调用
        verify(cmdbService, times(1)).searchInst(any(SearchInstDTO.class));
        verify(dataEngineService, atLeastOnce()).requestDataEngine(any(DataEngineQueryDTO.class));
    }
    
    /**
     * 测试告警列表查询 - 成功情况
     */
    @Test
    void testGetAlarmByRegionSuccess() {
        // 模拟数据引擎服务返回数据
        when(dataEngineService.requestDataEngine(any(DataEngineQueryDTO.class))).thenReturn(mockDataEngineResponse);
        
        // 执行测试
        AlarmListVO result = honeycombChartStatisticsService.getAlarmByRegion(mockListQueryDTO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(100L, result.getTotal());
        
        // 验证数据引擎服务被调用
        verify(dataEngineService, times(2)).requestDataEngine(any(DataEngineQueryDTO.class));
    }
    
    /**
     * 测试故障列表查询 - 成功情况
     */
    @Test
    void testGetFaultByRegionSuccess() {
        // 创建 mock 告警服务响应
        AlarmResponse<FaultScenePageListInfos> mockAlarmResponse = new AlarmResponse<>();
        AlarmResponse.Meta meta = new AlarmResponse.Meta();
        meta.setCode("20000");
        mockAlarmResponse.setMeta(meta);
        
        FaultScenePageListInfos faultSceneData = new FaultScenePageListInfos();
        faultSceneData.setList(Arrays.asList());
        mockAlarmResponse.setData(faultSceneData);
        
        // 模拟数据引擎服务返回数据
        when(dataEngineService.requestDataEngine(any(DataEngineQueryDTO.class))).thenReturn(mockDataEngineResponse);
        
        // 模拟告警服务返回故障作战室列表
        when(alarmService.queryFaultSceneList(any())).thenReturn(mockAlarmResponse);
        
        // 执行测试
        FaultListVO result = honeycombChartStatisticsService.getFaultByRegion(mockListQueryDTO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(100L, result.getTotal());
        
        // 验证数据引擎服务被调用
        verify(dataEngineService, times(2)).requestDataEngine(any(DataEngineQueryDTO.class));
    }
    
    /**
     * 测试故障列表查询 - 资源池名称为空情况
     */
    @Test
    void testGetFaultByRegionEmptyRegionName() {
        // 创建资源池名称为空的DTO
        HoneycombChartListQueryDTO emptyRegionDTO = new HoneycombChartListQueryDTO();
        emptyRegionDTO.setRegionCode("REGION001");
        emptyRegionDTO.setRegionName("");
        emptyRegionDTO.setPageSize(10);
        emptyRegionDTO.setPageNum(1);
        
        // 执行测试并期望抛出异常
        assertThrows(Exception.class, () -> {
            honeycombChartStatisticsService.getFaultByRegion(emptyRegionDTO);
        });
        
        // 验证数据引擎服务没有被调用
        verify(dataEngineService, never()).requestDataEngine(any(DataEngineQueryDTO.class));
    }
    
    /**
     * 测试安全攻击告警列表查询 - 成功情况
     */
    @Test
    void testGetSecurityAttacksAlarmByRegionSuccess() {
        // 模拟数据引擎服务返回数据
        when(dataEngineService.requestDataEngine(any(DataEngineQueryDTO.class))).thenReturn(mockDataEngineResponse);
        
        // 执行测试
        AlarmListVO result = honeycombChartStatisticsService.getSecurityAttacksAlarmByRegion(mockListQueryDTO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(100L, result.getTotal());
        
        // 验证数据引擎服务被调用
        verify(dataEngineService, times(2)).requestDataEngine(any(DataEngineQueryDTO.class));
    }
    
    /**
     * 测试变更列表查询 - 成功情况
     */
    @Test
    void testGetChangeByRegionSuccess() {
        // 模拟数据引擎服务返回数据
        when(dataEngineService.requestDataEngine(any(DataEngineQueryDTO.class))).thenReturn(mockDataEngineResponse);
        
        // 执行测试
        ChangeListVO result = honeycombChartStatisticsService.getChangeByRegion(mockListQueryDTO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(100L, result.getTotal());
        
        // 验证数据引擎服务被调用
        verify(dataEngineService, times(2)).requestDataEngine(any(DataEngineQueryDTO.class));
    }
    
    /**
     * 测试变更列表查询 - 资源池名称为空情况
     */
    @Test
    void testGetChangeByRegionEmptyRegionName() {
        // 创建资源池名称为空的DTO
        HoneycombChartListQueryDTO emptyRegionDTO = new HoneycombChartListQueryDTO();
        emptyRegionDTO.setRegionCode("REGION001");
        emptyRegionDTO.setRegionName("");
        emptyRegionDTO.setPageSize(10);
        emptyRegionDTO.setPageNum(1);
        
        // 执行测试并期望抛出异常
        assertThrows(Exception.class, () -> {
            honeycombChartStatisticsService.getChangeByRegion(emptyRegionDTO);
        });
        
        // 验证数据引擎服务没有被调用
        verify(dataEngineService, never()).requestDataEngine(any(DataEngineQueryDTO.class));
    }
    
    /**
     * 测试数据引擎服务异常处理
     */
    @Test
    void testDataEngineServiceException() {
        // 模拟数据引擎服务抛出异常
        when(dataEngineService.requestDataEngine(any(DataEngineQueryDTO.class)))
                .thenThrow(new RuntimeException("数据引擎服务异常"));
        
        // 执行测试并期望抛出异常
        assertThrows(Exception.class, () -> {
            honeycombChartStatisticsService.getAlarmByRegion(mockListQueryDTO);
        });
        
        // 验证数据引擎服务被调用
        verify(dataEngineService, atLeastOnce()).requestDataEngine(any(DataEngineQueryDTO.class));
    }
}
