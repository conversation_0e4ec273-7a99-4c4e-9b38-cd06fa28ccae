/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.CockpitFaultSceneDetail;
import com.chinamobile.cmss.ops.monitor.cockpit.external.scheduler.pojo.MsgAuditTaskNum;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.FaultSceneService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * FaultSceneController 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class FaultSceneControllerTest {
    
    @Mock
    private FaultSceneService faultSceneService;
    
    @InjectMocks
    private FaultSceneController faultSceneController;
    
    private MockMvc mockMvc;
    
    private ObjectMapper objectMapper;
    
    private List<CockpitFaultSceneDetail> mockFaultSceneDetailList;
    
    private MsgAuditTaskNum mockMsgAuditTaskNum;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(faultSceneController).build();
        objectMapper = new ObjectMapper();
        
        // 初始化故障作战室详情列表
        mockFaultSceneDetailList = new ArrayList<>();
        
        CockpitFaultSceneDetail detail1 = new CockpitFaultSceneDetail();
        detail1.setSceneNo("FS001");
        detail1.setSceneName("测试故障作战室1");
        detail1.setRelatedOrderNo("WO001");
        mockFaultSceneDetailList.add(detail1);
        
        CockpitFaultSceneDetail detail2 = new CockpitFaultSceneDetail();
        detail2.setSceneNo("FS002");
        detail2.setSceneName("测试故障作战室2");
        detail2.setRelatedOrderNo("WO002");
        mockFaultSceneDetailList.add(detail2);
        
        // 初始化消息审批任务数目
        mockMsgAuditTaskNum = new MsgAuditTaskNum();
        mockMsgAuditTaskNum.setTodoTaskNum(3);
        mockMsgAuditTaskNum.setDoneTaskNum(2);
    }
    
    /**
     * 测试查询驾驶舱的故障作战室列表 - 正常情况
     */
    @Test
    void testQuerySceneListForCockpit() throws Exception {
        // 模拟 faultSceneService 的 querySceneListForCockpit 方法
        when(faultSceneService.querySceneListForCockpit()).thenReturn(mockFaultSceneDetailList);
        
        // 执行测试
        mockMvc.perform(post("/fault/scene/querySceneListForCockpit").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray()).andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].sceneNo").value("FS001"))
                .andExpect(jsonPath("$[0].sceneName").value("测试故障作战室1"))
                .andExpect(jsonPath("$[0].relatedOrderNo").value("WO001"))
                .andExpect(jsonPath("$[1].sceneNo").value("FS002"))
                .andExpect(jsonPath("$[1].sceneName").value("测试故障作战室2"))
                .andExpect(jsonPath("$[1].relatedOrderNo").value("WO002"));
        
        // 验证调用次数
        verify(faultSceneService, times(1)).querySceneListForCockpit();
    }
    
    /**
     * 测试查询驾驶舱的故障作战室列表 - 空列表
     */
    @Test
    void testQuerySceneListForCockpitWithEmptyList() throws Exception {
        // 模拟 faultSceneService 的 querySceneListForCockpit 方法返回空列表
        when(faultSceneService.querySceneListForCockpit()).thenReturn(new ArrayList<>());
        
        // 执行测试
        mockMvc.perform(post("/fault/scene/querySceneListForCockpit").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray()).andExpect(jsonPath("$.length()").value(0));
        
        // 验证调用次数
        verify(faultSceneService, times(1)).querySceneListForCockpit();
    }
    
    /**
     * 测试查询驾驶舱的故障作战室列表 - null结果
     */
    @Test
    void testQuerySceneListForCockpitWithNullResult() throws Exception {
        // 模拟 faultSceneService 的 querySceneListForCockpit 方法返回null
        when(faultSceneService.querySceneListForCockpit()).thenReturn(null);
        
        // 执行测试
        mockMvc.perform(post("/fault/scene/querySceneListForCockpit").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        
        // 验证调用次数
        verify(faultSceneService, times(1)).querySceneListForCockpit();
    }
    
    /**
     * 测试查询驾驶舱的消息审批任务数目 - 正常情况
     */
    @Test
    void testQueryMsgAuditTaskNum() throws Exception {
        // 模拟 faultSceneService 的 queryMsgAuditTaskNum 方法
        when(faultSceneService.queryMsgAuditTaskNum()).thenReturn(mockMsgAuditTaskNum);
        
        // 执行测试
        mockMvc.perform(get("/fault/scene/queryMsgAuditTaskNum").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.todoTaskNum").value(3)).andExpect(jsonPath("$.doneTaskNum").value(2));
        
        // 验证调用次数
        verify(faultSceneService, times(1)).queryMsgAuditTaskNum();
    }
    
    /**
     * 测试查询驾驶舱的消息审批任务数目 - 零任务数
     */
    @Test
    void testQueryMsgAuditTaskNumWithZeroTasks() throws Exception {
        // 创建零任务数的消息审批任务数目
        MsgAuditTaskNum zeroTaskNum = new MsgAuditTaskNum();
        zeroTaskNum.setTodoTaskNum(0);
        zeroTaskNum.setDoneTaskNum(0);
        
        // 模拟 faultSceneService 的 queryMsgAuditTaskNum 方法
        when(faultSceneService.queryMsgAuditTaskNum()).thenReturn(zeroTaskNum);
        
        // 执行测试
        mockMvc.perform(get("/fault/scene/queryMsgAuditTaskNum").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.todoTaskNum").value(0)).andExpect(jsonPath("$.doneTaskNum").value(0));
        
        // 验证调用次数
        verify(faultSceneService, times(1)).queryMsgAuditTaskNum();
    }
    
    /**
     * 测试查询驾驶舱的消息审批任务数目 - null结果
     */
    @Test
    void testQueryMsgAuditTaskNumWithNullResult() throws Exception {
        // 模拟 faultSceneService 的 queryMsgAuditTaskNum 方法返回null
        when(faultSceneService.queryMsgAuditTaskNum()).thenReturn(null);
        
        // 执行测试
        mockMvc.perform(get("/fault/scene/queryMsgAuditTaskNum").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        
        // 验证调用次数
        verify(faultSceneService, times(1)).queryMsgAuditTaskNum();
    }
    
    /**
     * 测试查询驾驶舱的消息审批任务数目 - 大数值
     */
    @Test
    void testQueryMsgAuditTaskNumWithLargeValue() throws Exception {
        // 创建大数值的消息审批任务数目
        MsgAuditTaskNum largeTaskNum = new MsgAuditTaskNum();
        largeTaskNum.setTodoTaskNum(600000);
        largeTaskNum.setDoneTaskNum(399999);
        
        // 模拟 faultSceneService 的 queryMsgAuditTaskNum 方法
        when(faultSceneService.queryMsgAuditTaskNum()).thenReturn(largeTaskNum);
        
        // 执行测试
        mockMvc.perform(get("/fault/scene/queryMsgAuditTaskNum").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.todoTaskNum").value(600000)).andExpect(jsonPath("$.doneTaskNum").value(399999));
        
        // 验证调用次数
        verify(faultSceneService, times(1)).queryMsgAuditTaskNum();
    }
    
    /**
     * 测试查询驾驶舱的故障作战室列表 - 单个作战室
     */
    @Test
    void testQuerySceneListForCockpitWithSingleScene() throws Exception {
        
        CockpitFaultSceneDetail singleDetail = new CockpitFaultSceneDetail();
        singleDetail.setSceneNo("FS001");
        singleDetail.setSceneName("单个测试故障作战室");
        singleDetail.setRelatedOrderNo("WO001");
        // 创建单个故障作战室详情
        List<CockpitFaultSceneDetail> singleSceneList = new ArrayList<>();
        singleSceneList.add(singleDetail);
        
        // 模拟 faultSceneService 的 querySceneListForCockpit 方法
        when(faultSceneService.querySceneListForCockpit()).thenReturn(singleSceneList);
        
        // 执行测试
        mockMvc.perform(post("/fault/scene/querySceneListForCockpit").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray()).andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].sceneNo").value("FS001"))
                .andExpect(jsonPath("$[0].sceneName").value("单个测试故障作战室"))
                .andExpect(jsonPath("$[0].relatedOrderNo").value("WO001"));
        
        // 验证调用次数
        verify(faultSceneService, times(1)).querySceneListForCockpit();
    }
    
    /**
     * 测试查询驾驶舱的故障作战室列表 - 包含null字段
     */
    @Test
    void testQuerySceneListForCockpitWithNullFields() throws Exception {
        
        CockpitFaultSceneDetail nullFieldsDetail = new CockpitFaultSceneDetail();
        nullFieldsDetail.setSceneNo("FS001");
        nullFieldsDetail.setSceneName(null);
        nullFieldsDetail.setRelatedOrderNo(null);
        // 创建包含null字段的故障作战室详情
        List<CockpitFaultSceneDetail> nullFieldsList = new ArrayList<>();
        nullFieldsList.add(nullFieldsDetail);
        
        // 模拟 faultSceneService 的 querySceneListForCockpit 方法
        when(faultSceneService.querySceneListForCockpit()).thenReturn(nullFieldsList);
        
        // 执行测试
        mockMvc.perform(post("/fault/scene/querySceneListForCockpit").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray()).andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].sceneNo").value("FS001"));
        
        // 验证调用次数
        verify(faultSceneService, times(1)).querySceneListForCockpit();
    }
}
