/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.web.dto.WorkOrderSuperviseDTO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.WorkOrderService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderNumVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderSuperviseResultVO;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.WorkOrderSuperviseVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * WorkOrderController 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class WorkOrderControllerTest {
    
    @Mock
    private WorkOrderService workOrderService;
    
    @InjectMocks
    private WorkOrderController workOrderController;
    
    private MockMvc mockMvc;
    
    private ObjectMapper objectMapper;
    
    private WorkOrderSuperviseVO mockSuperviseVO;
    
    private WorkOrderNumVO mockNumVO;
    
    private WorkOrderSuperviseDTO mockSuperviseDTO;
    
    private WorkOrderSuperviseResultVO mockResultVO;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(workOrderController).build();
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        
        // 初始化工单督办VO
        mockSuperviseVO = WorkOrderSuperviseVO.builder().build();
        
        // 初始化工单数量VO
        mockNumVO = WorkOrderNumVO.builder().workOrderNum(5).build();
        
        // 初始化工单督办DTO
        mockSuperviseDTO = new WorkOrderSuperviseDTO();
        mockSuperviseDTO.setWorkOrderNo("WO202501070001");
        mockSuperviseDTO.setProcessorPhone("13800138000");
        
        // 初始化工单督办结果VO
        mockResultVO = WorkOrderSuperviseResultVO.builder().workOrderNo("WO202501070001").superviseNum(1)
                .lastSuperviseTime(new Date()).build();
    }
    
    /**
     * 测试查询工单督办列表 - 成功情况
     */
    @Test
    void testQuerySuperviseListSuccess() throws Exception {
        // 模拟服务层返回督办列表
        when(workOrderService.querySuperviseList()).thenReturn(mockSuperviseVO);
        
        // 执行测试
        mockMvc.perform(get("/work/order/querySuperviseList")).andExpect(status().isOk());
        
        // 验证服务层方法被调用
        verify(workOrderService, times(1)).querySuperviseList();
    }
    
    /**
     * 测试查询工单督办列表 - 空数据情况
     */
    @Test
    void testQuerySuperviseListEmptyData() throws Exception {
        // 创建空的督办VO
        WorkOrderSuperviseVO emptySuperviseVO = WorkOrderSuperviseVO.builder().build();
        
        // 模拟服务层返回空督办列表
        when(workOrderService.querySuperviseList()).thenReturn(emptySuperviseVO);
        
        // 执行测试
        mockMvc.perform(get("/work/order/querySuperviseList")).andExpect(status().isOk());
        
        // 验证服务层方法被调用
        verify(workOrderService, times(1)).querySuperviseList();
    }
    
    /**
     * 测试查询工单督办列表 - 服务层返回null情况
     */
    @Test
    void testQuerySuperviseListServiceReturnsNull() throws Exception {
        // 模拟服务层返回null
        when(workOrderService.querySuperviseList()).thenReturn(null);
        
        // 执行测试
        mockMvc.perform(get("/work/order/querySuperviseList")).andExpect(status().isOk())
                .andExpect(content().string(""));
        
        // 验证服务层方法被调用
        verify(workOrderService, times(1)).querySuperviseList();
    }
    
    /**
     * 测试查询正在实施的变更数量 - 成功情况
     */
    @Test
    void testQueryOperatingChangeWorkOrderNumSuccess() throws Exception {
        // 模拟服务层返回变更数量
        when(workOrderService.queryOperatingChangeWorkOrderNum()).thenReturn(mockNumVO);
        
        // 执行测试
        mockMvc.perform(get("/work/order/queryOperatingChangeWorkOrderNum")).andExpect(status().isOk())
                .andExpect(jsonPath("$.workOrderNum").value(5));
        
        // 验证服务层方法被调用
        verify(workOrderService, times(1)).queryOperatingChangeWorkOrderNum();
    }
    
    /**
     * 测试查询正在实施的变更数量 - 零数量情况
     */
    @Test
    void testQueryOperatingChangeWorkOrderNumZeroCount() throws Exception {
        // 创建零数量的VO
        WorkOrderNumVO zeroNumVO = WorkOrderNumVO.builder().workOrderNum(0).build();
        
        // 模拟服务层返回零数量
        when(workOrderService.queryOperatingChangeWorkOrderNum()).thenReturn(zeroNumVO);
        
        // 执行测试
        mockMvc.perform(get("/work/order/queryOperatingChangeWorkOrderNum")).andExpect(status().isOk())
                .andExpect(jsonPath("$.workOrderNum").value(0));
        
        // 验证服务层方法被调用
        verify(workOrderService, times(1)).queryOperatingChangeWorkOrderNum();
    }
    
    /**
     * 测试查询正在实施的变更数量 - 服务层返回null情况
     */
    @Test
    void testQueryOperatingChangeWorkOrderNumServiceReturnsNull() throws Exception {
        // 模拟服务层返回null
        when(workOrderService.queryOperatingChangeWorkOrderNum()).thenReturn(null);
        
        // 执行测试
        mockMvc.perform(get("/work/order/queryOperatingChangeWorkOrderNum")).andExpect(status().isOk())
                .andExpect(content().string(""));
        
        // 验证服务层方法被调用
        verify(workOrderService, times(1)).queryOperatingChangeWorkOrderNum();
    }
    
    /**
     * 测试工单批量督办 - 成功情况
     */
    @Test
    void testBatchSuperviseSuccess() throws Exception {
        List<WorkOrderSuperviseDTO> superviseDTOList = Arrays.asList(mockSuperviseDTO);
        List<WorkOrderSuperviseResultVO> resultVOList = Arrays.asList(mockResultVO);
        
        // 模拟服务层返回督办结果
        when(workOrderService.batchSupervise(anyList())).thenReturn(resultVOList);
        
        // 执行测试
        mockMvc.perform(post("/work/order/batchSupervise").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(superviseDTOList))).andExpect(status().isOk())
                .andExpect(jsonPath("$[0].workOrderNo").value("WO202501070001"))
                .andExpect(jsonPath("$[0].superviseNum").value(1));
        
        // 验证服务层方法被调用
        verify(workOrderService, times(1)).batchSupervise(anyList());
    }
    
    /**
     * 测试工单批量督办 - 空列表情况
     */
    @Test
    void testBatchSuperviseEmptyList() throws Exception {
        List<WorkOrderSuperviseDTO> emptyList = Arrays.asList();
        List<WorkOrderSuperviseResultVO> emptyResultList = Arrays.asList();
        
        // 模拟服务层返回空结果
        when(workOrderService.batchSupervise(anyList())).thenReturn(emptyResultList);
        
        // 执行测试
        mockMvc.perform(post("/work/order/batchSupervise").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(emptyList))).andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray()).andExpect(jsonPath("$").isEmpty());
        
        // 验证服务层方法被调用
        verify(workOrderService, times(1)).batchSupervise(anyList());
    }
    
    /**
     * 测试工单批量督办 - 多个工单情况
     */
    @Test
    void testBatchSuperviseMultipleWorkOrders() throws Exception {
        // 创建多个督办DTO
        WorkOrderSuperviseDTO superviseDTO1 = new WorkOrderSuperviseDTO();
        superviseDTO1.setWorkOrderNo("WO202501070001");
        superviseDTO1.setWorkOrderTitle("故障工单标题");
        superviseDTO1.setFaultLevel("高");
        superviseDTO1.setResourcePool("资源池A");
        superviseDTO1.setProcessor("处理人A");
        superviseDTO1.setProcessorPhone("13800138000");
        
        WorkOrderSuperviseDTO superviseDTO2 = new WorkOrderSuperviseDTO();
        superviseDTO2.setWorkOrderNo("WO202501070002");
        superviseDTO2.setWorkOrderTitle("事件工单标题");
        superviseDTO2.setFaultLevel("中");
        superviseDTO2.setResourcePool("资源池B");
        superviseDTO2.setProcessor("处理人B");
        superviseDTO2.setProcessorPhone("13900139000");
        
        List<WorkOrderSuperviseDTO> superviseDTOList = Arrays.asList(superviseDTO1, superviseDTO2);
        
        // 创建多个督办结果VO
        WorkOrderSuperviseResultVO resultVO1 = WorkOrderSuperviseResultVO.builder().workOrderNo("WO202501070001")
                .superviseNum(1).lastSuperviseTime(new Date()).build();
        
        WorkOrderSuperviseResultVO resultVO2 = WorkOrderSuperviseResultVO.builder().workOrderNo("WO202501070002")
                .superviseNum(1).lastSuperviseTime(new Date()).build();
        
        List<WorkOrderSuperviseResultVO> resultVOList = Arrays.asList(resultVO1, resultVO2);
        
        // 模拟服务层返回督办结果
        when(workOrderService.batchSupervise(anyList())).thenReturn(resultVOList);
        
        // 执行测试
        mockMvc.perform(post("/work/order/batchSupervise").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(superviseDTOList))).andExpect(status().isOk())
                .andExpect(jsonPath("$[0].workOrderNo").value("WO202501070001"))
                .andExpect(jsonPath("$[0].superviseNum").value(1))
                .andExpect(jsonPath("$[1].workOrderNo").value("WO202501070002"))
                .andExpect(jsonPath("$[1].superviseNum").value(1));
        
        // 验证服务层方法被调用
        verify(workOrderService, times(1)).batchSupervise(anyList());
    }
    
    /**
     * 测试工单批量督办 - 服务层返回null情况
     */
    @Test
    void testBatchSuperviseServiceReturnsNull() throws Exception {
        List<WorkOrderSuperviseDTO> superviseDTOList = Arrays.asList(mockSuperviseDTO);
        
        // 模拟服务层返回null
        when(workOrderService.batchSupervise(anyList())).thenReturn(null);
        
        // 执行测试
        mockMvc.perform(post("/work/order/batchSupervise").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(superviseDTOList))).andExpect(status().isOk())
                .andExpect(content().string(""));
        
        // 验证服务层方法被调用
        verify(workOrderService, times(1)).batchSupervise(anyList());
    }
    
    /**
     * 测试工单批量督办 - 验证HTTP方法
     */
    @Test
    void testBatchSuperviseHttpMethod() throws Exception {
        List<WorkOrderSuperviseDTO> superviseDTOList = Arrays.asList(mockSuperviseDTO);
        List<WorkOrderSuperviseResultVO> resultVOList = Arrays.asList(mockResultVO);
        
        // 模拟服务层返回督办结果
        when(workOrderService.batchSupervise(anyList())).thenReturn(resultVOList);
        
        // 测试POST方法
        mockMvc.perform(post("/work/order/batchSupervise").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(superviseDTOList))).andExpect(status().isOk());
        
        // 测试GET方法应该返回405 Method Not Allowed
        mockMvc.perform(get("/work/order/batchSupervise")).andExpect(status().isMethodNotAllowed());
        
        // 测试PUT方法应该返回405 Method Not Allowed
        mockMvc.perform(put("/work/order/batchSupervise").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(superviseDTOList))).andExpect(status().isMethodNotAllowed());
        
        // 测试DELETE方法应该返回405 Method Not Allowed
        mockMvc.perform(delete("/work/order/batchSupervise")).andExpect(status().isMethodNotAllowed());
        
        // 验证服务层方法只被POST请求调用了1次
        verify(workOrderService, times(1)).batchSupervise(anyList());
    }
}
