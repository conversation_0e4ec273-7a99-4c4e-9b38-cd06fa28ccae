/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.controller;

import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.AlarmTypeFilter;
import com.chinamobile.cmss.ops.monitor.cockpit.web.service.AlarmTypeFilterService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.AlarmTypeFilterCountVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * AlarmTypeFilterController 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class AlarmTypeFilterControllerTest {
    
    @Mock
    private AlarmTypeFilterService alarmTypeFilterService;
    
    @InjectMocks
    private AlarmTypeFilterController alarmTypeFilterController;
    
    private MockMvc mockMvc;
    
    private ObjectMapper objectMapper;
    
    private AlarmTypeFilter mockAlarmTypeFilter;
    
    private AlarmTypeFilterCountVo mockCountVo;
    
    private MockMultipartFile mockAvatarFile;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(alarmTypeFilterController).build();
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        
        // 初始化告警类型过滤器实体
        mockAlarmTypeFilter = new AlarmTypeFilter();
        mockAlarmTypeFilter.setId(1L);
        mockAlarmTypeFilter.setFilterName("网络告警过滤器");
        mockAlarmTypeFilter.setFilterId("TYPE001");
        mockAlarmTypeFilter.setEnable((short) 1);
        mockAlarmTypeFilter.setSortOrder(1);
        mockAlarmTypeFilter.setCreateTime(LocalDateTime.now());
        mockAlarmTypeFilter.setUpdateTime(LocalDateTime.now());
        mockAlarmTypeFilter.setCreateBy("admin");
        mockAlarmTypeFilter.setUpdateBy("admin");
        mockAlarmTypeFilter.setExtension("png");
        mockAlarmTypeFilter.setPicture("test image".getBytes());
        
        // 初始化统计VO
        mockCountVo = new AlarmTypeFilterCountVo();
        mockCountVo.setFilterId("TYPE001");
        mockCountVo.setFilterName("网络告警过滤器");
        mockCountVo.setMajorAlarmCount(15);
        
        // 初始化模拟文件
        mockAvatarFile = new MockMultipartFile("avatarFile", "test.png", "image/png", "test image content".getBytes());
    }
    
    /**
     * 测试新增告警类型过滤器 - 成功情况
     */
    @Test
    void testAddAlarmTypeFilterSuccess() throws Exception {
        // 模拟服务层返回成功
        when(alarmTypeFilterService.add(anyString(), anyString(), any(Short.class), any())).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(multipart("/alarmTypeFilter/add").file(mockAvatarFile).param("filterName", "网络告警过滤器")
                .param("filterId", "TYPE001").param("enable", "1")).andExpect(status().isOk())
                .andExpect(content().string("true"));
        
        // 验证服务层方法被调用
        verify(alarmTypeFilterService, times(1)).add(eq("网络告警过滤器"), eq("TYPE001"), eq((short) 1), any());
    }
    
    /**
     * 测试新增告警类型过滤器 - 失败情况
     */
    @Test
    void testAddAlarmTypeFilterFailure() throws Exception {
        // 模拟服务层返回失败
        when(alarmTypeFilterService.add(anyString(), anyString(), any(Short.class), any())).thenReturn(false);
        
        // 执行测试
        mockMvc.perform(multipart("/alarmTypeFilter/add").file(mockAvatarFile).param("filterName", "网络告警过滤器")
                .param("filterId", "TYPE001").param("enable", "1")).andExpect(status().isOk())
                .andExpect(content().string("false"));
        
        // 验证服务层方法被调用
        verify(alarmTypeFilterService, times(1)).add(eq("网络告警过滤器"), eq("TYPE001"), eq((short) 1), any());
    }
    
    /**
     * 测试删除告警类型过滤器 - 成功情况
     */
    @Test
    void testDeleteAlarmTypeFilterSuccess() throws Exception {
        // 模拟服务层删除操作（void方法）
        doNothing().when(alarmTypeFilterService).delete(anyInt());
        
        // 执行测试
        mockMvc.perform(delete("/alarmTypeFilter/del").param("id", "1")).andExpect(status().isOk());
        
        // 验证服务层方法被调用
        verify(alarmTypeFilterService, times(1)).delete(1);
    }
    
    /**
     * 测试查询告警类型过滤器列表 - 成功情况
     */
    @Test
    void testAlarmTypeFilterListSuccess() throws Exception {
        List<AlarmTypeFilter> mockFilterList = Arrays.asList(mockAlarmTypeFilter);
        
        // 模拟服务层返回过滤器列表
        when(alarmTypeFilterService.getAlarmTypeFilterList()).thenReturn(mockFilterList);
        
        // 执行测试
        mockMvc.perform(get("/alarmTypeFilter/list")).andExpect(status().isOk()).andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].filterName").value("网络告警过滤器"))
                .andExpect(jsonPath("$[0].filterId").value("TYPE001")).andExpect(jsonPath("$[0].enable").value(1));
        
        // 验证服务层方法被调用
        verify(alarmTypeFilterService, times(1)).getAlarmTypeFilterList();
    }
    
    /**
     * 测试查询告警类型过滤器列表 - 空列表情况
     */
    @Test
    void testAlarmTypeFilterListEmptyList() throws Exception {
        // 模拟服务层返回空列表
        when(alarmTypeFilterService.getAlarmTypeFilterList()).thenReturn(Arrays.asList());
        
        // 执行测试
        mockMvc.perform(get("/alarmTypeFilter/list")).andExpect(status().isOk()).andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isEmpty());
        
        // 验证服务层方法被调用
        verify(alarmTypeFilterService, times(1)).getAlarmTypeFilterList();
    }
    
    /**
     * 测试修改告警类型过滤器 - 成功情况
     */
    @Test
    void testUpdateAlarmTypeFilterSuccess() throws Exception {
        // 模拟服务层返回成功
        when(alarmTypeFilterService.update(anyInt(), anyString(), anyString(), any(Short.class), any()))
                .thenReturn(true);
        
        // 执行测试
        mockMvc.perform(multipart("/alarmTypeFilter/update").file(mockAvatarFile).param("id", "1")
                .param("filterName", "更新的网络告警过滤器").param("filterId", "TYPE001").param("enable", "1").with(request -> {
                    request.setMethod("PUT");
                    return request;
                })).andExpect(status().isOk()).andExpect(content().string("true"));
        
        // 验证服务层方法被调用
        verify(alarmTypeFilterService, times(1)).update(eq(1), eq("更新的网络告警过滤器"), eq("TYPE001"), eq((short) 1), any());
    }
    
    /**
     * 测试修改告警类型过滤器 - 失败情况
     */
    @Test
    void testUpdateAlarmTypeFilterFailure() throws Exception {
        // 模拟服务层返回失败
        when(alarmTypeFilterService.update(anyInt(), anyString(), anyString(), any(Short.class), any()))
                .thenReturn(false);
        
        // 执行测试
        mockMvc.perform(multipart("/alarmTypeFilter/update").file(mockAvatarFile).param("id", "1")
                .param("filterName", "更新的网络告警过滤器").param("filterId", "TYPE001").param("enable", "1").with(request -> {
                    request.setMethod("PUT");
                    return request;
                })).andExpect(status().isOk()).andExpect(content().string("false"));
        
        // 验证服务层方法被调用
        verify(alarmTypeFilterService, times(1)).update(eq(1), eq("更新的网络告警过滤器"), eq("TYPE001"), eq((short) 1), any());
    }
    
    /**
     * 测试启用告警类型过滤器 - 成功情况
     */
    @Test
    void testEnableAlarmTypeFilterSuccess() throws Exception {
        // 模拟服务层返回成功
        when(alarmTypeFilterService.updateStatus(anyInt(), any(Short.class))).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(put("/alarmTypeFilter/enable").param("id", "1").param("enable", "1")).andExpect(status().isOk())
                .andExpect(content().string("true"));
        
        // 验证服务层方法被调用
        verify(alarmTypeFilterService, times(1)).updateStatus(1, (short) 1);
    }
    
    /**
     * 测试调整顺序 - 成功情况
     */
    @Test
    void testUpdateOrderSuccess() throws Exception {
        Map<Integer, Integer> sortOrder = new HashMap<>();
        sortOrder.put(1, 1);
        sortOrder.put(2, 2);
        
        // 模拟服务层返回成功
        when(alarmTypeFilterService.updateOrder(any(Map.class))).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(put("/alarmTypeFilter/order").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(sortOrder))).andExpect(status().isOk())
                .andExpect(content().string("true"));
        
        // 验证服务层方法被调用
        verify(alarmTypeFilterService, times(1)).updateOrder(any(Map.class));
    }
    
    /**
     * 测试查询重大告警数量 - 成功情况
     */
    @Test
    void testAlarmTypeFilterCountVoSuccess() throws Exception {
        List<AlarmTypeFilterCountVo> mockCountList = Arrays.asList(mockCountVo);
        
        // 模拟服务层返回统计信息
        when(alarmTypeFilterService.getMajorAlarmCount(anyString(), anyString())).thenReturn(mockCountList);
        
        // 执行测试
        mockMvc.perform(get("/alarmTypeFilter/majorAlarmCount").param("startTime", "2025-01-01 00:00:00")
                .param("endTime", "2025-01-31 23:59:59")).andExpect(status().isOk())
                .andExpect(jsonPath("$[0].filterId").value("TYPE001"))
                .andExpect(jsonPath("$[0].filterName").value("网络告警过滤器"))
                .andExpect(jsonPath("$[0].majorAlarmCount").value(15));
        
        // 验证服务层方法被调用
        verify(alarmTypeFilterService, times(1)).getMajorAlarmCount("2025-01-01 00:00:00", "2025-01-31 23:59:59");
    }
    
    /**
     * 测试查询重大告警数量 - 空结果情况
     */
    @Test
    void testAlarmTypeFilterCountVoEmptyResult() throws Exception {
        // 模拟服务层返回空列表
        when(alarmTypeFilterService.getMajorAlarmCount(anyString(), anyString())).thenReturn(Arrays.asList());
        
        // 执行测试
        mockMvc.perform(get("/alarmTypeFilter/majorAlarmCount").param("startTime", "2025-01-01 00:00:00")
                .param("endTime", "2025-01-31 23:59:59")).andExpect(status().isOk()).andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isEmpty());
        
        // 验证服务层方法被调用
        verify(alarmTypeFilterService, times(1)).getMajorAlarmCount("2025-01-01 00:00:00", "2025-01-31 23:59:59");
    }
}
