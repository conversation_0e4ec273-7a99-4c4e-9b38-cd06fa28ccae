/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chinamobile.cmss.ops.monitor.cockpit.common.exception.ServerException;
import com.chinamobile.cmss.ops.monitor.cockpit.web.entity.CocWorkOrderSupervise;
import com.chinamobile.cmss.ops.monitor.cockpit.web.mapper.CocWorkOrderSuperviseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * CocWorkOrderSuperviseDAOImpl 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class CocWorkOrderSuperviseDAOImplTest {
    
    @Mock
    private CocWorkOrderSuperviseMapper cocWorkOrderSuperviseMapper;
    
    @InjectMocks
    private CocWorkOrderSuperviseDAOImpl cocWorkOrderSuperviseDAO;
    
    private List<CocWorkOrderSupervise> mockWorkOrderSuperviseList;
    
    private CocWorkOrderSupervise mockWorkOrderSupervise;
    
    @BeforeEach
    void setUp() {
        // 初始化工单督办实体
        mockWorkOrderSupervise = new CocWorkOrderSupervise();
        mockWorkOrderSupervise.setId("1");
        mockWorkOrderSupervise.setWorkOrderNo("WO001");
        mockWorkOrderSupervise.setSuperviseNum(1);
        mockWorkOrderSupervise.setLastSuperviseTime(new Date());
        
        // 初始化工单督办列表
        mockWorkOrderSuperviseList = new ArrayList<>();
        mockWorkOrderSuperviseList.add(mockWorkOrderSupervise);
        
        CocWorkOrderSupervise workOrder2 = new CocWorkOrderSupervise();
        workOrder2.setId("2");
        workOrder2.setWorkOrderNo("WO002");
        workOrder2.setSuperviseNum(2);
        workOrder2.setLastSuperviseTime(new Date());
        mockWorkOrderSuperviseList.add(workOrder2);
    }
    
    /**
     * 测试批量更新工单督办信息 - 正常情况
     */
    @Test
    void testUpdateBatch() {
        // 模拟 mapper 的 updateBatch 方法返回成功更新的记录数
        when(cocWorkOrderSuperviseMapper.updateBatch(mockWorkOrderSuperviseList)).thenReturn(2);
        
        // 执行测试
        int result = cocWorkOrderSuperviseDAO.updateBatch(mockWorkOrderSuperviseList);
        
        // 验证结果
        assertEquals(2, result);
        
        // 验证调用次数
        verify(cocWorkOrderSuperviseMapper, times(1)).updateBatch(mockWorkOrderSuperviseList);
    }
    
    /**
     * 测试批量更新工单督办信息 - 空列表
     */
    @Test
    void testUpdateBatchWithEmptyList() {
        List<CocWorkOrderSupervise> emptyList = new ArrayList<>();
        
        // 模拟 mapper 的 updateBatch 方法返回0
        when(cocWorkOrderSuperviseMapper.updateBatch(emptyList)).thenReturn(0);
        
        // 执行测试
        int result = cocWorkOrderSuperviseDAO.updateBatch(emptyList);
        
        // 验证结果
        assertEquals(0, result);
        
        // 验证调用次数
        verify(cocWorkOrderSuperviseMapper, times(1)).updateBatch(emptyList);
    }
    
    /**
     * 测试批量更新工单督办信息 - 异常情况
     */
    @Test
    void testUpdateBatchWithException() {
        // 模拟 mapper 的 updateBatch 方法抛出异常
        when(cocWorkOrderSuperviseMapper.updateBatch(anyList()))
                .thenThrow(new RuntimeException("Database connection failed"));
        
        // 执行测试并验证异常
        ServerException exception = assertThrows(ServerException.class, () -> {
            cocWorkOrderSuperviseDAO.updateBatch(mockWorkOrderSuperviseList);
        });
        
        // 验证异常信息
        assertEquals("批量更新工单督办信息失败!", exception.getMessage());
        
        // 验证调用次数
        verify(cocWorkOrderSuperviseMapper, times(1)).updateBatch(mockWorkOrderSuperviseList);
    }
    
    /**
     * 测试查询全量工单督办信息 - 正常情况
     */
    @Test
    void testSelectAll() {
        // 创建 spy 对象来模拟 list 方法
        CocWorkOrderSuperviseDAOImpl spyDAO = spy(new CocWorkOrderSuperviseDAOImpl());
        doReturn(mockWorkOrderSuperviseList).when(spyDAO).list(any(QueryWrapper.class));
        
        // 执行测试
        List<CocWorkOrderSupervise> result = spyDAO.selectAll();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("WO001", result.get(0).getWorkOrderNo());
        assertEquals("WO002", result.get(1).getWorkOrderNo());
    }
    
    /**
     * 测试查询全量工单督办信息 - 空结果
     */
    @Test
    void testSelectAllWithEmptyResult() {
        // 创建 spy 对象来模拟 list 方法返回空列表
        CocWorkOrderSuperviseDAOImpl spyDAO = spy(new CocWorkOrderSuperviseDAOImpl());
        doReturn(new ArrayList<>()).when(spyDAO).list(any(QueryWrapper.class));
        
        // 执行测试
        List<CocWorkOrderSupervise> result = spyDAO.selectAll();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
    }
    
    /**
     * 测试查询全量工单督办信息 - 异常情况
     */
    @Test
    void testSelectAllWithException() {
        // 创建 spy 对象来模拟 list 方法抛出异常
        CocWorkOrderSuperviseDAOImpl spyDAO = spy(new CocWorkOrderSuperviseDAOImpl());
        doThrow(new RuntimeException("Database query failed")).when(spyDAO).list(any(QueryWrapper.class));
        
        // 执行测试并验证异常
        ServerException exception = assertThrows(ServerException.class, () -> {
            spyDAO.selectAll();
        });
        
        // 验证异常信息
        assertEquals("查询全量工单督办信息失败!", exception.getMessage());
    }
    
    /**
     * 测试根据工单号列表查询工单督办信息 - 正常情况
     */
    @Test
    void testSelectByWorkOrderNoList() {
        List<String> workOrderNoList = Arrays.asList("WO001", "WO002");
        
        // 创建 spy 对象来模拟 list 方法
        CocWorkOrderSuperviseDAOImpl spyDAO = spy(new CocWorkOrderSuperviseDAOImpl());
        doReturn(mockWorkOrderSuperviseList).when(spyDAO).list(any(QueryWrapper.class));
        
        // 执行测试
        List<CocWorkOrderSupervise> result = spyDAO.selectByWorkOrderNoList(workOrderNoList);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("WO001", result.get(0).getWorkOrderNo());
        assertEquals("WO002", result.get(1).getWorkOrderNo());
    }
    
    /**
     * 测试根据工单号列表查询工单督办信息 - 空工单号列表
     */
    @Test
    void testSelectByWorkOrderNoListWithEmptyList() {
        List<String> emptyWorkOrderNoList = new ArrayList<>();
        
        // 创建 spy 对象来模拟 list 方法返回空列表
        CocWorkOrderSuperviseDAOImpl spyDAO = spy(new CocWorkOrderSuperviseDAOImpl());
        doReturn(new ArrayList<>()).when(spyDAO).list(any(QueryWrapper.class));
        
        // 执行测试
        List<CocWorkOrderSupervise> result = spyDAO.selectByWorkOrderNoList(emptyWorkOrderNoList);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
    }
    
    /**
     * 测试根据工单号列表查询工单督办信息 - 单个工单号
     */
    @Test
    void testSelectByWorkOrderNoListWithSingleWorkOrder() {
        List<String> singleWorkOrderNoList = Arrays.asList("WO001");
        List<CocWorkOrderSupervise> singleResult = Arrays.asList(mockWorkOrderSupervise);
        
        // 创建 spy 对象来模拟 list 方法返回单个工单督办
        CocWorkOrderSuperviseDAOImpl spyDAO = spy(new CocWorkOrderSuperviseDAOImpl());
        doReturn(singleResult).when(spyDAO).list(any(QueryWrapper.class));
        
        // 执行测试
        List<CocWorkOrderSupervise> result = spyDAO.selectByWorkOrderNoList(singleWorkOrderNoList);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("WO001", result.get(0).getWorkOrderNo());
    }
    
    /**
     * 测试根据工单号列表查询工单督办信息 - 异常情况
     */
    @Test
    void testSelectByWorkOrderNoListWithException() {
        List<String> workOrderNoList = Arrays.asList("WO001", "WO002");
        
        // 创建 spy 对象来模拟 list 方法抛出异常
        CocWorkOrderSuperviseDAOImpl spyDAO = spy(new CocWorkOrderSuperviseDAOImpl());
        doThrow(new RuntimeException("Database query failed")).when(spyDAO).list(any(QueryWrapper.class));
        
        // 执行测试并验证异常
        ServerException exception = assertThrows(ServerException.class, () -> {
            spyDAO.selectByWorkOrderNoList(workOrderNoList);
        });
        
        // 验证异常信息
        assertEquals("根据工单号列表查询工单督办信息失败!", exception.getMessage());
    }
    
    /**
     * 测试批量更新工单督办信息 - 部分更新成功
     */
    @Test
    void testUpdateBatchPartialSuccess() {
        // 模拟 mapper 的 updateBatch 方法返回部分成功更新的记录数
        when(cocWorkOrderSuperviseMapper.updateBatch(mockWorkOrderSuperviseList)).thenReturn(1);
        
        // 执行测试
        int result = cocWorkOrderSuperviseDAO.updateBatch(mockWorkOrderSuperviseList);
        
        // 验证结果
        assertEquals(1, result);
        
        // 验证调用次数
        verify(cocWorkOrderSuperviseMapper, times(1)).updateBatch(mockWorkOrderSuperviseList);
    }
}
