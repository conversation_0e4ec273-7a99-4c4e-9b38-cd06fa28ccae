/*
 * ------------------------------------------------------------------------------
 * *****************************************************************************
 * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2025
 *
 * The copyright to the computer program(s) herein is the property of
 * CMSS Co.,Ltd. The programs may be used and/or copied only with written
 * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions
 * stipulated in the agreement/contract under which the program(s) have been
 * supplied.
 * ******************************************************************************
 * -------------------------------------------------------------------------------
 */

package com.chinamobile.cmss.ops.monitor.cockpit.web.service.impl;

import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.pojo.FaultAggregateBoardInfo;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.pojo.InfluenceResponse;
import com.chinamobile.cmss.ops.monitor.cockpit.external.influence.service.InfluenceService;
import com.chinamobile.cmss.ops.monitor.cockpit.web.vo.FaultLevelAggregateVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * FaultInfluenceServiceImpl 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class FaultInfluenceServiceImplTest {
    
    @Mock
    private InfluenceService influenceService;
    
    @InjectMocks
    private FaultInfluenceServiceImpl faultInfluenceService;
    
    private InfluenceResponse<FaultAggregateBoardInfo> mockInfluenceResponse;
    
    private FaultAggregateBoardInfo mockFaultAggregateBoardInfo;
    
    private FaultAggregateBoardInfo.FaultLevelInfo mockFaultLevelInfo;
    
    @BeforeEach
    void setUp() {
        // 初始化故障等级信息
        mockFaultLevelInfo = new FaultAggregateBoardInfo.FaultLevelInfo();
        mockFaultLevelInfo.setGeneralEvent(15);
        mockFaultLevelInfo.setNormalFault(10);
        mockFaultLevelInfo.setSeriousFault(8);
        mockFaultLevelInfo.setMajorFault(5);
        mockFaultLevelInfo.setCriticalFault(2);
        
        // 初始化故障聚合板信息
        mockFaultAggregateBoardInfo = new FaultAggregateBoardInfo();
        mockFaultAggregateBoardInfo.setFaultLevelInfo(mockFaultLevelInfo);
        
        // 初始化影响面响应
        mockInfluenceResponse = new InfluenceResponse<>();
        mockInfluenceResponse.setResult(true);
        mockInfluenceResponse.setData(mockFaultAggregateBoardInfo);
    }
    
    /**
     * 测试查询故障等级统计信息 - 正常情况
     */
    @Test
    void testQueryAggregateFaultLevel() {
        // 模拟 influenceService 的 queryFaultAggregateBoardInfo 方法
        when(influenceService.queryFaultAggregateBoardInfo()).thenReturn(mockInfluenceResponse);
        
        // 执行测试
        FaultLevelAggregateVO result = faultInfluenceService.queryAggregateFaultLevel();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(15, result.getGeneralEvent());
        assertEquals(10, result.getNormalFault());
        assertEquals(8, result.getSeriousFault());
        assertEquals(5, result.getMajorFault());
        assertEquals(2, result.getCriticalFault());
        
        // 验证调用次数
        verify(influenceService, times(1)).queryFaultAggregateBoardInfo();
    }
    
    /**
     * 测试查询故障等级统计信息 - 零值情况
     */
    @Test
    void testQueryAggregateFaultLevelWithZeroValues() {
        // 创建零值的故障等级信息
        FaultAggregateBoardInfo.FaultLevelInfo zeroFaultLevelInfo = new FaultAggregateBoardInfo.FaultLevelInfo();
        zeroFaultLevelInfo.setGeneralEvent(0);
        zeroFaultLevelInfo.setNormalFault(0);
        zeroFaultLevelInfo.setSeriousFault(0);
        zeroFaultLevelInfo.setMajorFault(0);
        zeroFaultLevelInfo.setCriticalFault(0);
        
        FaultAggregateBoardInfo zeroFaultAggregateBoardInfo = new FaultAggregateBoardInfo();
        zeroFaultAggregateBoardInfo.setFaultLevelInfo(zeroFaultLevelInfo);
        
        InfluenceResponse<FaultAggregateBoardInfo> zeroInfluenceResponse = new InfluenceResponse<>();
        zeroInfluenceResponse.setResult(true);
        zeroInfluenceResponse.setData(zeroFaultAggregateBoardInfo);
        
        // 模拟 influenceService 的 queryFaultAggregateBoardInfo 方法
        when(influenceService.queryFaultAggregateBoardInfo()).thenReturn(zeroInfluenceResponse);
        
        // 执行测试
        FaultLevelAggregateVO result = faultInfluenceService.queryAggregateFaultLevel();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getGeneralEvent());
        assertEquals(0, result.getNormalFault());
        assertEquals(0, result.getSeriousFault());
        assertEquals(0, result.getMajorFault());
        assertEquals(0, result.getCriticalFault());
        
        // 验证调用次数
        verify(influenceService, times(1)).queryFaultAggregateBoardInfo();
    }
    
    /**
     * 测试查询故障等级统计信息 - 大数值情况
     */
    @Test
    void testQueryAggregateFaultLevelWithLargeValues() {
        // 创建大数值的故障等级信息
        FaultAggregateBoardInfo.FaultLevelInfo largeFaultLevelInfo = new FaultAggregateBoardInfo.FaultLevelInfo();
        largeFaultLevelInfo.setGeneralEvent(999999);
        largeFaultLevelInfo.setNormalFault(888888);
        largeFaultLevelInfo.setSeriousFault(777777);
        largeFaultLevelInfo.setMajorFault(666666);
        largeFaultLevelInfo.setCriticalFault(555555);
        
        FaultAggregateBoardInfo largeFaultAggregateBoardInfo = new FaultAggregateBoardInfo();
        largeFaultAggregateBoardInfo.setFaultLevelInfo(largeFaultLevelInfo);
        
        InfluenceResponse<FaultAggregateBoardInfo> largeInfluenceResponse = new InfluenceResponse<>();
        largeInfluenceResponse.setResult(true);
        largeInfluenceResponse.setData(largeFaultAggregateBoardInfo);
        
        // 模拟 influenceService 的 queryFaultAggregateBoardInfo 方法
        when(influenceService.queryFaultAggregateBoardInfo()).thenReturn(largeInfluenceResponse);
        
        // 执行测试
        FaultLevelAggregateVO result = faultInfluenceService.queryAggregateFaultLevel();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(999999, result.getGeneralEvent());
        assertEquals(888888, result.getNormalFault());
        assertEquals(777777, result.getSeriousFault());
        assertEquals(666666, result.getMajorFault());
        assertEquals(555555, result.getCriticalFault());
        
        // 验证调用次数
        verify(influenceService, times(1)).queryFaultAggregateBoardInfo();
    }
    
    /**
     * 测试查询故障等级统计信息 - null故障等级信息
     */
    @Test
    void testQueryAggregateFaultLevelWithNullFaultLevelInfo() {
        // 创建null故障等级信息的聚合板信息
        FaultAggregateBoardInfo nullFaultLevelBoardInfo = new FaultAggregateBoardInfo();
        nullFaultLevelBoardInfo.setFaultLevelInfo(null);
        
        InfluenceResponse<FaultAggregateBoardInfo> nullFaultLevelResponse = new InfluenceResponse<>();
        nullFaultLevelResponse.setResult(true);
        nullFaultLevelResponse.setData(nullFaultLevelBoardInfo);
        
        // 模拟 influenceService 的 queryFaultAggregateBoardInfo 方法
        when(influenceService.queryFaultAggregateBoardInfo()).thenReturn(nullFaultLevelResponse);
        
        // 执行测试并期望抛出异常
        try {
            faultInfluenceService.queryAggregateFaultLevel();
        } catch (NullPointerException e) {
            // 预期的异常
        }
        
        // 验证调用次数
        verify(influenceService, times(1)).queryFaultAggregateBoardInfo();
    }
    
    /**
     * 测试查询故障等级统计信息 - null聚合板信息
     */
    @Test
    void testQueryAggregateFaultLevelWithNullBoardInfo() {
        // 创建null聚合板信息的响应
        InfluenceResponse<FaultAggregateBoardInfo> nullBoardInfoResponse = new InfluenceResponse<>();
        nullBoardInfoResponse.setResult(true);
        nullBoardInfoResponse.setData(null);
        
        // 模拟 influenceService 的 queryFaultAggregateBoardInfo 方法
        when(influenceService.queryFaultAggregateBoardInfo()).thenReturn(nullBoardInfoResponse);
        
        // 执行测试并期望抛出异常
        try {
            faultInfluenceService.queryAggregateFaultLevel();
        } catch (NullPointerException e) {
            // 预期的异常
        }
        
        // 验证调用次数
        verify(influenceService, times(1)).queryFaultAggregateBoardInfo();
    }
    
    /**
     * 测试查询故障等级统计信息 - 验证Builder模式
     */
    @Test
    void testQueryAggregateFaultLevelBuilderPattern() {
        // 模拟 influenceService 的 queryFaultAggregateBoardInfo 方法
        when(influenceService.queryFaultAggregateBoardInfo()).thenReturn(mockInfluenceResponse);
        
        // 执行测试
        FaultLevelAggregateVO result = faultInfluenceService.queryAggregateFaultLevel();
        
        // 验证结果对象不为null
        assertNotNull(result);
        
        // 验证Builder模式正确设置了所有字段
        assertEquals(mockFaultLevelInfo.getGeneralEvent(), result.getGeneralEvent());
        assertEquals(mockFaultLevelInfo.getNormalFault(), result.getNormalFault());
        assertEquals(mockFaultLevelInfo.getSeriousFault(), result.getSeriousFault());
        assertEquals(mockFaultLevelInfo.getMajorFault(), result.getMajorFault());
        assertEquals(mockFaultLevelInfo.getCriticalFault(), result.getCriticalFault());
        
        // 验证调用次数
        verify(influenceService, times(1)).queryFaultAggregateBoardInfo();
    }
    
    /**
     * 测试查询故障等级统计信息 - 负数值情况
     */
    @Test
    void testQueryAggregateFaultLevelWithNegativeValues() {
        // 创建负数值的故障等级信息
        FaultAggregateBoardInfo.FaultLevelInfo negativeFaultLevelInfo = new FaultAggregateBoardInfo.FaultLevelInfo();
        negativeFaultLevelInfo.setGeneralEvent(-1);
        negativeFaultLevelInfo.setNormalFault(-2);
        negativeFaultLevelInfo.setSeriousFault(-3);
        negativeFaultLevelInfo.setMajorFault(-4);
        negativeFaultLevelInfo.setCriticalFault(-5);
        
        FaultAggregateBoardInfo negativeFaultAggregateBoardInfo = new FaultAggregateBoardInfo();
        negativeFaultAggregateBoardInfo.setFaultLevelInfo(negativeFaultLevelInfo);
        
        InfluenceResponse<FaultAggregateBoardInfo> negativeInfluenceResponse = new InfluenceResponse<>();
        negativeInfluenceResponse.setResult(true);
        negativeInfluenceResponse.setData(negativeFaultAggregateBoardInfo);
        
        // 模拟 influenceService 的 queryFaultAggregateBoardInfo 方法
        when(influenceService.queryFaultAggregateBoardInfo()).thenReturn(negativeInfluenceResponse);
        
        // 执行测试
        FaultLevelAggregateVO result = faultInfluenceService.queryAggregateFaultLevel();
        
        // 验证结果（即使是负数也应该正确映射）
        assertNotNull(result);
        assertEquals(-1, result.getGeneralEvent());
        assertEquals(-2, result.getNormalFault());
        assertEquals(-3, result.getSeriousFault());
        assertEquals(-4, result.getMajorFault());
        assertEquals(-5, result.getCriticalFault());
        
        // 验证调用次数
        verify(influenceService, times(1)).queryFaultAggregateBoardInfo();
    }
}
